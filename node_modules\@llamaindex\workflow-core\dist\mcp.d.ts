import { Zod<PERSON><PERSON><PERSON>ha<PERSON>, <PERSON>odT<PERSON><PERSON><PERSON>, z } from "zod";
import { Workflow, WorkflowEvent } from "@llamaindex/workflow-core";
import { RequestHandlerExtra } from "@modelcontextprotocol/sdk/shared/protocol.js";
import { CallToolResult } from "@modelcontextprotocol/sdk/types.js";

//#region src/mcp.d.ts
declare const getReqHandlerExtra: () => RequestHandlerExtra<any, any>;
declare function mcpTool<Args extends ZodRawShape, Start extends z.objectOutputType<Args, ZodTypeAny>, Stop extends CallToolResult>(workflow: Workflow, start: WorkflowEvent<Start>, stop: WorkflowEvent<Stop>): (args: Start, extra: RequestHandlerExtra<any, any>) => CallToolResult | Promise<CallToolResult>;
//#endregion
export { getReqHandlerExtra, mcpTool };
//# sourceMappingURL=mcp.d.ts.map