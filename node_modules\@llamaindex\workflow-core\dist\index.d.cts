//#region src/core/event.d.ts
declare const opaqueSymbol: unique symbol;
type InferWorkflowEventData<T> = T extends WorkflowEventData<infer U> ? U : T extends WorkflowEvent<infer U> ? U : never;
type WorkflowEventData<Data, DebugLabel extends string = string> = {
  get data(): Data;
} & {
  readonly [opaqueSymbol]: DebugLabel;
};
type WorkflowEvent<Data, DebugLabel extends string = string> = {
  /**
   * This is the label used for debugging purposes.
   */
  debugLabel?: DebugLabel;
  /**
   * This is the unique identifier for the event, which is used for sharing cross the network boundaries.
   */
  readonly uniqueId: string;
  with(data: Data): WorkflowEventData<Data, DebugLabel>;
  include(event: unknown): event is WorkflowEventData<Data, DebugLabel>;
} & {
  readonly [opaqueSymbol]: DebugLabel;
};
type WorkflowEventConfig<DebugLabel extends string = string> = {
  debugLabel?: DebugLabel;
  uniqueId?: string;
};
declare const workflowEvent: <Data = void, DebugLabel extends string = string>(config?: WorkflowEventConfig<DebugLabel>) => WorkflowEvent<Data, DebugLabel>;
declare const isWorkflowEvent: (instance: unknown) => instance is WorkflowEvent<any>;
declare const isWorkflowEventData: (instance: unknown) => instance is WorkflowEventData<any>;
declare const eventSource: (instance: unknown) => WorkflowEvent<any> | undefined;
//#endregion
//#region src/core/utils.d.ts
type Subscribable<Args extends any[], R> = {
  subscribe: (callback: (...args: Args) => R) => () => void;
  publish: (...args: Args) => unknown[];
};
//#endregion
//#region src/core/stream.d.ts
declare class JsonEncodeTransform extends TransformStream<WorkflowEventData<any>, string> {
  constructor();
}
declare class WorkflowStream<R = any> extends ReadableStream<R> implements AsyncIterable<R> {
  #private;
  on<T>(event: WorkflowEvent<T>, handler: (event: WorkflowEventData<T>) => void): () => void;
  constructor(subscribable: Subscribable<[R], void>, rootStream: ReadableStream<R>);
  constructor(subscribable: Subscribable<[R], void>, rootStream: null);
  constructor(subscribable: null, rootStream: ReadableStream<R> | null);
  static fromReadableStream<T = any>(stream: ReadableStream<WorkflowEventData<any>>): WorkflowStream<T>;
  static fromResponse(response: Response, eventMap: Record<string, WorkflowEvent<any>>): WorkflowStream<WorkflowEventData<any>>;
  toResponse(init?: ResponseInit, transformer?: JsonEncodeTransform): R extends WorkflowEventData<any> ? Response : never;
  get locked(): boolean;
  [Symbol.asyncIterator](): ReadableStreamAsyncIterator<R>;
  cancel(reason?: any): Promise<void>;
  getReader(options: {
    mode: "byob";
  }): ReadableStreamBYOBReader;
  getReader(): ReadableStreamDefaultReader<R>;
  getReader(options?: ReadableStreamGetReaderOptions): ReadableStreamReader<R>;
  pipeThrough<T>(transform: ReadableWritablePair<T, R>, options?: StreamPipeOptions): WorkflowStream<T>;
  pipeTo(destination: WritableStream<R>, options?: StreamPipeOptions): Promise<void>;
  tee(): [WorkflowStream<R>, WorkflowStream<R>];
  forEach(callback: (item: R) => void): Promise<void>;
  map<T>(callback: (item: R) => T): WorkflowStream<T>;
  values(options?: ReadableStreamIteratorOptions): ReadableStreamAsyncIterator<R>;
  take(limit: number): WorkflowStream<R>;
  filter(predicate: R extends WorkflowEventData<any> ? WorkflowEvent<InferWorkflowEventData<R>> : never): WorkflowStream<R>;
  filter(predicate: R): WorkflowStream<R>;
  filter(predicate: (event: R) => boolean): WorkflowStream<R>;
  until(predicate: R extends WorkflowEventData<any> ? WorkflowEvent<InferWorkflowEventData<R>> : never): WorkflowStream<R>;
  until(predicate: (item: R) => boolean): WorkflowStream<R>;
  until(item: R): WorkflowStream<R>;
  toArray(): Promise<R[]>;
}
//#endregion
//#region src/core/context.d.ts
type Handler<AcceptEvents extends WorkflowEvent<any>[], Result extends WorkflowEventData<any> | void> = (...event: { [K in keyof AcceptEvents]: ReturnType<AcceptEvents[K]["with"]> }) => Result | Promise<Result>;
type BaseHandlerContext = {
  abortController: AbortController;
  handler: Handler<WorkflowEvent<any>[], any>;
  inputEvents: WorkflowEvent<any>[];
  inputs: WorkflowEventData<any>[];
  outputs: WorkflowEventData<any>[];
  prev: HandlerContext;
  next: Set<HandlerContext>;
  root: HandlerContext;
};
type SyncHandlerContext = BaseHandlerContext & {
  async: false;
  pending: null;
};
type AsyncHandlerContext = BaseHandlerContext & {
  async: true;
  pending: Promise<WorkflowEventData<any> | void> | null;
};
type HandlerContext = AsyncHandlerContext | SyncHandlerContext;
type WorkflowContext = {
  get stream(): WorkflowStream<WorkflowEventData<any>>;
  get signal(): AbortSignal;
  sendEvent: (...events: WorkflowEventData<any>[]) => void;
  __internal__call_send_event: Subscribable<[event: WorkflowEventData<any>, handlerContext: HandlerContext], void>;
};
declare function getContext(): WorkflowContext;
//#endregion
//#region src/core/workflow.d.ts
type Workflow = {
  handle<const AcceptEvents extends WorkflowEvent<any>[], Result extends ReturnType<WorkflowEvent<any>["with"]> | void>(accept: AcceptEvents, handler: Handler<AcceptEvents, Result>): void;
  createContext(): WorkflowContext;
};
declare const createWorkflow: () => Workflow;
//#endregion
export { Handler, InferWorkflowEventData, Workflow, WorkflowContext, WorkflowEvent, WorkflowEventConfig, WorkflowEventData, WorkflowStream, createWorkflow, eventSource, getContext, isWorkflowEvent, isWorkflowEventData, workflowEvent };
//# sourceMappingURL=index.d.cts.map