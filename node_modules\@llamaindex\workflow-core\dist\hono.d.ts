import { Workflow, WorkflowEvent, WorkflowEventData } from "@llamaindex/workflow-core";
import { Context, Hand<PERSON> } from "hono";

//#region src/hono.d.ts
declare const createHonoHandler: <Start, Stop>(workflow: Workflow, getStart: (c: Context) => WorkflowEventData<Start> | Promise<WorkflowEventData<Start>>, stopEvent: WorkflowEvent<Stop>, wrapStopEvent?: (c: Context, stop: Stop) => Response) => Handler;
//#endregion
export { createHonoHandler };
//# sourceMappingURL=hono.d.ts.map