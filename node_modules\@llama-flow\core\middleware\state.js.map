{"version": 3, "file": "state.js", "names": ["init: (input: Input) => State", "workflow: Workflow", "input: Input", "context"], "sources": ["../src/middleware/state.ts"], "sourcesContent": ["import type {\n  Workflow,\n  Workflow as WorkflowCore,\n  WorkflowContext,\n} from \"@llama-flow/core\";\nimport { getContext } from \"@llama-flow/core\";\n\ntype WithState<State, Input> = Input extends void | undefined\n  ? {\n      <Workflow extends WorkflowCore>(\n        workflow: Workflow,\n      ): Omit<Workflow, \"createContext\"> & {\n        createContext(): ReturnType<Workflow[\"createContext\"]> & {\n          get state(): State;\n        };\n      };\n    }\n  : {\n      <Workflow extends WorkflowCore>(\n        workflow: Workflow,\n      ): Omit<Workflow, \"createContext\"> & {\n        createContext(input: Input): ReturnType<Workflow[\"createContext\"]> & {\n          get state(): State;\n        };\n      };\n    };\n\ntype CreateState<State, Input, Context extends WorkflowContext> = {\n  getContext(): Context & {\n    get state(): State;\n  };\n  withState: WithState<State, Input>;\n};\n\nexport function createStatefulMiddleware<\n  State,\n  Input = void,\n  Context extends WorkflowContext = WorkflowContext,\n>(init: (input: Input) => State): CreateState<State, Input, Context> {\n  return {\n    getContext: getContext as never,\n    withState: ((workflow: Workflow) => {\n      return {\n        ...workflow,\n        createContext: (input: Input) => {\n          const state = init(input);\n          const context = workflow.createContext();\n          context.__internal__call_context.subscribe((_, next) => {\n            // todo: make sure getContext is consistent with `workflow.createContext`\n            const context = getContext();\n            if (!Reflect.has(context, \"state\")) {\n              Object.defineProperty(context, \"state\", {\n                get: () => state,\n              });\n            }\n            next(_);\n          });\n          if (!Reflect.has(context, \"state\")) {\n            Object.defineProperty(context, \"state\", {\n              get: () => state,\n            });\n          }\n          return context as any;\n        },\n      };\n    }) as unknown as WithState<State, Input>,\n  };\n}\n"], "mappings": ";;;AAkCA,SAAgB,yBAIdA,MAAmE;AACnE,QAAO;EACO;EACZ,WAAY,CAACC,aAAuB;AAClC,UAAO;IACL,GAAG;IACH,eAAe,CAACC,UAAiB;KAC/B,MAAM,QAAQ,KAAK,MAAM;KACzB,MAAM,UAAU,SAAS,eAAe;AACxC,aAAQ,yBAAyB,UAAU,CAAC,GAAG,SAAS;MAEtD,MAAMC,YAAU,YAAY;AAC5B,WAAK,QAAQ,IAAIA,WAAS,QAAQ,CAChC,QAAO,eAAeA,WAAS,SAAS,EACtC,KAAK,MAAM,MACZ,EAAC;AAEJ,WAAK,EAAE;KACR,EAAC;AACF,UAAK,QAAQ,IAAI,SAAS,QAAQ,CAChC,QAAO,eAAe,SAAS,SAAS,EACtC,KAAK,MAAM,MACZ,EAAC;AAEJ,YAAO;IACR;GACF;EACF;CACF;AACF"}