{"version": 3, "file": "until.js", "names": ["value: unknown", "stream: WorkflowStream | ReadableStream<WorkflowEventData<any>>", "cond:\n    | ((event: WorkflowEventData<any>) => boolean | Promise<boolean>)\n    | WorkflowEvent<any>", "reader: ReadableStreamDefaultReader<WorkflowEventData<any>> | null"], "sources": ["../src/stream/until.ts"], "sourcesContent": ["import {\n  type WorkflowEvent,\n  type WorkflowEventData,\n  WorkflowStream,\n} from \"@llamaindex/workflow-core\";\n\nconst isWorkflowEvent = (value: unknown): value is WorkflowEvent<any> =>\n  value != null &&\n  typeof value === \"object\" &&\n  \"with\" in value &&\n  \"include\" in value;\n\n/**\n * @deprecated use `stream.until` instead. This will be removed in the next minor version.\n */\nexport function until(\n  stream: WorkflowStream | ReadableStream<WorkflowEventData<any>>,\n  cond: (event: WorkflowEventData<any>) => boolean | Promise<boolean>,\n): WorkflowStream;\nexport function until<Stop>(\n  stream: WorkflowStream | ReadableStream<WorkflowEventData<any>>,\n  cond: WorkflowEvent<Stop>,\n): WorkflowStream;\nexport function until(\n  stream: WorkflowStream | ReadableStream<WorkflowEventData<any>>,\n  cond:\n    | ((event: WorkflowEventData<any>) => boolean | Promise<boolean>)\n    | WorkflowEvent<any>,\n): WorkflowStream<WorkflowEventData<any>> {\n  let reader: ReadableStreamDefaultReader<WorkflowEventData<any>> | null = null;\n  return WorkflowStream.fromReadableStream(\n    new ReadableStream<WorkflowEventData<any>>({\n      start: () => {\n        reader = stream.getReader();\n      },\n      pull: async (controller) => {\n        const { done, value } = await reader!.read();\n        if (value) {\n          controller.enqueue(value);\n        }\n        if (done) {\n          reader!.releaseLock();\n          reader = null;\n          controller.close();\n        } else {\n          if (isWorkflowEvent(cond) && cond.include(value)) {\n            reader!.releaseLock();\n            controller.close();\n          } else if (typeof cond === \"function\" && (await cond(value))) {\n            reader!.releaseLock();\n            controller.close();\n          }\n        }\n      },\n    }),\n  );\n}\n"], "mappings": ";;;AAMA,MAAM,kBAAkB,CAACA,UACvB,SAAS,eACF,UAAU,YACjB,UAAU,SACV,aAAa;AAaf,SAAgB,MACdC,QACAC,MAGwC;CACxC,IAAIC,SAAqE;AACzE,QAAO,eAAe,mBACpB,IAAI,eAAuC;EACzC,OAAO,MAAM;AACX,YAAS,OAAO,WAAW;EAC5B;EACD,MAAM,OAAO,eAAe;GAC1B,MAAM,EAAE,MAAM,OAAO,GAAG,MAAM,OAAQ,MAAM;AAC5C,OAAI,MACF,YAAW,QAAQ,MAAM;AAE3B,OAAI,MAAM;AACR,WAAQ,aAAa;AACrB,aAAS;AACT,eAAW,OAAO;GACnB,WACK,gBAAgB,KAAK,IAAI,KAAK,QAAQ,MAAM,EAAE;AAChD,WAAQ,aAAa;AACrB,eAAW,OAAO;GACnB,kBAAiB,SAAS,cAAe,MAAM,KAAK,MAAM,EAAG;AAC5D,WAAQ,aAAa;AACrB,eAAW,OAAO;GACnB;EAEJ;CACF,GACF;AACF"}