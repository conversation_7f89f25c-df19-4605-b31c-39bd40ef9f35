Object.defineProperty(exports, '__esModule', { value: true });

var A = async (t, r)=>{
    let e = typeof r == "function" ? await r(t) : r;
    if (e) return t.scheme === "bearer" ? `Bearer ${e}` : t.scheme === "basic" ? `Basic ${btoa(e)}` : e;
}, z = (t, r, e)=>{
    typeof e == "string" || e instanceof Blob ? t.append(r, e) : t.append(r, JSON.stringify(e));
}, k = {
    bodySerializer: (t)=>{
        let r = new FormData;
        return Object.entries(t).forEach(([e, a])=>{
            a != null && (Array.isArray(a) ? a.forEach((i)=>z(r, e, i)) : z(r, e, a));
        }), r;
    }
}, R = {
    bodySerializer: (t)=>JSON.stringify(t, (r, e)=>typeof e == "bigint" ? e.toString() : e)
}, U = (t)=>{
    switch(t){
        case "label":
            return ".";
        case "matrix":
            return ";";
        case "simple":
            return ",";
        default:
            return "&";
    }
}, _ = (t)=>{
    switch(t){
        case "form":
            return ",";
        case "pipeDelimited":
            return "|";
        case "spaceDelimited":
            return "%20";
        default:
            return ",";
    }
}, D = (t)=>{
    switch(t){
        case "label":
            return ".";
        case "matrix":
            return ";";
        case "simple":
            return ",";
        default:
            return "&";
    }
}, O = ({ allowReserved: t, explode: r, name: e, style: a, value: i })=>{
    if (!r) {
        let s = (t ? i : i.map((l)=>encodeURIComponent(l))).join(_(a));
        switch(a){
            case "label":
                return `.${s}`;
            case "matrix":
                return `;${e}=${s}`;
            case "simple":
                return s;
            default:
                return `${e}=${s}`;
        }
    }
    let o = U(a), n = i.map((s)=>a === "label" || a === "simple" ? t ? s : encodeURIComponent(s) : y({
            allowReserved: t,
            name: e,
            value: s
        })).join(o);
    return a === "label" || a === "matrix" ? o + n : n;
}, y = ({ allowReserved: t, name: r, value: e })=>{
    if (e == null) return "";
    if (typeof e == "object") throw new Error("Deeply-nested arrays/objects aren\u2019t supported. Provide your own `querySerializer()` to handle these.");
    return `${r}=${t ? e : encodeURIComponent(e)}`;
}, q = ({ allowReserved: t, explode: r, name: e, style: a, value: i })=>{
    if (i instanceof Date) return `${e}=${i.toISOString()}`;
    if (a !== "deepObject" && !r) {
        let s = [];
        Object.entries(i).forEach(([f, u])=>{
            s = [
                ...s,
                f,
                t ? u : encodeURIComponent(u)
            ];
        });
        let l = s.join(",");
        switch(a){
            case "form":
                return `${e}=${l}`;
            case "label":
                return `.${l}`;
            case "matrix":
                return `;${e}=${l}`;
            default:
                return l;
        }
    }
    let o = D(a), n = Object.entries(i).map(([s, l])=>y({
            allowReserved: t,
            name: a === "deepObject" ? `${e}[${s}]` : s,
            value: l
        })).join(o);
    return a === "label" || a === "matrix" ? o + n : n;
};
var H = /\{[^{}]+\}/g, B = ({ path: t, url: r })=>{
    let e = r, a = r.match(H);
    if (a) for (let i of a){
        let o = false, n = i.substring(1, i.length - 1), s = "simple";
        n.endsWith("*") && (o = true, n = n.substring(0, n.length - 1)), n.startsWith(".") ? (n = n.substring(1), s = "label") : n.startsWith(";") && (n = n.substring(1), s = "matrix");
        let l = t[n];
        if (l == null) continue;
        if (Array.isArray(l)) {
            e = e.replace(i, O({
                explode: o,
                name: n,
                style: s,
                value: l
            }));
            continue;
        }
        if (typeof l == "object") {
            e = e.replace(i, q({
                explode: o,
                name: n,
                style: s,
                value: l
            }));
            continue;
        }
        if (s === "matrix") {
            e = e.replace(i, `;${y({
                name: n,
                value: l
            })}`);
            continue;
        }
        let f = encodeURIComponent(s === "label" ? `.${l}` : l);
        e = e.replace(i, f);
    }
    return e;
}, P = ({ allowReserved: t, array: r, object: e } = {})=>(i)=>{
        let o = [];
        if (i && typeof i == "object") for(let n in i){
            let s = i[n];
            if (s != null) if (Array.isArray(s)) {
                let l = O({
                    allowReserved: t,
                    explode: true,
                    name: n,
                    style: "form",
                    value: s,
                    ...r
                });
                l && o.push(l);
            } else if (typeof s == "object") {
                let l = q({
                    allowReserved: t,
                    explode: true,
                    name: n,
                    style: "deepObject",
                    value: s,
                    ...e
                });
                l && o.push(l);
            } else {
                let l = y({
                    allowReserved: t,
                    name: n,
                    value: s
                });
                l && o.push(l);
            }
        }
        return o.join("&");
    }, E = (t)=>{
    if (!t) return "stream";
    let r = t.split(";")[0]?.trim();
    if (r) {
        if (r.startsWith("application/json") || r.endsWith("+json")) return "json";
        if (r === "multipart/form-data") return "formData";
        if ([
            "application/",
            "audio/",
            "image/",
            "video/"
        ].some((e)=>r.startsWith(e))) return "blob";
        if (r.startsWith("text/")) return "text";
    }
}, I = async ({ security: t, ...r })=>{
    for (let e of t){
        let a = await A(e, r.auth);
        if (!a) continue;
        let i = e.name ?? "Authorization";
        switch(e.in){
            case "query":
                r.query || (r.query = {}), r.query[i] = a;
                break;
            case "cookie":
                r.headers.append("Cookie", `${i}=${a}`);
                break;
            case "header":
            default:
                r.headers.set(i, a);
                break;
        }
        return;
    }
}, S = (t)=>W({
        baseUrl: t.baseUrl,
        path: t.path,
        query: t.query,
        querySerializer: typeof t.querySerializer == "function" ? t.querySerializer : P(t.querySerializer),
        url: t.url
    }), W = ({ baseUrl: t, path: r, query: e, querySerializer: a, url: i })=>{
    let o = i.startsWith("/") ? i : `/${i}`, n = (t ?? "") + o;
    r && (n = B({
        path: r,
        url: n
    }));
    let s = e ? a(e) : "";
    return s.startsWith("?") && (s = s.substring(1)), s && (n += `?${s}`), n;
}, C = (t, r)=>{
    let e = {
        ...t,
        ...r
    };
    return e.baseUrl?.endsWith("/") && (e.baseUrl = e.baseUrl.substring(0, e.baseUrl.length - 1)), e.headers = x(t.headers, r.headers), e;
}, x = (...t)=>{
    let r = new Headers;
    for (let e of t){
        if (!e || typeof e != "object") continue;
        let a = e instanceof Headers ? e.entries() : Object.entries(e);
        for (let [i, o] of a)if (o === null) r.delete(i);
        else if (Array.isArray(o)) for (let n of o)r.append(i, n);
        else o !== void 0 && r.set(i, typeof o == "object" ? JSON.stringify(o) : o);
    }
    return r;
}, h = class {
    constructor(){
        this._fns = [];
    }
    clear() {
        this._fns = [];
    }
    exists(r) {
        return this._fns.indexOf(r) !== -1;
    }
    eject(r) {
        let e = this._fns.indexOf(r);
        e !== -1 && (this._fns = [
            ...this._fns.slice(0, e),
            ...this._fns.slice(e + 1)
        ]);
    }
    use(r) {
        this._fns = [
            ...this._fns,
            r
        ];
    }
}, v = ()=>({
        error: new h,
        request: new h,
        response: new h
    }), N = P({
    allowReserved: false,
    array: {
        explode: true,
        style: "form"
    },
    object: {
        explode: true,
        style: "deepObject"
    }
}), Q = {
    "Content-Type": "application/json"
}, w = (t = {})=>({
        ...R,
        headers: Q,
        parseAs: "auto",
        querySerializer: N,
        ...t
    });
var J = (t = {})=>{
    let r = C(w(), t), e = ()=>({
            ...r
        }), a = (n)=>(r = C(r, n), e()), i = v(), o = async (n)=>{
        let s = {
            ...r,
            ...n,
            fetch: n.fetch ?? r.fetch ?? globalThis.fetch,
            headers: x(r.headers, n.headers)
        };
        s.security && await I({
            ...s,
            security: s.security
        }), s.body && s.bodySerializer && (s.body = s.bodySerializer(s.body)), (s.body === void 0 || s.body === "") && s.headers.delete("Content-Type");
        let l = S(s), f = {
            redirect: "follow",
            ...s
        }, u = new Request(l, f);
        for (let p of i.request._fns)u = await p(u, s);
        let T = s.fetch, c = await T(u);
        for (let p of i.response._fns)c = await p(c, u, s);
        let m = {
            request: u,
            response: c
        };
        if (c.ok) {
            if (c.status === 204 || c.headers.get("Content-Length") === "0") return {
                data: {},
                ...m
            };
            let p = (s.parseAs === "auto" ? E(c.headers.get("Content-Type")) : s.parseAs) ?? "json";
            if (p === "stream") return {
                data: c.body,
                ...m
            };
            let b = await c[p]();
            return p === "json" && (s.responseValidator && await s.responseValidator(b), s.responseTransformer && (b = await s.responseTransformer(b))), {
                data: b,
                ...m
            };
        }
        let g = await c.text();
        try {
            g = JSON.parse(g);
        } catch  {}
        let d = g;
        for (let p of i.error._fns)d = await p(g, c, u, s);
        if (d = d || {}, s.throwOnError) throw d;
        return {
            error: d,
            ...m
        };
    };
    return {
        buildUrl: S,
        connect: (n)=>o({
                ...n,
                method: "CONNECT"
            }),
        delete: (n)=>o({
                ...n,
                method: "DELETE"
            }),
        get: (n)=>o({
                ...n,
                method: "GET"
            }),
        getConfig: e,
        head: (n)=>o({
                ...n,
                method: "HEAD"
            }),
        interceptors: i,
        options: (n)=>o({
                ...n,
                method: "OPTIONS"
            }),
        patch: (n)=>o({
                ...n,
                method: "PATCH"
            }),
        post: (n)=>o({
                ...n,
                method: "POST"
            }),
        put: (n)=>o({
                ...n,
                method: "PUT"
            }),
        request: o,
        setConfig: a,
        trace: (n)=>o({
                ...n,
                method: "TRACE"
            })
    };
};

// This file is auto-generated by @hey-api/openapi-ts
const client = J(w());

// This file is auto-generated by @hey-api/openapi-ts
/**
 * List Keys
 * List API Keys for a user.
 */ const listKeysApiV1ApiKeysGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/api-keys",
        ...options
    });
};
/**
 * Generate Key
 * Generate a new API Key.
 */ const generateKeyApiV1ApiKeysPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/api-keys",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Delete Api Key
 * Delete an API Key by ID.
 */ const deleteApiKeyApiV1ApiKeysApiKeyIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/api-keys/{api_key_id}",
        ...options
    });
};
/**
 * Update Existing Api Key
 * Update name of an existing API Key.
 */ const updateExistingApiKeyApiV1ApiKeysApiKeyIdPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/api-keys/{api_key_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Validate Embedding Connection
 * Validate an embedding connection.
 *
 * Args:
 * embedding_config: The embedding configuration to validate.
 * pipeline_id: If provided, the embedding connection will be validated for the pipeline.
 * user: The user to validate the embedding connection for.
 * db: The database session.
 *
 * Returns:
 * A BaseConnectionValidation object indicating the result of the validation.
 */ const validateEmbeddingConnectionApiV1ValidateIntegrationsValidateEmbeddingConnectionPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/validate-integrations/validate-embedding-connection",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Validate Data Source Connection
 * Validate a data source connection.
 */ const validateDataSourceConnectionApiV1ValidateIntegrationsValidateDataSourceConnectionPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/validate-integrations/validate-data-source-connection",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Validate Data Sink Connection
 * Validate a data sink connection.
 */ const validateDataSinkConnectionApiV1ValidateIntegrationsValidateDataSinkConnectionPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/validate-integrations/validate-data-sink-connection",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * List Data Sinks
 * List data sinks for a given project.
 */ const listDataSinksApiV1DataSinksGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/data-sinks",
        ...options
    });
};
/**
 * Create Data Sink
 * Create a new data sink.
 */ const createDataSinkApiV1DataSinksPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/data-sinks",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Upsert Data Sink
 * Upserts a data sink.
 * Updates if a data sink with the same name and project_id already exists. Otherwise, creates a new data sink.
 */ const upsertDataSinkApiV1DataSinksPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/data-sinks",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Delete Data Sink
 * Delete a data sink by ID.
 */ const deleteDataSinkApiV1DataSinksDataSinkIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/data-sinks/{data_sink_id}",
        ...options
    });
};
/**
 * Get Data Sink
 * Get a data sink by ID.
 */ const getDataSinkApiV1DataSinksDataSinkIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/data-sinks/{data_sink_id}",
        ...options
    });
};
/**
 * Update Data Sink
 * Update a data sink by ID.
 */ const updateDataSinkApiV1DataSinksDataSinkIdPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/data-sinks/{data_sink_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * List Data Sources
 * List data sources for a given project.
 * If project_id is not provided, uses the default project.
 */ const listDataSourcesApiV1DataSourcesGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/data-sources",
        ...options
    });
};
/**
 * Create Data Source
 * Create a new data source.
 */ const createDataSourceApiV1DataSourcesPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/data-sources",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Upsert Data Source
 * Upserts a data source.
 * Updates if a data source with the same name and project_id already exists. Otherwise, creates a new data source.
 */ const upsertDataSourceApiV1DataSourcesPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/data-sources",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Delete Data Source
 * Delete a data source by ID.
 */ const deleteDataSourceApiV1DataSourcesDataSourceIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/data-sources/{data_source_id}",
        ...options
    });
};
/**
 * Get Data Source
 * Get a data source by ID.
 */ const getDataSourceApiV1DataSourcesDataSourceIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/data-sources/{data_source_id}",
        ...options
    });
};
/**
 * Update Data Source
 * Update a data source by ID.
 */ const updateDataSourceApiV1DataSourcesDataSourceIdPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/data-sources/{data_source_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * List Embedding Model Configs
 */ const listEmbeddingModelConfigsApiV1EmbeddingModelConfigsGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/embedding-model-configs",
        ...options
    });
};
/**
 * Create a new Embedding Model Configuration
 * Create a new embedding model configuration within a specified project.
 */ const createEmbeddingModelConfigApiV1EmbeddingModelConfigsPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/embedding-model-configs",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Upsert Embedding Model Config
 * Upserts an embedding model config.
 * Updates if an embedding model config with the same name and project_id already exists. Otherwise, creates a new embedding model config.
 */ const upsertEmbeddingModelConfigApiV1EmbeddingModelConfigsPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/embedding-model-configs",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Delete Embedding Model Config
 * Delete an embedding model config by ID.
 */ const deleteEmbeddingModelConfigApiV1EmbeddingModelConfigsEmbeddingModelConfigIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/embedding-model-configs/{embedding_model_config_id}",
        ...options
    });
};
/**
 * Update Embedding Model Config
 * Update an embedding model config by ID.
 */ const updateEmbeddingModelConfigApiV1EmbeddingModelConfigsEmbeddingModelConfigIdPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/embedding-model-configs/{embedding_model_config_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * List Organizations
 * List organizations for a user.
 */ const listOrganizationsApiV1OrganizationsGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations",
        ...options
    });
};
/**
 * Create Organization
 * Create a new organization.
 */ const createOrganizationApiV1OrganizationsPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Upsert Organization
 * Upsert a new organization.
 */ const upsertOrganizationApiV1OrganizationsPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Get Default Organization
 * Get the default organization for the user.
 */ const getDefaultOrganizationApiV1OrganizationsDefaultGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/default",
        ...options
    });
};
/**
 * Set Default Organization
 * Set the default organization for the user.
 */ const setDefaultOrganizationApiV1OrganizationsDefaultPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/default",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Delete Organization
 * Delete an organization by ID.
 */ const deleteOrganizationApiV1OrganizationsOrganizationIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/{organization_id}",
        ...options
    });
};
/**
 * Get Organization
 * Get an organization by ID.
 */ const getOrganizationApiV1OrganizationsOrganizationIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/{organization_id}",
        ...options
    });
};
/**
 * Update Organization
 * Update an existing organization.
 */ const updateOrganizationApiV1OrganizationsOrganizationIdPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/{organization_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Get Organization Usage
 * Get usage for a project
 */ const getOrganizationUsageApiV1OrganizationsOrganizationIdUsageGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/{organization_id}/usage",
        ...options
    });
};
/**
 * List Organization Users
 * Get all users in an organization.
 */ const listOrganizationUsersApiV1OrganizationsOrganizationIdUsersGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/{organization_id}/users",
        ...options
    });
};
/**
 * Add Users To Organization
 * Add a user to an organization.
 */ const addUsersToOrganizationApiV1OrganizationsOrganizationIdUsersPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/{organization_id}/users",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Remove Users From Organization
 * Remove users from an organization.
 */ const removeUsersFromOrganizationApiV1OrganizationsOrganizationIdUsersMemberUserIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/{organization_id}/users/{member_user_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * @deprecated
 * Batch Remove Users From Organization
 * Remove a batch of users from an organization.
 */ const batchRemoveUsersFromOrganizationApiV1OrganizationsOrganizationIdUsersRemovePut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/{organization_id}/users/remove",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * List Roles
 * List all roles in an organization.
 */ const listRolesApiV1OrganizationsOrganizationIdRolesGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/{organization_id}/roles",
        ...options
    });
};
/**
 * Get User Role
 * Get the role of a user in an organization.
 */ const getUserRoleApiV1OrganizationsOrganizationIdUsersRolesGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/{organization_id}/users/roles",
        ...options
    });
};
/**
 * Assign Role To User In Organization
 * Assign a role to a user in an organization.
 */ const assignRoleToUserInOrganizationApiV1OrganizationsOrganizationIdUsersRolesPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/{organization_id}/users/roles",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * List Projects By User
 * List all projects for a user in an organization.
 */ const listProjectsByUserApiV1OrganizationsOrganizationIdUsersUserIdProjectsGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/{organization_id}/users/{user_id}/projects",
        ...options
    });
};
/**
 * Add User To Project
 * Add a user to a project.
 */ const addUserToProjectApiV1OrganizationsOrganizationIdUsersUserIdProjectsPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/{organization_id}/users/{user_id}/projects",
        ...options
    });
};
/**
 * Remove User From Project
 * Remove a user from a project.
 */ const removeUserFromProjectApiV1OrganizationsOrganizationIdUsersUserIdProjectsProjectIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/organizations/{organization_id}/users/{user_id}/projects/{project_id}",
        ...options
    });
};
/**
 * List Projects
 * List projects or get one by name
 */ const listProjectsApiV1ProjectsGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/projects",
        ...options
    });
};
/**
 * Create Project
 * Create a new project.
 */ const createProjectApiV1ProjectsPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/projects",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Upsert Project
 * Upsert a project.
 * Updates if a project with the same name already exists. Otherwise, creates a new project.
 */ const upsertProjectApiV1ProjectsPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/projects",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Delete Project
 * Delete a project by ID.
 */ const deleteProjectApiV1ProjectsProjectIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/projects/{project_id}",
        ...options
    });
};
/**
 * Get Project
 * Get a project by ID.
 */ const getProjectApiV1ProjectsProjectIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/projects/{project_id}",
        ...options
    });
};
/**
 * Update Existing Project
 * Update an existing project.
 */ const updateExistingProjectApiV1ProjectsProjectIdPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/projects/{project_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Get Current Project
 * Get the current project.
 */ const getCurrentProjectApiV1ProjectsCurrentGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/projects/current",
        ...options
    });
};
/**
 * Get Project Usage
 * Get usage for a project
 */ const getProjectUsageApiV1ProjectsProjectIdUsageGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/projects/{project_id}/usage",
        ...options
    });
};
/**
 * Delete File
 * Delete the file from S3.
 */ const deleteFileApiV1FilesIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/files/{id}",
        ...options
    });
};
/**
 * Get File
 * Read File metadata objects.
 */ const getFileApiV1FilesIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/files/{id}",
        ...options
    });
};
/**
 * List Files
 * Read File metadata objects.
 */ const listFilesApiV1FilesGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/files",
        ...options
    });
};
/**
 * Upload File
 * Upload a file to S3.
 */ const uploadFileApiV1FilesPost = (options)=>{
    return (options.client ?? client).post({
        ...k,
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/files",
        ...options,
        headers: {
            "Content-Type": null,
            ...options?.headers
        }
    });
};
/**
 * Generate Presigned Url
 * Create a presigned url for uploading a file.
 */ const generatePresignedUrlApiV1FilesPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/files",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Sync Files
 * Sync Files API against file contents uploaded via S3 presigned urls.
 */ const syncFilesApiV1FilesSyncPut = (options)=>{
    return (options?.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/files/sync",
        ...options
    });
};
/**
 * Upload File From Url
 * Upload a file to the project from a URL.
 *
 * If name is ommitted in the request payload, the file name will be
 * extracted from the response Content-Disposition header if available
 * or otherwise it will be derived from the URL path.
 *
 * If providing the name in the request payload, always suffix the
 * file extension in the name if available.
 */ const uploadFileFromUrlApiV1FilesUploadFromUrlPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/files/upload_from_url",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Read File Content
 * Returns a presigned url to read the file content.
 */ const readFileContentApiV1FilesIdContentGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/files/{id}/content",
        ...options
    });
};
/**
 * List File Page Screenshots
 * List metadata for all screenshots of pages from a file.
 */ const listFilePageScreenshotsApiV1FilesIdPageScreenshotsGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/files/{id}/page_screenshots",
        ...options
    });
};
/**
 * Get File Page Screenshot
 * Get screenshot of a page from a file.
 */ const getFilePageScreenshotApiV1FilesIdPageScreenshotsPageIndexGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/files/{id}/page_screenshots/{page_index}",
        ...options
    });
};
/**
 * List File Pages Figures
 */ const listFilePagesFiguresApiV1FilesIdPageFiguresGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/files/{id}/page-figures",
        ...options
    });
};
/**
 * List File Page Figures
 */ const listFilePageFiguresApiV1FilesIdPageFiguresPageIndexGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/files/{id}/page-figures/{page_index}",
        ...options
    });
};
/**
 * Get File Page Figure
 */ const getFilePageFigureApiV1FilesIdPageFiguresPageIndexFigureNameGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/files/{id}/page-figures/{page_index}/{figure_name}",
        ...options
    });
};
/**
 * Search Pipelines
 * Search for pipelines by various parameters.
 */ const searchPipelinesApiV1PipelinesGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines",
        ...options
    });
};
/**
 * Create Pipeline
 * Create a new pipeline for a project.
 */ const createPipelineApiV1PipelinesPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Upsert Pipeline
 * Upsert a pipeline for a project.
 * Updates if a pipeline with the same name and project_id already exists. Otherwise, creates a new pipeline.
 */ const upsertPipelineApiV1PipelinesPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Delete Pipeline
 * Delete a pipeline by ID.
 */ const deletePipelineApiV1PipelinesPipelineIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}",
        ...options
    });
};
/**
 * Get Pipeline
 * Get a pipeline by ID for a given project.
 */ const getPipelineApiV1PipelinesPipelineIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}",
        ...options
    });
};
/**
 * Update Existing Pipeline
 * Update an existing pipeline for a project.
 */ const updateExistingPipelineApiV1PipelinesPipelineIdPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Get Pipeline Status
 * Get the status of a pipeline by ID.
 */ const getPipelineStatusApiV1PipelinesPipelineIdStatusGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/status",
        ...options
    });
};
/**
 * Sync Pipeline
 * Run ingestion for the pipeline by incrementally updating the data-sink with upstream changes from data-sources & files.
 */ const syncPipelineApiV1PipelinesPipelineIdSyncPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/sync",
        ...options
    });
};
/**
 * Cancel Pipeline Sync
 */ const cancelPipelineSyncApiV1PipelinesPipelineIdSyncCancelPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/sync/cancel",
        ...options
    });
};
/**
 * Force Delete Pipeline
 */ const forceDeletePipelineApiV1PipelinesPipelineIdForceDeletePost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/force-delete",
        ...options
    });
};
/**
 * Copy Pipeline
 * Copy a pipeline by ID.
 */ const copyPipelineApiV1PipelinesPipelineIdCopyPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/copy",
        ...options
    });
};
/**
 * @deprecated
 * List Pipeline Files
 * Get files for a pipeline.
 */ const listPipelineFilesApiV1PipelinesPipelineIdFilesGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/files",
        ...options
    });
};
/**
 * Add Files To Pipeline Api
 * Add files to a pipeline.
 */ const addFilesToPipelineApiApiV1PipelinesPipelineIdFilesPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/files",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * @deprecated
 * List Pipeline Files2
 * Get files for a pipeline.
 */ const listPipelineFiles2ApiV1PipelinesPipelineIdFiles2Get = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/files2",
        ...options
    });
};
/**
 * Get Pipeline File Status Counts
 * Get files for a pipeline.
 */ const getPipelineFileStatusCountsApiV1PipelinesPipelineIdFilesStatusCountsGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/files/status-counts",
        ...options
    });
};
/**
 * Get Pipeline File Status
 * Get status of a file for a pipeline.
 */ const getPipelineFileStatusApiV1PipelinesPipelineIdFilesFileIdStatusGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/files/{file_id}/status",
        ...options
    });
};
/**
 * Delete Pipeline File
 * Delete a file from a pipeline.
 */ const deletePipelineFileApiV1PipelinesPipelineIdFilesFileIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/files/{file_id}",
        ...options
    });
};
/**
 * Update Pipeline File
 * Update a file for a pipeline.
 */ const updatePipelineFileApiV1PipelinesPipelineIdFilesFileIdPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/files/{file_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Delete Pipeline Files Metadata
 * Delete metadata for all files in a pipeline.
 */ const deletePipelineFilesMetadataApiV1PipelinesPipelineIdMetadataDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/metadata",
        ...options
    });
};
/**
 * Import Pipeline Metadata
 * Import metadata for a pipeline.
 */ const importPipelineMetadataApiV1PipelinesPipelineIdMetadataPut = (options)=>{
    return (options.client ?? client).put({
        ...k,
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/metadata",
        ...options,
        headers: {
            "Content-Type": null,
            ...options?.headers
        }
    });
};
/**
 * List Pipeline Data Sources
 * Get data sources for a pipeline.
 */ const listPipelineDataSourcesApiV1PipelinesPipelineIdDataSourcesGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/data-sources",
        ...options
    });
};
/**
 * Add Data Sources To Pipeline
 * Add data sources to a pipeline.
 */ const addDataSourcesToPipelineApiV1PipelinesPipelineIdDataSourcesPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/data-sources",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Delete Pipeline Data Source
 * Delete a data source from a pipeline.
 */ const deletePipelineDataSourceApiV1PipelinesPipelineIdDataSourcesDataSourceIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/data-sources/{data_source_id}",
        ...options
    });
};
/**
 * Update Pipeline Data Source
 * Update the configuration of a data source in a pipeline.
 */ const updatePipelineDataSourceApiV1PipelinesPipelineIdDataSourcesDataSourceIdPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/data-sources/{data_source_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Sync Pipeline Data Source
 * Run ingestion for the pipeline data source by incrementally updating the data-sink with upstream changes from data-source.
 */ const syncPipelineDataSourceApiV1PipelinesPipelineIdDataSourcesDataSourceIdSyncPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/data-sources/{data_source_id}/sync",
        ...options
    });
};
/**
 * Get Pipeline Data Source Status
 * Get the status of a data source for a pipeline.
 */ const getPipelineDataSourceStatusApiV1PipelinesPipelineIdDataSourcesDataSourceIdStatusGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/data-sources/{data_source_id}/status",
        ...options
    });
};
/**
 * Run Search
 * Get retrieval results for a managed pipeline and a query
 */ const runSearchApiV1PipelinesPipelineIdRetrievePost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/retrieve",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * List Pipeline Jobs
 * Get jobs for a pipeline.
 */ const listPipelineJobsApiV1PipelinesPipelineIdJobsGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/jobs",
        ...options
    });
};
/**
 * Get Pipeline Job
 * Get a job for a pipeline.
 */ const getPipelineJobApiV1PipelinesPipelineIdJobsJobIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/jobs/{job_id}",
        ...options
    });
};
/**
 * Get Playground Session
 * Get a playground session for a user and pipeline.
 */ const getPlaygroundSessionApiV1PipelinesPipelineIdPlaygroundSessionGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/playground-session",
        ...options
    });
};
/**
 * Chat
 * Make a retrieval query + chat completion for a managed pipeline.
 */ const chatApiV1PipelinesPipelineIdChatPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/chat",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * List Pipeline Documents
 * Return a list of documents for a pipeline.
 */ const listPipelineDocumentsApiV1PipelinesPipelineIdDocumentsGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/documents",
        ...options
    });
};
/**
 * Create Batch Pipeline Documents
 * Batch create documents for a pipeline.
 */ const createBatchPipelineDocumentsApiV1PipelinesPipelineIdDocumentsPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/documents",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Upsert Batch Pipeline Documents
 * Batch create or update a document for a pipeline.
 */ const upsertBatchPipelineDocumentsApiV1PipelinesPipelineIdDocumentsPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/documents",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Paginated List Pipeline Documents
 * Return a list of documents for a pipeline.
 */ const paginatedListPipelineDocumentsApiV1PipelinesPipelineIdDocumentsPaginatedGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/documents/paginated",
        ...options
    });
};
/**
 * Delete Pipeline Document
 * Delete a document from a pipeline.
 * Initiates an async job that will:
 * 1. Delete vectors from the vector store
 * 2. Delete the document from MongoDB after vectors are successfully deleted
 */ const deletePipelineDocumentApiV1PipelinesPipelineIdDocumentsDocumentIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/documents/{document_id}",
        ...options
    });
};
/**
 * Get Pipeline Document
 * Return a single document for a pipeline.
 */ const getPipelineDocumentApiV1PipelinesPipelineIdDocumentsDocumentIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/documents/{document_id}",
        ...options
    });
};
/**
 * Get Pipeline Document Status
 * Return a single document for a pipeline.
 */ const getPipelineDocumentStatusApiV1PipelinesPipelineIdDocumentsDocumentIdStatusGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/documents/{document_id}/status",
        ...options
    });
};
/**
 * List Pipeline Document Chunks
 * Return a list of chunks for a pipeline document.
 */ const listPipelineDocumentChunksApiV1PipelinesPipelineIdDocumentsDocumentIdChunksGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/pipelines/{pipeline_id}/documents/{document_id}/chunks",
        ...options
    });
};
/**
 * List Retrievers
 * List Retrievers for a project.
 */ const listRetrieversApiV1RetrieversGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/retrievers",
        ...options
    });
};
/**
 * Create Retriever
 * Create a new Retriever.
 */ const createRetrieverApiV1RetrieversPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/retrievers",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Upsert Retriever
 * Upsert a new Retriever.
 */ const upsertRetrieverApiV1RetrieversPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/retrievers",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Delete Retriever
 * Delete a Retriever by ID.
 */ const deleteRetrieverApiV1RetrieversRetrieverIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/retrievers/{retriever_id}",
        ...options
    });
};
/**
 * Get Retriever
 * Get a Retriever by ID.
 */ const getRetrieverApiV1RetrieversRetrieverIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/retrievers/{retriever_id}",
        ...options
    });
};
/**
 * Update Retriever
 * Update an existing Retriever.
 */ const updateRetrieverApiV1RetrieversRetrieverIdPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/retrievers/{retriever_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Retrieve
 * Retrieve data using a Retriever.
 */ const retrieveApiV1RetrieversRetrieverIdRetrievePost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/retrievers/{retriever_id}/retrieve",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Direct Retrieve
 * Retrieve data using specified pipelines without creating a persistent retriever.
 */ const directRetrieveApiV1RetrieversRetrievePost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/retrievers/retrieve",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Get Jobs
 * Get jobs for a project.
 *
 * Note:
 * The include_usage_metrics parameter is deprecated and will be removed in a future version.
 * We've moved to usage v2 and this parameter will no longer return meaningful data.
 */ const getJobsApiV1JobsGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/jobs/",
        ...options
    });
};
/**
 * List Supported Models
 * List supported models.
 */ const listSupportedModelsApiV1EvalsModelsGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/evals/models",
        ...options
    });
};
/**
 * Get Job Image Result
 * Get a job by id
 */ const getJobImageResultApiV1ParsingJobJobIdResultImageNameGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/result/image/{name}",
        ...options
    });
};
/**
 * Get Supported File Extensions
 * Get a list of supported file extensions
 */ const getSupportedFileExtensionsApiV1ParsingSupportedFileExtensionsGet = (options)=>{
    return (options?.client ?? client).get({
        url: "/api/v1/parsing/supported_file_extensions",
        ...options
    });
};
/**
 * Screenshot
 */ const screenshotApiV1ParsingScreenshotPost = (options)=>{
    return (options?.client ?? client).post({
        ...k,
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/screenshot",
        ...options,
        headers: {
            "Content-Type": null,
            ...options?.headers
        }
    });
};
/**
 * Upload File
 */ const uploadFileApiV1ParsingUploadPost = (options)=>{
    return (options?.client ?? client).post({
        ...k,
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/upload",
        ...options,
        headers: {
            "Content-Type": null,
            ...options?.headers
        }
    });
};
/**
 * Get Job
 * Get a job by id
 */ const getJobApiV1ParsingJobJobIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}",
        ...options
    });
};
/**
 * Get Job Parameters
 * Get a job by id
 */ const getJobParametersApiV1ParsingJobJobIdParametersGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/parameters",
        ...options
    });
};
/**
 * Get Parsing Job Details
 * Get a job by id
 */ const getParsingJobDetailsApiV1ParsingJobJobIdDetailsGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/details",
        ...options
    });
};
/**
 * Get Job Text Result
 * Get a job by id
 *
 * Note: The 'credits_used' and 'job_credits_usage' fields in the response metadata are deprecated
 * and will be removed in a future release.
 */ const getJobTextResultApiV1ParsingJobJobIdResultTextGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/result/text",
        ...options
    });
};
/**
 * Get Job Raw Text Result Raw
 * Get a job by id
 */ const getJobRawTextResultRawApiV1ParsingJobJobIdResultRawTextGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/result/raw/text",
        ...options
    });
};
/**
 * Get Job Raw Text Result
 * Get a job by id
 */ const getJobRawTextResultApiV1ParsingJobJobIdResultPdfGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/result/pdf",
        ...options
    });
};
/**
 * Get Job Raw Text Result Raw Pdf
 * Get a job by id
 */ const getJobRawTextResultRawPdfApiV1ParsingJobJobIdResultRawPdfGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/result/raw/pdf",
        ...options
    });
};
/**
 * Get Job Structured Result
 * Get a job by id
 *
 * Note: The 'credits_used' and 'job_credits_usage' fields in the response metadata are deprecated
 * and will be removed in a future release.
 */ const getJobStructuredResultApiV1ParsingJobJobIdResultStructuredGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/result/structured",
        ...options
    });
};
/**
 * Get Job Raw Structured Result
 * Get a job by id
 */ const getJobRawStructuredResultApiV1ParsingJobJobIdResultRawStructuredGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/result/raw/structured",
        ...options
    });
};
/**
 * Get Job Raw Xlsx Result
 * Get a job by id
 */ const getJobRawXlsxResultApiV1ParsingJobJobIdResultXlsxGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/result/xlsx",
        ...options
    });
};
/**
 * Get Job Raw Xlsx Result Raw
 * Get a job by id
 */ const getJobRawXlsxResultRawApiV1ParsingJobJobIdResultRawXlsxGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/result/raw/xlsx",
        ...options
    });
};
/**
 * Get Job Result
 * Get a job by id
 *
 * Note: The 'credits_used' and 'job_credits_usage' fields in the response metadata are deprecated
 * and will be removed in a future release.
 */ const getJobResultApiV1ParsingJobJobIdResultMarkdownGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/result/markdown",
        ...options
    });
};
/**
 * Get Job Raw Md Result
 * Get a job by id
 */ const getJobRawMdResultApiV1ParsingJobJobIdResultRawMarkdownGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/result/raw/markdown",
        ...options
    });
};
/**
 * Get Job Json Result
 * Get a job by id
 *
 * Note: The 'credits_used' and 'job_credits_usage' fields in the response metadata are deprecated
 * and will be removed in a future release.
 */ const getJobJsonResultApiV1ParsingJobJobIdResultJsonGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/result/json",
        ...options
    });
};
/**
 * Get Job Json Raw Result
 * Get a job by id
 */ const getJobJsonRawResultApiV1ParsingJobJobIdResultRawJsonGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/result/raw/json",
        ...options
    });
};
/**
 * @deprecated
 * Get Parsing History Result
 * Get parsing history for user
 *
 * This endpoint is deprecated.
 * Use /api/v1/jobs/?job_name=parsing&project_id=YOUR_PROJECT_ID instead.
 */ const getParsingHistoryResultApiV1ParsingHistoryGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/history",
        ...options
    });
};
/**
 * Generate Presigned Url
 * Generate a presigned URL for a job
 */ const generatePresignedUrlApiV1ParsingJobJobIdReadFilenameGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/parsing/job/{job_id}/read/{filename}",
        ...options
    });
};
/**
 * Get Chat Apps
 */ const getChatAppsApiV1AppsGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/apps/",
        ...options
    });
};
/**
 * Create Chat App
 * Create a new chat app.
 */ const createChatAppApiV1AppsPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/apps/",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Delete Chat App
 */ const deleteChatAppApiV1AppsIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/apps/{id}",
        ...options
    });
};
/**
 * Get Chat App
 * Get a chat app by ID.
 */ const getChatAppApiV1AppsIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/apps/{id}",
        ...options
    });
};
/**
 * Update Chat App
 * Update a chat app.
 */ const updateChatAppApiV1AppsIdPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/apps/{id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Chat With Chat App
 * Chat with a chat app.
 */ const chatWithChatAppApiV1AppsIdChatPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/apps/{id}/chat",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * List Deployments
 * List all deployments for a project.
 */ const listDeploymentsApiV1ProjectsProjectIdAgentsGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/projects/{project_id}/agents",
        ...options
    });
};
/**
 * Sync Deployments
 * Sync deployments for a project.
 */ const syncDeploymentsApiV1ProjectsProjectIdAgentsSyncPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/projects/{project_id}/agents:sync",
        ...options
    });
};
/**
 * Create Checkout Session
 * Create a new checkout session.
 */ const createCheckoutSessionApiV1BillingCheckoutSessionPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/billing/checkout-session",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Create Customer Portal Session
 * Create a new customer portal session.
 */ const createCustomerPortalSessionApiV1BillingCustomerPortalSessionPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/billing/customer-portal-session",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Stripe Webhook
 * Stripe webhook endpoint.
 */ const stripeWebhookApiV1BillingWebhookPost = (options)=>{
    return (options?.client ?? client).post({
        url: "/api/v1/billing/webhook",
        ...options
    });
};
/**
 * Downgrade Plan
 */ const downgradePlanApiV1BillingDowngradePlanPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/billing/downgrade-plan",
        ...options
    });
};
/**
 * Create Intent And Customer Session
 * Create a new setup intent and and a customer session.
 *
 * See https://docs.stripe.com/payments/existing-customers?platform=web&ui=elements
 */ const createIntentAndCustomerSessionApiV1BillingCreateIntentAndCustomerSessionPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/billing/create-intent-and-customer-session",
        ...options
    });
};
/**
 * Metronome Webhook
 * Metronome webhook endpoint.
 */ const metronomeWebhookApiV1BillingMetronomeWebhookPost = (options)=>{
    return (options?.client ?? client).post({
        url: "/api/v1/billing/metronome-webhook",
        ...options
    });
};
/**
 * Get Metronome Dashboard
 * Get the invoices for a given organization.
 */ const getMetronomeDashboardApiV1BillingMetronomeDashboardGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/billing/metronome/dashboard",
        ...options
    });
};
/**
 * List Extraction Agents
 */ const listExtractionAgentsApiV1ExtractionExtractionAgentsGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/extraction-agents",
        ...options
    });
};
/**
 * Create Extraction Agent
 */ const createExtractionAgentApiV1ExtractionExtractionAgentsPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/extraction-agents",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Validate Extraction Schema
 * Validates an extraction agent's schema definition.
 * Returns the normalized and validated schema if valid, otherwise raises an HTTP 400.
 */ const validateExtractionSchemaApiV1ExtractionExtractionAgentsSchemaValidationPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/extraction-agents/schema/validation",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Generate Extraction Schema
 * Generates an extraction agent's schema definition from a file and/or natural language prompt.
 */ const generateExtractionSchemaApiV1ExtractionExtractionAgentsSchemaGeneratePost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/extraction-agents/schema/generate",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Get Extraction Agent By Name
 */ const getExtractionAgentByNameApiV1ExtractionExtractionAgentsByNameNameGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/extraction-agents/by-name/{name}",
        ...options
    });
};
/**
 * Delete Extraction Agent
 */ const deleteExtractionAgentApiV1ExtractionExtractionAgentsExtractionAgentIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/extraction-agents/{extraction_agent_id}",
        ...options
    });
};
/**
 * Get Extraction Agent
 */ const getExtractionAgentApiV1ExtractionExtractionAgentsExtractionAgentIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/extraction-agents/{extraction_agent_id}",
        ...options
    });
};
/**
 * Update Extraction Agent
 */ const updateExtractionAgentApiV1ExtractionExtractionAgentsExtractionAgentIdPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/extraction-agents/{extraction_agent_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * List Jobs
 */ const listJobsApiV1ExtractionJobsGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/jobs",
        ...options
    });
};
/**
 * Run Job
 */ const runJobApiV1ExtractionJobsPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/jobs",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Get Job
 */ const getJobApiV1ExtractionJobsJobIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/jobs/{job_id}",
        ...options
    });
};
/**
 * Run Job Test User
 */ const runJobTestUserApiV1ExtractionJobsTestPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/jobs/test",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Run Job On File
 */ const runJobOnFileApiV1ExtractionJobsFilePost = (options)=>{
    return (options.client ?? client).post({
        ...k,
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/jobs/file",
        ...options,
        headers: {
            "Content-Type": null,
            ...options?.headers
        }
    });
};
/**
 * Run Batch Jobs
 */ const runBatchJobsApiV1ExtractionJobsBatchPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/jobs/batch",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Get Job Result
 */ const getJobResultApiV1ExtractionJobsJobIdResultGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/jobs/{job_id}/result",
        ...options
    });
};
/**
 * List Extract Runs
 */ const listExtractRunsApiV1ExtractionRunsGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/runs",
        ...options
    });
};
/**
 * Get Latest Run From Ui
 */ const getLatestRunFromUiApiV1ExtractionRunsLatestFromUiGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/runs/latest-from-ui",
        ...options
    });
};
/**
 * Get Run By Job Id
 */ const getRunByJobIdApiV1ExtractionRunsByJobJobIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/runs/by-job/{job_id}",
        ...options
    });
};
/**
 * Delete Extraction Run
 */ const deleteExtractionRunApiV1ExtractionRunsRunIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/runs/{run_id}",
        ...options
    });
};
/**
 * Get Run
 */ const getRunApiV1ExtractionRunsRunIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/extraction/runs/{run_id}",
        ...options
    });
};
/**
 * @deprecated
 * Create Report
 * Create a new report.
 */ const createReportApiV1ReportsPost = (options)=>{
    return (options.client ?? client).post({
        ...k,
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/reports/",
        ...options,
        headers: {
            "Content-Type": null,
            ...options?.headers
        }
    });
};
/**
 * @deprecated
 * List Reports
 * List all reports for a project.
 */ const listReportsApiV1ReportsListGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/reports/list",
        ...options
    });
};
/**
 * @deprecated
 * Delete Report
 * Delete a report.
 */ const deleteReportApiV1ReportsReportIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/reports/{report_id}",
        ...options
    });
};
/**
 * @deprecated
 * Get Report
 * Get a specific report.
 */ const getReportApiV1ReportsReportIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/reports/{report_id}",
        ...options
    });
};
/**
 * @deprecated
 * Update Report
 * Update a report's content.
 */ const updateReportApiV1ReportsReportIdPatch = (options)=>{
    return (options.client ?? client).patch({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/reports/{report_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * @deprecated
 * Update Report Metadata
 * Update metadata for a report.
 */ const updateReportMetadataApiV1ReportsReportIdPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/reports/{report_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * @deprecated
 * Get Report Plan
 * Get the plan for a report.
 */ const getReportPlanApiV1ReportsReportIdPlanGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/reports/{report_id}/plan",
        ...options
    });
};
/**
 * @deprecated
 * Update Report Plan
 * Update the plan of a report, including approval, rejection, and editing.
 */ const updateReportPlanApiV1ReportsReportIdPlanPatch = (options)=>{
    return (options.client ?? client).patch({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/reports/{report_id}/plan",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * @deprecated
 * Get Report Events
 * Get all historical events for a report.
 */ const getReportEventsApiV1ReportsReportIdEventsGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/reports/{report_id}/events",
        ...options
    });
};
/**
 * @deprecated
 * Get Report Metadata
 * Get metadata for a report.
 */ const getReportMetadataApiV1ReportsReportIdMetadataGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/reports/{report_id}/metadata",
        ...options
    });
};
/**
 * @deprecated
 * Suggest Edits Endpoint
 * Suggest edits to a report based on user query and chat history.
 */ const suggestEditsEndpointApiV1ReportsReportIdSuggestEditsPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/reports/{report_id}/suggest_edits",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * @deprecated
 * Restart Report
 * Restart a report from scratch.
 */ const restartReportApiV1ReportsReportIdRestartPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/reports/{report_id}/restart",
        ...options
    });
};
/**
 * List Batches
 */ const listBatchesApiV1BetaBatchesGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/beta/batches",
        ...options
    });
};
/**
 * Create Batch
 */ const createBatchApiV1BetaBatchesPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/beta/batches",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Get Batch
 */ const getBatchApiV1BetaBatchesBatchIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/beta/batches/{batch_id}",
        ...options
    });
};
/**
 * Delete Agent Data
 * Delete agent data by ID.
 */ const deleteAgentDataApiV1BetaAgentDataItemIdDelete = (options)=>{
    return (options.client ?? client).delete({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/beta/agent-data/{item_id}",
        ...options
    });
};
/**
 * Get Agent Data
 * Get agent data by ID.
 */ const getAgentDataApiV1BetaAgentDataItemIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/beta/agent-data/{item_id}",
        ...options
    });
};
/**
 * Update Agent Data
 * Update agent data by ID (overwrites).
 */ const updateAgentDataApiV1BetaAgentDataItemIdPut = (options)=>{
    return (options.client ?? client).put({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/beta/agent-data/{item_id}",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Create Agent Data
 * Create new agent data.
 */ const createAgentDataApiV1BetaAgentDataPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/beta/agent-data/",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Search Agent Data
 * Search agent data with filtering, sorting, and pagination.
 */ const searchAgentDataApiV1BetaAgentDataSearchPost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/beta/agent-data/:search",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Aggregate Agent Data
 * Aggregate agent data with grouping and optional counting/first item retrieval.
 */ const aggregateAgentDataApiV1BetaAgentDataAggregatePost = (options)=>{
    return (options.client ?? client).post({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/v1/beta/agent-data/:aggregate",
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...options?.headers
        }
    });
};
/**
 * Get Job Image Result
 * Get a job by id
 */ const getJobImageResultApiParsingJobJobIdResultImageNameGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/result/image/{name}",
        ...options
    });
};
/**
 * Get Supported File Extensions
 * Get a list of supported file extensions
 */ const getSupportedFileExtensionsApiParsingSupportedFileExtensionsGet = (options)=>{
    return (options?.client ?? client).get({
        url: "/api/parsing/supported_file_extensions",
        ...options
    });
};
/**
 * Screenshot
 */ const screenshotApiParsingScreenshotPost = (options)=>{
    return (options?.client ?? client).post({
        ...k,
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/screenshot",
        ...options,
        headers: {
            "Content-Type": null,
            ...options?.headers
        }
    });
};
/**
 * Upload File
 */ const uploadFileApiParsingUploadPost = (options)=>{
    return (options?.client ?? client).post({
        ...k,
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/upload",
        ...options,
        headers: {
            "Content-Type": null,
            ...options?.headers
        }
    });
};
/**
 * Get Job
 * Get a job by id
 */ const getJobApiParsingJobJobIdGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}",
        ...options
    });
};
/**
 * Get Job Parameters
 * Get a job by id
 */ const getJobParametersApiParsingJobJobIdParametersGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/parameters",
        ...options
    });
};
/**
 * Get Parsing Job Details
 * Get a job by id
 */ const getParsingJobDetailsApiParsingJobJobIdDetailsGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/details",
        ...options
    });
};
/**
 * Get Job Text Result
 * Get a job by id
 *
 * Note: The 'credits_used' and 'job_credits_usage' fields in the response metadata are deprecated
 * and will be removed in a future release.
 */ const getJobTextResultApiParsingJobJobIdResultTextGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/result/text",
        ...options
    });
};
/**
 * Get Job Raw Text Result Raw
 * Get a job by id
 */ const getJobRawTextResultRawApiParsingJobJobIdResultRawTextGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/result/raw/text",
        ...options
    });
};
/**
 * Get Job Raw Text Result
 * Get a job by id
 */ const getJobRawTextResultApiParsingJobJobIdResultPdfGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/result/pdf",
        ...options
    });
};
/**
 * Get Job Raw Text Result Raw Pdf
 * Get a job by id
 */ const getJobRawTextResultRawPdfApiParsingJobJobIdResultRawPdfGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/result/raw/pdf",
        ...options
    });
};
/**
 * Get Job Structured Result
 * Get a job by id
 *
 * Note: The 'credits_used' and 'job_credits_usage' fields in the response metadata are deprecated
 * and will be removed in a future release.
 */ const getJobStructuredResultApiParsingJobJobIdResultStructuredGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/result/structured",
        ...options
    });
};
/**
 * Get Job Raw Structured Result
 * Get a job by id
 */ const getJobRawStructuredResultApiParsingJobJobIdResultRawStructuredGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/result/raw/structured",
        ...options
    });
};
/**
 * Get Job Raw Xlsx Result
 * Get a job by id
 */ const getJobRawXlsxResultApiParsingJobJobIdResultXlsxGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/result/xlsx",
        ...options
    });
};
/**
 * Get Job Raw Xlsx Result Raw
 * Get a job by id
 */ const getJobRawXlsxResultRawApiParsingJobJobIdResultRawXlsxGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/result/raw/xlsx",
        ...options
    });
};
/**
 * Get Job Result
 * Get a job by id
 *
 * Note: The 'credits_used' and 'job_credits_usage' fields in the response metadata are deprecated
 * and will be removed in a future release.
 */ const getJobResultApiParsingJobJobIdResultMarkdownGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/result/markdown",
        ...options
    });
};
/**
 * Get Job Raw Md Result
 * Get a job by id
 */ const getJobRawMdResultApiParsingJobJobIdResultRawMarkdownGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/result/raw/markdown",
        ...options
    });
};
/**
 * Get Job Json Result
 * Get a job by id
 *
 * Note: The 'credits_used' and 'job_credits_usage' fields in the response metadata are deprecated
 * and will be removed in a future release.
 */ const getJobJsonResultApiParsingJobJobIdResultJsonGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/result/json",
        ...options
    });
};
/**
 * Get Job Json Raw Result
 * Get a job by id
 */ const getJobJsonRawResultApiParsingJobJobIdResultRawJsonGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/result/raw/json",
        ...options
    });
};
/**
 * @deprecated
 * Get Parsing History Result
 * Get parsing history for user
 *
 * This endpoint is deprecated.
 * Use /api/v1/jobs/?job_name=parsing&project_id=YOUR_PROJECT_ID instead.
 */ const getParsingHistoryResultApiParsingHistoryGet = (options)=>{
    return (options?.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/history",
        ...options
    });
};
/**
 * Generate Presigned Url
 * Generate a presigned URL for a job
 */ const generatePresignedUrlApiParsingJobJobIdReadFilenameGet = (options)=>{
    return (options.client ?? client).get({
        security: [
            {
                scheme: "bearer",
                type: "http"
            },
            {
                scheme: "bearer",
                type: "http"
            }
        ],
        url: "/api/parsing/job/{job_id}/read/{filename}",
        ...options
    });
};

// This file is auto-generated by @hey-api/openapi-ts
/**
 * Schema for an API Key.
 */ const BoxAuthMechanism = {
    DEVELOPER_TOKEN: "developer_token",
    CCG: "ccg"
};
const ChunkMode = {
    PAGE: "PAGE",
    DOCUMENT: "DOCUMENT",
    SECTION: "SECTION",
    GROUPED_PAGES: "GROUPED_PAGES"
};
/**
 * Enum for the mode of composite retrieval.
 */ const CompositeRetrievalMode = {
    ROUTING: "routing",
    FULL: "full"
};
const ConfigurableDataSinkNames = {
    PINECONE: "PINECONE",
    POSTGRES: "POSTGRES",
    QDRANT: "QDRANT",
    AZUREAI_SEARCH: "AZUREAI_SEARCH",
    MONGODB_ATLAS: "MONGODB_ATLAS",
    MILVUS: "MILVUS"
};
const ConfigurableDataSourceNames = {
    S3: "S3",
    AZURE_STORAGE_BLOB: "AZURE_STORAGE_BLOB",
    GOOGLE_DRIVE: "GOOGLE_DRIVE",
    MICROSOFT_ONEDRIVE: "MICROSOFT_ONEDRIVE",
    MICROSOFT_SHAREPOINT: "MICROSOFT_SHAREPOINT",
    SLACK: "SLACK",
    NOTION_PAGE: "NOTION_PAGE",
    CONFLUENCE: "CONFLUENCE",
    JIRA: "JIRA",
    BOX: "BOX"
};
const DocumentChunkMode = {
    PAGE: "PAGE",
    SECTION: "SECTION"
};
const ExtractMode = {
    FAST: "FAST",
    BALANCED: "BALANCED",
    PREMIUM: "PREMIUM",
    MULTIMODAL: "MULTIMODAL",
    ACCURATE: "ACCURATE"
};
const ExtractModels = {
    GPT_4_1: "gpt-4.1",
    GPT_4_1_MINI: "gpt-4.1-mini",
    GPT_4_1_NANO: "gpt-4.1-nano",
    GEMINI_2_0_FLASH: "gemini-2.0-flash",
    O3_MINI: "o3-mini",
    GEMINI_2_5_FLASH: "gemini-2.5-flash",
    GEMINI_2_5_PRO: "gemini-2.5-pro",
    GEMINI_2_5_FLASH_LITE_PREVIEW_06_17: "gemini-2.5-flash-lite-preview-06-17",
    GPT_4O: "gpt-4o",
    GPT_4O_MINI: "gpt-4o-mini"
};
const ExtractState = {
    CREATED: "CREATED",
    PENDING: "PENDING",
    SUCCESS: "SUCCESS",
    ERROR: "ERROR"
};
const ExtractTarget = {
    PER_DOC: "PER_DOC",
    PER_PAGE: "PER_PAGE"
};
/**
 * Enum for representing the different available page error handling modes
 */ const FailPageMode = {
    RAW_TEXT: "raw_text",
    BLANK_PAGE: "blank_page",
    ERROR_MESSAGE: "error_message"
};
/**
 * Vector store filter conditions to combine different filters.
 */ const FilterCondition = {
    AND: "and",
    OR: "or",
    NOT: "not"
};
/**
 * Vector store filter operator.
 */ const FilterOperator = {
    "==": "==",
    ">": ">",
    "<": "<",
    "!=": "!=",
    ">=": ">=",
    "<=": "<=",
    IN: "in",
    NIN: "nin",
    ANY: "any",
    ALL: "all",
    TEXT_MATCH: "text_match",
    TEXT_MATCH_INSENSITIVE: "text_match_insensitive",
    CONTAINS: "contains",
    IS_EMPTY: "is_empty"
};
/**
 * Enum for mapping original job names to readable names.
 */ const JobNameMapping = {
    MANAGED_INGESTION: "MANAGED_INGESTION",
    DATA_SOURCE: "DATA_SOURCE",
    FILES_UPDATE: "FILES_UPDATE",
    FILE_UPDATER: "FILE_UPDATER",
    PARSE: "PARSE",
    TRANSFORM: "TRANSFORM",
    INGESTION: "INGESTION",
    METADATA_UPDATE: "METADATA_UPDATE"
};
/**
 * Enum for executable pipeline job names.
 */ const JobNames = {
    LOAD_DOCUMENTS_JOB: "load_documents_job",
    LOAD_FILES_JOB: "load_files_job",
    PLAYGROUND_JOB: "playground_job",
    PIPELINE_MANAGED_INGESTION_JOB: "pipeline_managed_ingestion_job",
    DATA_SOURCE_MANAGED_INGESTION_JOB: "data_source_managed_ingestion_job",
    DATA_SOURCE_UPDATE_DISPATCHER_JOB: "data_source_update_dispatcher_job",
    PIPELINE_FILE_UPDATE_DISPATCHER_JOB: "pipeline_file_update_dispatcher_job",
    PIPELINE_FILE_UPDATER_JOB: "pipeline_file_updater_job",
    FILE_MANAGED_INGESTION_JOB: "file_managed_ingestion_job",
    DOCUMENT_INGESTION_JOB: "document_ingestion_job",
    METADATA_UPDATE_JOB: "metadata_update_job",
    PARSE_RAW_FILE_JOB_CACHED: "parse_raw_file_job_cached",
    EXTRACTION_JOB: "extraction_job",
    EXTRACT_JOB: "extract_job",
    ASYNCIO_TEST_JOB: "asyncio_test_job",
    PARSE_RAW_FILE_JOB: "parse_raw_file_job",
    LLAMA_PARSE_TRANSFORM_JOB: "llama_parse_transform_job"
};
const LlamaParseSupportedFileExtensions = {
    ".PDF": ".pdf",
    ".DOC": ".doc",
    ".DOCX": ".docx",
    ".DOCM": ".docm",
    ".DOT": ".dot",
    ".DOTX": ".dotx",
    ".DOTM": ".dotm",
    ".RTF": ".rtf",
    ".WPS": ".wps",
    ".WPD": ".wpd",
    ".SXW": ".sxw",
    ".STW": ".stw",
    ".SXG": ".sxg",
    ".PAGES": ".pages",
    ".MW": ".mw",
    ".MCW": ".mcw",
    ".UOT": ".uot",
    ".UOF": ".uof",
    ".UOS": ".uos",
    ".UOP": ".uop",
    ".PPT": ".ppt",
    ".PPTX": ".pptx",
    ".POT": ".pot",
    ".PPTM": ".pptm",
    ".POTX": ".potx",
    ".POTM": ".potm",
    ".KEY": ".key",
    ".ODP": ".odp",
    ".ODG": ".odg",
    ".OTP": ".otp",
    ".FOPD": ".fopd",
    ".SXI": ".sxi",
    ".STI": ".sti",
    ".EPUB": ".epub",
    ".JPG": ".jpg",
    ".JPEG": ".jpeg",
    ".PNG": ".png",
    ".GIF": ".gif",
    ".BMP": ".bmp",
    ".SVG": ".svg",
    ".TIFF": ".tiff",
    ".WEBP": ".webp",
    ".HTML": ".html",
    ".HTM": ".htm",
    ".XLS": ".xls",
    ".XLSX": ".xlsx",
    ".XLSM": ".xlsm",
    ".XLSB": ".xlsb",
    ".XLW": ".xlw",
    ".CSV": ".csv",
    ".DIF": ".dif",
    ".SYLK": ".sylk",
    ".SLK": ".slk",
    ".PRN": ".prn",
    ".NUMBERS": ".numbers",
    ".ET": ".et",
    ".ODS": ".ods",
    ".FODS": ".fods",
    ".UOS1": ".uos1",
    ".UOS2": ".uos2",
    ".DBF": ".dbf",
    ".WK1": ".wk1",
    ".WK2": ".wk2",
    ".WK3": ".wk3",
    ".WK4": ".wk4",
    ".WKS": ".wks",
    ".WQ1": ".wq1",
    ".WQ2": ".wq2",
    ".WB1": ".wb1",
    ".WB2": ".wb2",
    ".WB3": ".wb3",
    ".QPW": ".qpw",
    ".XLR": ".xlr",
    ".ETH": ".eth",
    ".TSV": ".tsv"
};
/**
 * Status of managed ingestion with partial Updates.
 */ const ManagedIngestionStatus = {
    NOT_STARTED: "NOT_STARTED",
    IN_PROGRESS: "IN_PROGRESS",
    SUCCESS: "SUCCESS",
    ERROR: "ERROR",
    PARTIAL_SUCCESS: "PARTIAL_SUCCESS",
    CANCELLED: "CANCELLED"
};
/**
 * Message role.
 */ const MessageRole = {
    SYSTEM: "system",
    DEVELOPER: "developer",
    USER: "user",
    ASSISTANT: "assistant",
    FUNCTION: "function",
    TOOL: "tool",
    CHATBOT: "chatbot",
    MODEL: "model"
};
const MetronomeDashboardType = {
    INVOICES: "invoices",
    USAGE: "usage"
};
/**
 * Node relationships used in `BaseNode` class.
 *
 * Attributes:
 * SOURCE: The node is the source document.
 * PREVIOUS: The node is the previous node in the document.
 * NEXT: The node is the next node in the document.
 * PARENT: The node is the parent node in the document.
 * CHILD: The node is a child node in the document.
 */ const NodeRelationship = {
    1: "1",
    2: "2",
    3: "3",
    4: "4",
    5: "5"
};
const ObjectType = {
    1: "1",
    2: "2",
    3: "3",
    4: "4",
    5: "5"
};
/**
 * Distance methods for PGVector.
 * Docs:
 * https://github.com/pgvector/pgvector?tab=readme-ov-file#query-options
 */ const PgVectorDistanceMethod = {
    L2: "l2",
    IP: "ip",
    COSINE: "cosine",
    L1: "l1",
    HAMMING: "hamming",
    JACCARD: "jaccard"
};
/**
 * Vector storage formats for PGVector.
 * Docs:
 * https://github.com/pgvector/pgvector?tab=readme-ov-file#query-options
 */ const PgVectorVectorType = {
    VECTOR: "vector",
    HALF_VEC: "half_vec",
    BIT: "bit",
    SPARSE_VEC: "sparse_vec"
};
/**
 * Enum for the Parse plan level.
 */ const ParsePlanLevel = {
    DEFAULT: "DEFAULT",
    PREMIUM: "PREMIUM"
};
/**
 * Enum for representing the languages supported by the parser
 */ const ParserLanguages = {
    AF: "af",
    AZ: "az",
    BS: "bs",
    CS: "cs",
    CY: "cy",
    DA: "da",
    DE: "de",
    EN: "en",
    ES: "es",
    ET: "et",
    FR: "fr",
    GA: "ga",
    HR: "hr",
    HU: "hu",
    ID: "id",
    IS: "is",
    IT: "it",
    KU: "ku",
    LA: "la",
    LT: "lt",
    LV: "lv",
    MI: "mi",
    MS: "ms",
    MT: "mt",
    NL: "nl",
    NO: "no",
    OC: "oc",
    PI: "pi",
    PL: "pl",
    PT: "pt",
    RO: "ro",
    RS_LATIN: "rs_latin",
    SK: "sk",
    SL: "sl",
    SQ: "sq",
    SV: "sv",
    SW: "sw",
    TL: "tl",
    TR: "tr",
    UZ: "uz",
    VI: "vi",
    AR: "ar",
    FA: "fa",
    UG: "ug",
    UR: "ur",
    BN: "bn",
    AS: "as",
    MNI: "mni",
    RU: "ru",
    RS_CYRILLIC: "rs_cyrillic",
    BE: "be",
    BG: "bg",
    UK: "uk",
    MN: "mn",
    ABQ: "abq",
    ADY: "ady",
    KBD: "kbd",
    AVA: "ava",
    DAR: "dar",
    INH: "inh",
    CHE: "che",
    LBE: "lbe",
    LEZ: "lez",
    TAB: "tab",
    TJK: "tjk",
    HI: "hi",
    MR: "mr",
    NE: "ne",
    BH: "bh",
    MAI: "mai",
    ANG: "ang",
    BHO: "bho",
    MAH: "mah",
    SCK: "sck",
    NEW: "new",
    GOM: "gom",
    SA: "sa",
    BGC: "bgc",
    TH: "th",
    CH_SIM: "ch_sim",
    CH_TRA: "ch_tra",
    JA: "ja",
    KO: "ko",
    TA: "ta",
    TE: "te",
    KN: "kn"
};
/**
 * Enum for representing the mode of parsing to be used
 */ const ParsingMode = {
    PARSE_PAGE_WITHOUT_LLM: "parse_page_without_llm",
    PARSE_PAGE_WITH_LLM: "parse_page_with_llm",
    PARSE_PAGE_WITH_LVM: "parse_page_with_lvm",
    PARSE_PAGE_WITH_AGENT: "parse_page_with_agent",
    PARSE_PAGE_WITH_LAYOUT_AGENT: "parse_page_with_layout_agent",
    PARSE_DOCUMENT_WITH_LLM: "parse_document_with_llm",
    PARSE_DOCUMENT_WITH_LVM: "parse_document_with_lvm",
    PARSE_DOCUMENT_WITH_AGENT: "parse_document_with_agent"
};
/**
 * Enum for dataset partition names.
 */ const PartitionNames = {
    DATA_SOURCE_ID_PARTITION: "data_source_id_partition",
    PIPELINE_ID_PARTITION: "pipeline_id_partition",
    EVAL_DATASET_ID_PARTITION: "eval_dataset_id_partition",
    FILE_ID_PARTITION: "file_id_partition",
    PIPELINE_FILE_ID_PARTITION: "pipeline_file_id_partition",
    FILE_PARSING_ID_PARTITION: "file_parsing_id_partition",
    EXTRACTION_SCHEMA_ID_PARTITION: "extraction_schema_id_partition"
};
/**
 * Enum for representing the type of a pipeline
 */ const PipelineType = {
    PLAYGROUND: "PLAYGROUND",
    MANAGED: "MANAGED"
};
/**
 * Enum of possible pooling choices with pooling behaviors.
 */ const Pooling = {
    CLS: "cls",
    MEAN: "mean",
    LAST: "last"
};
/**
 * Enum for the reranker type.
 */ const ReRankerType = {
    SYSTEM_DEFAULT: "system_default",
    LLM: "llm",
    COHERE: "cohere",
    BEDROCK: "bedrock",
    SCORE: "score",
    DISABLED: "disabled"
};
const ReportBlockDependency = {
    NONE: "none",
    ALL: "all",
    PREVIOUS: "previous",
    NEXT: "next"
};
const ReportEventType = {
    LOAD_TEMPLATE: "load_template",
    EXTRACT_PLAN: "extract_plan",
    SUMMARIZE: "summarize",
    FILE_PROCESSING: "file_processing",
    GENERATE_BLOCK: "generate_block",
    EDITING: "editing"
};
const ReportState = {
    PENDING: "pending",
    PLANNING: "planning",
    WAITING_APPROVAL: "waiting_approval",
    GENERATING: "generating",
    COMPLETED: "completed",
    ERROR: "error"
};
const RetrievalMode = {
    CHUNKS: "chunks",
    FILES_VIA_METADATA: "files_via_metadata",
    FILES_VIA_CONTENT: "files_via_content",
    AUTO_ROUTED: "auto_routed"
};
const SchemaRelaxMode = {
    FULL: "FULL",
    TOP_LEVEL: "TOP_LEVEL",
    LEAF: "LEAF"
};
/**
 * Enum for representing the status of a job
 */ const StatusEnum = {
    PENDING: "PENDING",
    SUCCESS: "SUCCESS",
    ERROR: "ERROR",
    PARTIAL_SUCCESS: "PARTIAL_SUCCESS",
    CANCELLED: "CANCELLED"
};
const StructMode = {
    STRUCT_PARSE: "STRUCT_PARSE",
    JSON_MODE: "JSON_MODE",
    FUNC_CALL: "FUNC_CALL",
    STRUCT_RELAXED: "STRUCT_RELAXED",
    UNSTRUCTURED: "UNSTRUCTURED"
};
const SupportedLlmModelNames = {
    GPT_4O: "GPT_4O",
    GPT_4O_MINI: "GPT_4O_MINI",
    GPT_4_1: "GPT_4_1",
    GPT_4_1_NANO: "GPT_4_1_NANO",
    GPT_4_1_MINI: "GPT_4_1_MINI",
    AZURE_OPENAI_GPT_4O: "AZURE_OPENAI_GPT_4O",
    AZURE_OPENAI_GPT_4O_MINI: "AZURE_OPENAI_GPT_4O_MINI",
    CLAUDE_3_5_SONNET: "CLAUDE_3_5_SONNET",
    BEDROCK_CLAUDE_3_5_SONNET_V1: "BEDROCK_CLAUDE_3_5_SONNET_V1",
    BEDROCK_CLAUDE_3_5_SONNET_V2: "BEDROCK_CLAUDE_3_5_SONNET_V2",
    VERTEX_AI_CLAUDE_3_5_SONNET_V2: "VERTEX_AI_CLAUDE_3_5_SONNET_V2"
};
/**
 * Copied from llama_index.embeddings.vertex.base.VertexEmbeddingMode
 * since importing llama_index.embeddings.vertex.base incurs a lot of memory usage.
 */ const VertexEmbeddingMode = {
    DEFAULT: "default",
    CLASSIFICATION: "classification",
    CLUSTERING: "clustering",
    SIMILARITY: "similarity",
    RETRIEVAL: "retrieval"
};

client.setConfig({
    baseUrl: "https://api.cloud.llamaindex.ai/",
    headers: {
        "X-SDK-Name": "llamaindex-ts"
    }
});

exports.BoxAuthMechanism = BoxAuthMechanism;
exports.ChunkMode = ChunkMode;
exports.CompositeRetrievalMode = CompositeRetrievalMode;
exports.ConfigurableDataSinkNames = ConfigurableDataSinkNames;
exports.ConfigurableDataSourceNames = ConfigurableDataSourceNames;
exports.DocumentChunkMode = DocumentChunkMode;
exports.ExtractMode = ExtractMode;
exports.ExtractModels = ExtractModels;
exports.ExtractState = ExtractState;
exports.ExtractTarget = ExtractTarget;
exports.FailPageMode = FailPageMode;
exports.FilterCondition = FilterCondition;
exports.FilterOperator = FilterOperator;
exports.JobNameMapping = JobNameMapping;
exports.JobNames = JobNames;
exports.LlamaParseSupportedFileExtensions = LlamaParseSupportedFileExtensions;
exports.ManagedIngestionStatus = ManagedIngestionStatus;
exports.MessageRole = MessageRole;
exports.MetronomeDashboardType = MetronomeDashboardType;
exports.NodeRelationship = NodeRelationship;
exports.ObjectType = ObjectType;
exports.ParsePlanLevel = ParsePlanLevel;
exports.ParserLanguages = ParserLanguages;
exports.ParsingMode = ParsingMode;
exports.PartitionNames = PartitionNames;
exports.PgVectorDistanceMethod = PgVectorDistanceMethod;
exports.PgVectorVectorType = PgVectorVectorType;
exports.PipelineType = PipelineType;
exports.Pooling = Pooling;
exports.ReRankerType = ReRankerType;
exports.ReportBlockDependency = ReportBlockDependency;
exports.ReportEventType = ReportEventType;
exports.ReportState = ReportState;
exports.RetrievalMode = RetrievalMode;
exports.SchemaRelaxMode = SchemaRelaxMode;
exports.StatusEnum = StatusEnum;
exports.StructMode = StructMode;
exports.SupportedLlmModelNames = SupportedLlmModelNames;
exports.VertexEmbeddingMode = VertexEmbeddingMode;
exports.addDataSourcesToPipelineApiV1PipelinesPipelineIdDataSourcesPut = addDataSourcesToPipelineApiV1PipelinesPipelineIdDataSourcesPut;
exports.addFilesToPipelineApiApiV1PipelinesPipelineIdFilesPut = addFilesToPipelineApiApiV1PipelinesPipelineIdFilesPut;
exports.addUserToProjectApiV1OrganizationsOrganizationIdUsersUserIdProjectsPut = addUserToProjectApiV1OrganizationsOrganizationIdUsersUserIdProjectsPut;
exports.addUsersToOrganizationApiV1OrganizationsOrganizationIdUsersPut = addUsersToOrganizationApiV1OrganizationsOrganizationIdUsersPut;
exports.aggregateAgentDataApiV1BetaAgentDataAggregatePost = aggregateAgentDataApiV1BetaAgentDataAggregatePost;
exports.assignRoleToUserInOrganizationApiV1OrganizationsOrganizationIdUsersRolesPut = assignRoleToUserInOrganizationApiV1OrganizationsOrganizationIdUsersRolesPut;
exports.batchRemoveUsersFromOrganizationApiV1OrganizationsOrganizationIdUsersRemovePut = batchRemoveUsersFromOrganizationApiV1OrganizationsOrganizationIdUsersRemovePut;
exports.cancelPipelineSyncApiV1PipelinesPipelineIdSyncCancelPost = cancelPipelineSyncApiV1PipelinesPipelineIdSyncCancelPost;
exports.chatApiV1PipelinesPipelineIdChatPost = chatApiV1PipelinesPipelineIdChatPost;
exports.chatWithChatAppApiV1AppsIdChatPost = chatWithChatAppApiV1AppsIdChatPost;
exports.client = client;
exports.copyPipelineApiV1PipelinesPipelineIdCopyPost = copyPipelineApiV1PipelinesPipelineIdCopyPost;
exports.createAgentDataApiV1BetaAgentDataPost = createAgentDataApiV1BetaAgentDataPost;
exports.createBatchApiV1BetaBatchesPost = createBatchApiV1BetaBatchesPost;
exports.createBatchPipelineDocumentsApiV1PipelinesPipelineIdDocumentsPost = createBatchPipelineDocumentsApiV1PipelinesPipelineIdDocumentsPost;
exports.createChatAppApiV1AppsPost = createChatAppApiV1AppsPost;
exports.createCheckoutSessionApiV1BillingCheckoutSessionPost = createCheckoutSessionApiV1BillingCheckoutSessionPost;
exports.createCustomerPortalSessionApiV1BillingCustomerPortalSessionPost = createCustomerPortalSessionApiV1BillingCustomerPortalSessionPost;
exports.createDataSinkApiV1DataSinksPost = createDataSinkApiV1DataSinksPost;
exports.createDataSourceApiV1DataSourcesPost = createDataSourceApiV1DataSourcesPost;
exports.createEmbeddingModelConfigApiV1EmbeddingModelConfigsPost = createEmbeddingModelConfigApiV1EmbeddingModelConfigsPost;
exports.createExtractionAgentApiV1ExtractionExtractionAgentsPost = createExtractionAgentApiV1ExtractionExtractionAgentsPost;
exports.createIntentAndCustomerSessionApiV1BillingCreateIntentAndCustomerSessionPost = createIntentAndCustomerSessionApiV1BillingCreateIntentAndCustomerSessionPost;
exports.createOrganizationApiV1OrganizationsPost = createOrganizationApiV1OrganizationsPost;
exports.createPipelineApiV1PipelinesPost = createPipelineApiV1PipelinesPost;
exports.createProjectApiV1ProjectsPost = createProjectApiV1ProjectsPost;
exports.createReportApiV1ReportsPost = createReportApiV1ReportsPost;
exports.createRetrieverApiV1RetrieversPost = createRetrieverApiV1RetrieversPost;
exports.deleteAgentDataApiV1BetaAgentDataItemIdDelete = deleteAgentDataApiV1BetaAgentDataItemIdDelete;
exports.deleteApiKeyApiV1ApiKeysApiKeyIdDelete = deleteApiKeyApiV1ApiKeysApiKeyIdDelete;
exports.deleteChatAppApiV1AppsIdDelete = deleteChatAppApiV1AppsIdDelete;
exports.deleteDataSinkApiV1DataSinksDataSinkIdDelete = deleteDataSinkApiV1DataSinksDataSinkIdDelete;
exports.deleteDataSourceApiV1DataSourcesDataSourceIdDelete = deleteDataSourceApiV1DataSourcesDataSourceIdDelete;
exports.deleteEmbeddingModelConfigApiV1EmbeddingModelConfigsEmbeddingModelConfigIdDelete = deleteEmbeddingModelConfigApiV1EmbeddingModelConfigsEmbeddingModelConfigIdDelete;
exports.deleteExtractionAgentApiV1ExtractionExtractionAgentsExtractionAgentIdDelete = deleteExtractionAgentApiV1ExtractionExtractionAgentsExtractionAgentIdDelete;
exports.deleteExtractionRunApiV1ExtractionRunsRunIdDelete = deleteExtractionRunApiV1ExtractionRunsRunIdDelete;
exports.deleteFileApiV1FilesIdDelete = deleteFileApiV1FilesIdDelete;
exports.deleteOrganizationApiV1OrganizationsOrganizationIdDelete = deleteOrganizationApiV1OrganizationsOrganizationIdDelete;
exports.deletePipelineApiV1PipelinesPipelineIdDelete = deletePipelineApiV1PipelinesPipelineIdDelete;
exports.deletePipelineDataSourceApiV1PipelinesPipelineIdDataSourcesDataSourceIdDelete = deletePipelineDataSourceApiV1PipelinesPipelineIdDataSourcesDataSourceIdDelete;
exports.deletePipelineDocumentApiV1PipelinesPipelineIdDocumentsDocumentIdDelete = deletePipelineDocumentApiV1PipelinesPipelineIdDocumentsDocumentIdDelete;
exports.deletePipelineFileApiV1PipelinesPipelineIdFilesFileIdDelete = deletePipelineFileApiV1PipelinesPipelineIdFilesFileIdDelete;
exports.deletePipelineFilesMetadataApiV1PipelinesPipelineIdMetadataDelete = deletePipelineFilesMetadataApiV1PipelinesPipelineIdMetadataDelete;
exports.deleteProjectApiV1ProjectsProjectIdDelete = deleteProjectApiV1ProjectsProjectIdDelete;
exports.deleteReportApiV1ReportsReportIdDelete = deleteReportApiV1ReportsReportIdDelete;
exports.deleteRetrieverApiV1RetrieversRetrieverIdDelete = deleteRetrieverApiV1RetrieversRetrieverIdDelete;
exports.directRetrieveApiV1RetrieversRetrievePost = directRetrieveApiV1RetrieversRetrievePost;
exports.downgradePlanApiV1BillingDowngradePlanPost = downgradePlanApiV1BillingDowngradePlanPost;
exports.forceDeletePipelineApiV1PipelinesPipelineIdForceDeletePost = forceDeletePipelineApiV1PipelinesPipelineIdForceDeletePost;
exports.generateExtractionSchemaApiV1ExtractionExtractionAgentsSchemaGeneratePost = generateExtractionSchemaApiV1ExtractionExtractionAgentsSchemaGeneratePost;
exports.generateKeyApiV1ApiKeysPost = generateKeyApiV1ApiKeysPost;
exports.generatePresignedUrlApiParsingJobJobIdReadFilenameGet = generatePresignedUrlApiParsingJobJobIdReadFilenameGet;
exports.generatePresignedUrlApiV1FilesPut = generatePresignedUrlApiV1FilesPut;
exports.generatePresignedUrlApiV1ParsingJobJobIdReadFilenameGet = generatePresignedUrlApiV1ParsingJobJobIdReadFilenameGet;
exports.getAgentDataApiV1BetaAgentDataItemIdGet = getAgentDataApiV1BetaAgentDataItemIdGet;
exports.getBatchApiV1BetaBatchesBatchIdGet = getBatchApiV1BetaBatchesBatchIdGet;
exports.getChatAppApiV1AppsIdGet = getChatAppApiV1AppsIdGet;
exports.getChatAppsApiV1AppsGet = getChatAppsApiV1AppsGet;
exports.getCurrentProjectApiV1ProjectsCurrentGet = getCurrentProjectApiV1ProjectsCurrentGet;
exports.getDataSinkApiV1DataSinksDataSinkIdGet = getDataSinkApiV1DataSinksDataSinkIdGet;
exports.getDataSourceApiV1DataSourcesDataSourceIdGet = getDataSourceApiV1DataSourcesDataSourceIdGet;
exports.getDefaultOrganizationApiV1OrganizationsDefaultGet = getDefaultOrganizationApiV1OrganizationsDefaultGet;
exports.getExtractionAgentApiV1ExtractionExtractionAgentsExtractionAgentIdGet = getExtractionAgentApiV1ExtractionExtractionAgentsExtractionAgentIdGet;
exports.getExtractionAgentByNameApiV1ExtractionExtractionAgentsByNameNameGet = getExtractionAgentByNameApiV1ExtractionExtractionAgentsByNameNameGet;
exports.getFileApiV1FilesIdGet = getFileApiV1FilesIdGet;
exports.getFilePageFigureApiV1FilesIdPageFiguresPageIndexFigureNameGet = getFilePageFigureApiV1FilesIdPageFiguresPageIndexFigureNameGet;
exports.getFilePageScreenshotApiV1FilesIdPageScreenshotsPageIndexGet = getFilePageScreenshotApiV1FilesIdPageScreenshotsPageIndexGet;
exports.getJobApiParsingJobJobIdGet = getJobApiParsingJobJobIdGet;
exports.getJobApiV1ExtractionJobsJobIdGet = getJobApiV1ExtractionJobsJobIdGet;
exports.getJobApiV1ParsingJobJobIdGet = getJobApiV1ParsingJobJobIdGet;
exports.getJobImageResultApiParsingJobJobIdResultImageNameGet = getJobImageResultApiParsingJobJobIdResultImageNameGet;
exports.getJobImageResultApiV1ParsingJobJobIdResultImageNameGet = getJobImageResultApiV1ParsingJobJobIdResultImageNameGet;
exports.getJobJsonRawResultApiParsingJobJobIdResultRawJsonGet = getJobJsonRawResultApiParsingJobJobIdResultRawJsonGet;
exports.getJobJsonRawResultApiV1ParsingJobJobIdResultRawJsonGet = getJobJsonRawResultApiV1ParsingJobJobIdResultRawJsonGet;
exports.getJobJsonResultApiParsingJobJobIdResultJsonGet = getJobJsonResultApiParsingJobJobIdResultJsonGet;
exports.getJobJsonResultApiV1ParsingJobJobIdResultJsonGet = getJobJsonResultApiV1ParsingJobJobIdResultJsonGet;
exports.getJobParametersApiParsingJobJobIdParametersGet = getJobParametersApiParsingJobJobIdParametersGet;
exports.getJobParametersApiV1ParsingJobJobIdParametersGet = getJobParametersApiV1ParsingJobJobIdParametersGet;
exports.getJobRawMdResultApiParsingJobJobIdResultRawMarkdownGet = getJobRawMdResultApiParsingJobJobIdResultRawMarkdownGet;
exports.getJobRawMdResultApiV1ParsingJobJobIdResultRawMarkdownGet = getJobRawMdResultApiV1ParsingJobJobIdResultRawMarkdownGet;
exports.getJobRawStructuredResultApiParsingJobJobIdResultRawStructuredGet = getJobRawStructuredResultApiParsingJobJobIdResultRawStructuredGet;
exports.getJobRawStructuredResultApiV1ParsingJobJobIdResultRawStructuredGet = getJobRawStructuredResultApiV1ParsingJobJobIdResultRawStructuredGet;
exports.getJobRawTextResultApiParsingJobJobIdResultPdfGet = getJobRawTextResultApiParsingJobJobIdResultPdfGet;
exports.getJobRawTextResultApiV1ParsingJobJobIdResultPdfGet = getJobRawTextResultApiV1ParsingJobJobIdResultPdfGet;
exports.getJobRawTextResultRawApiParsingJobJobIdResultRawTextGet = getJobRawTextResultRawApiParsingJobJobIdResultRawTextGet;
exports.getJobRawTextResultRawApiV1ParsingJobJobIdResultRawTextGet = getJobRawTextResultRawApiV1ParsingJobJobIdResultRawTextGet;
exports.getJobRawTextResultRawPdfApiParsingJobJobIdResultRawPdfGet = getJobRawTextResultRawPdfApiParsingJobJobIdResultRawPdfGet;
exports.getJobRawTextResultRawPdfApiV1ParsingJobJobIdResultRawPdfGet = getJobRawTextResultRawPdfApiV1ParsingJobJobIdResultRawPdfGet;
exports.getJobRawXlsxResultApiParsingJobJobIdResultXlsxGet = getJobRawXlsxResultApiParsingJobJobIdResultXlsxGet;
exports.getJobRawXlsxResultApiV1ParsingJobJobIdResultXlsxGet = getJobRawXlsxResultApiV1ParsingJobJobIdResultXlsxGet;
exports.getJobRawXlsxResultRawApiParsingJobJobIdResultRawXlsxGet = getJobRawXlsxResultRawApiParsingJobJobIdResultRawXlsxGet;
exports.getJobRawXlsxResultRawApiV1ParsingJobJobIdResultRawXlsxGet = getJobRawXlsxResultRawApiV1ParsingJobJobIdResultRawXlsxGet;
exports.getJobResultApiParsingJobJobIdResultMarkdownGet = getJobResultApiParsingJobJobIdResultMarkdownGet;
exports.getJobResultApiV1ExtractionJobsJobIdResultGet = getJobResultApiV1ExtractionJobsJobIdResultGet;
exports.getJobResultApiV1ParsingJobJobIdResultMarkdownGet = getJobResultApiV1ParsingJobJobIdResultMarkdownGet;
exports.getJobStructuredResultApiParsingJobJobIdResultStructuredGet = getJobStructuredResultApiParsingJobJobIdResultStructuredGet;
exports.getJobStructuredResultApiV1ParsingJobJobIdResultStructuredGet = getJobStructuredResultApiV1ParsingJobJobIdResultStructuredGet;
exports.getJobTextResultApiParsingJobJobIdResultTextGet = getJobTextResultApiParsingJobJobIdResultTextGet;
exports.getJobTextResultApiV1ParsingJobJobIdResultTextGet = getJobTextResultApiV1ParsingJobJobIdResultTextGet;
exports.getJobsApiV1JobsGet = getJobsApiV1JobsGet;
exports.getLatestRunFromUiApiV1ExtractionRunsLatestFromUiGet = getLatestRunFromUiApiV1ExtractionRunsLatestFromUiGet;
exports.getMetronomeDashboardApiV1BillingMetronomeDashboardGet = getMetronomeDashboardApiV1BillingMetronomeDashboardGet;
exports.getOrganizationApiV1OrganizationsOrganizationIdGet = getOrganizationApiV1OrganizationsOrganizationIdGet;
exports.getOrganizationUsageApiV1OrganizationsOrganizationIdUsageGet = getOrganizationUsageApiV1OrganizationsOrganizationIdUsageGet;
exports.getParsingHistoryResultApiParsingHistoryGet = getParsingHistoryResultApiParsingHistoryGet;
exports.getParsingHistoryResultApiV1ParsingHistoryGet = getParsingHistoryResultApiV1ParsingHistoryGet;
exports.getParsingJobDetailsApiParsingJobJobIdDetailsGet = getParsingJobDetailsApiParsingJobJobIdDetailsGet;
exports.getParsingJobDetailsApiV1ParsingJobJobIdDetailsGet = getParsingJobDetailsApiV1ParsingJobJobIdDetailsGet;
exports.getPipelineApiV1PipelinesPipelineIdGet = getPipelineApiV1PipelinesPipelineIdGet;
exports.getPipelineDataSourceStatusApiV1PipelinesPipelineIdDataSourcesDataSourceIdStatusGet = getPipelineDataSourceStatusApiV1PipelinesPipelineIdDataSourcesDataSourceIdStatusGet;
exports.getPipelineDocumentApiV1PipelinesPipelineIdDocumentsDocumentIdGet = getPipelineDocumentApiV1PipelinesPipelineIdDocumentsDocumentIdGet;
exports.getPipelineDocumentStatusApiV1PipelinesPipelineIdDocumentsDocumentIdStatusGet = getPipelineDocumentStatusApiV1PipelinesPipelineIdDocumentsDocumentIdStatusGet;
exports.getPipelineFileStatusApiV1PipelinesPipelineIdFilesFileIdStatusGet = getPipelineFileStatusApiV1PipelinesPipelineIdFilesFileIdStatusGet;
exports.getPipelineFileStatusCountsApiV1PipelinesPipelineIdFilesStatusCountsGet = getPipelineFileStatusCountsApiV1PipelinesPipelineIdFilesStatusCountsGet;
exports.getPipelineJobApiV1PipelinesPipelineIdJobsJobIdGet = getPipelineJobApiV1PipelinesPipelineIdJobsJobIdGet;
exports.getPipelineStatusApiV1PipelinesPipelineIdStatusGet = getPipelineStatusApiV1PipelinesPipelineIdStatusGet;
exports.getPlaygroundSessionApiV1PipelinesPipelineIdPlaygroundSessionGet = getPlaygroundSessionApiV1PipelinesPipelineIdPlaygroundSessionGet;
exports.getProjectApiV1ProjectsProjectIdGet = getProjectApiV1ProjectsProjectIdGet;
exports.getProjectUsageApiV1ProjectsProjectIdUsageGet = getProjectUsageApiV1ProjectsProjectIdUsageGet;
exports.getReportApiV1ReportsReportIdGet = getReportApiV1ReportsReportIdGet;
exports.getReportEventsApiV1ReportsReportIdEventsGet = getReportEventsApiV1ReportsReportIdEventsGet;
exports.getReportMetadataApiV1ReportsReportIdMetadataGet = getReportMetadataApiV1ReportsReportIdMetadataGet;
exports.getReportPlanApiV1ReportsReportIdPlanGet = getReportPlanApiV1ReportsReportIdPlanGet;
exports.getRetrieverApiV1RetrieversRetrieverIdGet = getRetrieverApiV1RetrieversRetrieverIdGet;
exports.getRunApiV1ExtractionRunsRunIdGet = getRunApiV1ExtractionRunsRunIdGet;
exports.getRunByJobIdApiV1ExtractionRunsByJobJobIdGet = getRunByJobIdApiV1ExtractionRunsByJobJobIdGet;
exports.getSupportedFileExtensionsApiParsingSupportedFileExtensionsGet = getSupportedFileExtensionsApiParsingSupportedFileExtensionsGet;
exports.getSupportedFileExtensionsApiV1ParsingSupportedFileExtensionsGet = getSupportedFileExtensionsApiV1ParsingSupportedFileExtensionsGet;
exports.getUserRoleApiV1OrganizationsOrganizationIdUsersRolesGet = getUserRoleApiV1OrganizationsOrganizationIdUsersRolesGet;
exports.importPipelineMetadataApiV1PipelinesPipelineIdMetadataPut = importPipelineMetadataApiV1PipelinesPipelineIdMetadataPut;
exports.listBatchesApiV1BetaBatchesGet = listBatchesApiV1BetaBatchesGet;
exports.listDataSinksApiV1DataSinksGet = listDataSinksApiV1DataSinksGet;
exports.listDataSourcesApiV1DataSourcesGet = listDataSourcesApiV1DataSourcesGet;
exports.listDeploymentsApiV1ProjectsProjectIdAgentsGet = listDeploymentsApiV1ProjectsProjectIdAgentsGet;
exports.listEmbeddingModelConfigsApiV1EmbeddingModelConfigsGet = listEmbeddingModelConfigsApiV1EmbeddingModelConfigsGet;
exports.listExtractRunsApiV1ExtractionRunsGet = listExtractRunsApiV1ExtractionRunsGet;
exports.listExtractionAgentsApiV1ExtractionExtractionAgentsGet = listExtractionAgentsApiV1ExtractionExtractionAgentsGet;
exports.listFilePageFiguresApiV1FilesIdPageFiguresPageIndexGet = listFilePageFiguresApiV1FilesIdPageFiguresPageIndexGet;
exports.listFilePageScreenshotsApiV1FilesIdPageScreenshotsGet = listFilePageScreenshotsApiV1FilesIdPageScreenshotsGet;
exports.listFilePagesFiguresApiV1FilesIdPageFiguresGet = listFilePagesFiguresApiV1FilesIdPageFiguresGet;
exports.listFilesApiV1FilesGet = listFilesApiV1FilesGet;
exports.listJobsApiV1ExtractionJobsGet = listJobsApiV1ExtractionJobsGet;
exports.listKeysApiV1ApiKeysGet = listKeysApiV1ApiKeysGet;
exports.listOrganizationUsersApiV1OrganizationsOrganizationIdUsersGet = listOrganizationUsersApiV1OrganizationsOrganizationIdUsersGet;
exports.listOrganizationsApiV1OrganizationsGet = listOrganizationsApiV1OrganizationsGet;
exports.listPipelineDataSourcesApiV1PipelinesPipelineIdDataSourcesGet = listPipelineDataSourcesApiV1PipelinesPipelineIdDataSourcesGet;
exports.listPipelineDocumentChunksApiV1PipelinesPipelineIdDocumentsDocumentIdChunksGet = listPipelineDocumentChunksApiV1PipelinesPipelineIdDocumentsDocumentIdChunksGet;
exports.listPipelineDocumentsApiV1PipelinesPipelineIdDocumentsGet = listPipelineDocumentsApiV1PipelinesPipelineIdDocumentsGet;
exports.listPipelineFiles2ApiV1PipelinesPipelineIdFiles2Get = listPipelineFiles2ApiV1PipelinesPipelineIdFiles2Get;
exports.listPipelineFilesApiV1PipelinesPipelineIdFilesGet = listPipelineFilesApiV1PipelinesPipelineIdFilesGet;
exports.listPipelineJobsApiV1PipelinesPipelineIdJobsGet = listPipelineJobsApiV1PipelinesPipelineIdJobsGet;
exports.listProjectsApiV1ProjectsGet = listProjectsApiV1ProjectsGet;
exports.listProjectsByUserApiV1OrganizationsOrganizationIdUsersUserIdProjectsGet = listProjectsByUserApiV1OrganizationsOrganizationIdUsersUserIdProjectsGet;
exports.listReportsApiV1ReportsListGet = listReportsApiV1ReportsListGet;
exports.listRetrieversApiV1RetrieversGet = listRetrieversApiV1RetrieversGet;
exports.listRolesApiV1OrganizationsOrganizationIdRolesGet = listRolesApiV1OrganizationsOrganizationIdRolesGet;
exports.listSupportedModelsApiV1EvalsModelsGet = listSupportedModelsApiV1EvalsModelsGet;
exports.metronomeWebhookApiV1BillingMetronomeWebhookPost = metronomeWebhookApiV1BillingMetronomeWebhookPost;
exports.paginatedListPipelineDocumentsApiV1PipelinesPipelineIdDocumentsPaginatedGet = paginatedListPipelineDocumentsApiV1PipelinesPipelineIdDocumentsPaginatedGet;
exports.readFileContentApiV1FilesIdContentGet = readFileContentApiV1FilesIdContentGet;
exports.removeUserFromProjectApiV1OrganizationsOrganizationIdUsersUserIdProjectsProjectIdDelete = removeUserFromProjectApiV1OrganizationsOrganizationIdUsersUserIdProjectsProjectIdDelete;
exports.removeUsersFromOrganizationApiV1OrganizationsOrganizationIdUsersMemberUserIdDelete = removeUsersFromOrganizationApiV1OrganizationsOrganizationIdUsersMemberUserIdDelete;
exports.restartReportApiV1ReportsReportIdRestartPost = restartReportApiV1ReportsReportIdRestartPost;
exports.retrieveApiV1RetrieversRetrieverIdRetrievePost = retrieveApiV1RetrieversRetrieverIdRetrievePost;
exports.runBatchJobsApiV1ExtractionJobsBatchPost = runBatchJobsApiV1ExtractionJobsBatchPost;
exports.runJobApiV1ExtractionJobsPost = runJobApiV1ExtractionJobsPost;
exports.runJobOnFileApiV1ExtractionJobsFilePost = runJobOnFileApiV1ExtractionJobsFilePost;
exports.runJobTestUserApiV1ExtractionJobsTestPost = runJobTestUserApiV1ExtractionJobsTestPost;
exports.runSearchApiV1PipelinesPipelineIdRetrievePost = runSearchApiV1PipelinesPipelineIdRetrievePost;
exports.screenshotApiParsingScreenshotPost = screenshotApiParsingScreenshotPost;
exports.screenshotApiV1ParsingScreenshotPost = screenshotApiV1ParsingScreenshotPost;
exports.searchAgentDataApiV1BetaAgentDataSearchPost = searchAgentDataApiV1BetaAgentDataSearchPost;
exports.searchPipelinesApiV1PipelinesGet = searchPipelinesApiV1PipelinesGet;
exports.setDefaultOrganizationApiV1OrganizationsDefaultPut = setDefaultOrganizationApiV1OrganizationsDefaultPut;
exports.stripeWebhookApiV1BillingWebhookPost = stripeWebhookApiV1BillingWebhookPost;
exports.suggestEditsEndpointApiV1ReportsReportIdSuggestEditsPost = suggestEditsEndpointApiV1ReportsReportIdSuggestEditsPost;
exports.syncDeploymentsApiV1ProjectsProjectIdAgentsSyncPost = syncDeploymentsApiV1ProjectsProjectIdAgentsSyncPost;
exports.syncFilesApiV1FilesSyncPut = syncFilesApiV1FilesSyncPut;
exports.syncPipelineApiV1PipelinesPipelineIdSyncPost = syncPipelineApiV1PipelinesPipelineIdSyncPost;
exports.syncPipelineDataSourceApiV1PipelinesPipelineIdDataSourcesDataSourceIdSyncPost = syncPipelineDataSourceApiV1PipelinesPipelineIdDataSourcesDataSourceIdSyncPost;
exports.updateAgentDataApiV1BetaAgentDataItemIdPut = updateAgentDataApiV1BetaAgentDataItemIdPut;
exports.updateChatAppApiV1AppsIdPut = updateChatAppApiV1AppsIdPut;
exports.updateDataSinkApiV1DataSinksDataSinkIdPut = updateDataSinkApiV1DataSinksDataSinkIdPut;
exports.updateDataSourceApiV1DataSourcesDataSourceIdPut = updateDataSourceApiV1DataSourcesDataSourceIdPut;
exports.updateEmbeddingModelConfigApiV1EmbeddingModelConfigsEmbeddingModelConfigIdPut = updateEmbeddingModelConfigApiV1EmbeddingModelConfigsEmbeddingModelConfigIdPut;
exports.updateExistingApiKeyApiV1ApiKeysApiKeyIdPut = updateExistingApiKeyApiV1ApiKeysApiKeyIdPut;
exports.updateExistingPipelineApiV1PipelinesPipelineIdPut = updateExistingPipelineApiV1PipelinesPipelineIdPut;
exports.updateExistingProjectApiV1ProjectsProjectIdPut = updateExistingProjectApiV1ProjectsProjectIdPut;
exports.updateExtractionAgentApiV1ExtractionExtractionAgentsExtractionAgentIdPut = updateExtractionAgentApiV1ExtractionExtractionAgentsExtractionAgentIdPut;
exports.updateOrganizationApiV1OrganizationsOrganizationIdPut = updateOrganizationApiV1OrganizationsOrganizationIdPut;
exports.updatePipelineDataSourceApiV1PipelinesPipelineIdDataSourcesDataSourceIdPut = updatePipelineDataSourceApiV1PipelinesPipelineIdDataSourcesDataSourceIdPut;
exports.updatePipelineFileApiV1PipelinesPipelineIdFilesFileIdPut = updatePipelineFileApiV1PipelinesPipelineIdFilesFileIdPut;
exports.updateReportApiV1ReportsReportIdPatch = updateReportApiV1ReportsReportIdPatch;
exports.updateReportMetadataApiV1ReportsReportIdPost = updateReportMetadataApiV1ReportsReportIdPost;
exports.updateReportPlanApiV1ReportsReportIdPlanPatch = updateReportPlanApiV1ReportsReportIdPlanPatch;
exports.updateRetrieverApiV1RetrieversRetrieverIdPut = updateRetrieverApiV1RetrieversRetrieverIdPut;
exports.uploadFileApiParsingUploadPost = uploadFileApiParsingUploadPost;
exports.uploadFileApiV1FilesPost = uploadFileApiV1FilesPost;
exports.uploadFileApiV1ParsingUploadPost = uploadFileApiV1ParsingUploadPost;
exports.uploadFileFromUrlApiV1FilesUploadFromUrlPut = uploadFileFromUrlApiV1FilesUploadFromUrlPut;
exports.upsertBatchPipelineDocumentsApiV1PipelinesPipelineIdDocumentsPut = upsertBatchPipelineDocumentsApiV1PipelinesPipelineIdDocumentsPut;
exports.upsertDataSinkApiV1DataSinksPut = upsertDataSinkApiV1DataSinksPut;
exports.upsertDataSourceApiV1DataSourcesPut = upsertDataSourceApiV1DataSourcesPut;
exports.upsertEmbeddingModelConfigApiV1EmbeddingModelConfigsPut = upsertEmbeddingModelConfigApiV1EmbeddingModelConfigsPut;
exports.upsertOrganizationApiV1OrganizationsPut = upsertOrganizationApiV1OrganizationsPut;
exports.upsertPipelineApiV1PipelinesPut = upsertPipelineApiV1PipelinesPut;
exports.upsertProjectApiV1ProjectsPut = upsertProjectApiV1ProjectsPut;
exports.upsertRetrieverApiV1RetrieversPut = upsertRetrieverApiV1RetrieversPut;
exports.validateDataSinkConnectionApiV1ValidateIntegrationsValidateDataSinkConnectionPost = validateDataSinkConnectionApiV1ValidateIntegrationsValidateDataSinkConnectionPost;
exports.validateDataSourceConnectionApiV1ValidateIntegrationsValidateDataSourceConnectionPost = validateDataSourceConnectionApiV1ValidateIntegrationsValidateDataSourceConnectionPost;
exports.validateEmbeddingConnectionApiV1ValidateIntegrationsValidateEmbeddingConnectionPost = validateEmbeddingConnectionApiV1ValidateIntegrationsValidateEmbeddingConnectionPost;
exports.validateExtractionSchemaApiV1ExtractionExtractionAgentsSchemaValidationPost = validateExtractionSchemaApiV1ExtractionExtractionAgentsSchemaValidationPost;
