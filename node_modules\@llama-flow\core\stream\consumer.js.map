{"version": 3, "file": "consumer.js", "names": ["stream: ReadableStream | WorkflowStream", "stream: ReadableStream<T> | WorkflowStream", "events: WorkflowEventData<any>[]", "event: WorkflowEventData<any>"], "sources": ["../src/stream/consumer.ts"], "sourcesContent": ["import { type WorkflowEventData, WorkflowStream } from \"@llama-flow/core\";\n\nconst noopStream = new WritableStream({\n  write: () => {\n    // no-op\n  },\n});\n\n/**\n * A no-op function that consumes a stream of events and does nothing with them.\n *\n * Do not collect the raw stream from `workflow.createContext()`\n * or `getContext()`, it's infinite and will never finish\n *\n * @deprecated uss `await stream.toArray()` instead\n */\nexport const nothing = async (\n  stream: ReadableStream | WorkflowStream,\n): Promise<void> => {\n  await stream.pipeTo(noopStream);\n};\n\n/**\n * Collects all events from a stream and returns them as an array.\n *\n * Do not collect the raw stream from `workflow.createContext()`\n * or getContext()`, it's infinite and will never finish.\n *\n * @deprecated uss `await stream.toArray()` instead\n */\nexport const collect = async <T extends WorkflowEventData<any>>(\n  stream: ReadableStream<T> | WorkflowStream,\n): Promise<WorkflowEventData<any>[]> => {\n  const events: WorkflowEventData<any>[] = [];\n  await stream.pipeTo(\n    new WritableStream({\n      write: (event: WorkflowEventData<any>) => {\n        events.push(event);\n      },\n    }),\n  );\n  return events;\n};\n"], "mappings": ";;;AAEA,MAAM,aAAa,IAAI,eAAe,EACpC,OAAO,MAAM,CAEZ,EACF;;;;;;;;;AAUD,MAAa,UAAU,OACrBA,WACkB;AAClB,OAAM,OAAO,OAAO,WAAW;AAChC;;;;;;;;;AAUD,MAAa,UAAU,OACrBC,WACsC;CACtC,MAAMC,SAAmC,CAAE;AAC3C,OAAM,OAAO,OACX,IAAI,eAAe,EACjB,OAAO,CAACC,UAAkC;AACxC,SAAO,KAAK,MAAM;CACnB,EACF,GACF;AACD,QAAO;AACR"}