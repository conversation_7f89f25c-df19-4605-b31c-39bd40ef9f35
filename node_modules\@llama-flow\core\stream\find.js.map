{"version": 3, "file": "find.js", "names": ["stream: ReadableStream<WorkflowEventData<any>> | WorkflowStream", "event: WorkflowEvent<T>"], "sources": ["../src/stream/find.ts"], "sourcesContent": ["import {\n  type WorkflowEvent,\n  type WorkflowEventData,\n  WorkflowStream,\n} from \"@llama-flow/core\";\n\n/**\n * Consume a stream of events with a given event and time.\n */\nexport async function find<T>(\n  stream: ReadableStream<WorkflowEventData<any>> | WorkflowStream,\n  event: WorkflowEvent<T>,\n): Promise<WorkflowEventData<T>> {\n  const reader = stream.getReader();\n  let result = await reader.read();\n  while (!result.done) {\n    const ev = result.value;\n    if (event.include(ev)) {\n      reader.releaseLock();\n      return ev;\n    }\n    result = await reader.read();\n  }\n  throw new Error(`Event ${event.toString()} not found`);\n}\n"], "mappings": ";;;;;;AASA,eAAsB,KACpBA,QACAC,OAC+B;CAC/B,MAAM,SAAS,OAAO,WAAW;CACjC,IAAI,SAAS,MAAM,OAAO,MAAM;AAChC,SAAQ,OAAO,MAAM;EACnB,MAAM,KAAK,OAAO;AAClB,MAAI,MAAM,QAAQ,GAAG,EAAE;AACrB,UAAO,aAAa;AACpB,UAAO;EACR;AACD,WAAS,MAAM,OAAO,MAAM;CAC7B;AACD,OAAM,IAAI,OAAO,QAAQ,MAAM,UAAU,CAAC;AAC3C"}