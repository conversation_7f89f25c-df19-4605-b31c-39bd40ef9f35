import { Workflow as Workflow$1, WorkflowEvent, WorkflowEventData } from "@llamaindex/workflow-core";

//#region src/middleware/snapshot.d.ts
declare const request: <T>(event: WorkflowEvent<T>, reason?: any) => WorkflowEventData<WorkflowEvent<T>>;
type SnapshotFn = () => Promise<[requestEvents: WorkflowEvent<any>[], serializable: SnapshotData]>;
type SnapshotWorkflowContext<Workflow extends Workflow$1> = ReturnType<Workflow["createContext"]> & {
  onRequest: <Event extends WorkflowEvent<any>>(event: Event, callback: (reason: any) => void | Promise<void>) => () => void;
  /**
   * Snapshot will lock the context and wait for there is no pending event.
   *
   * This is useful when you want to take a current snapshot of the workflow
   *
   */
  snapshot: SnapshotFn;
};
type WithSnapshotWorkflow<Workflow extends Workflow$1> = Omit<Workflow, "createContext"> & {
  createContext: () => SnapshotWorkflowContext<Workflow>;
  resume: (data: any[], serializable: Omit<SnapshotData, "unrecoverableQueue">) => SnapshotWorkflowContext<Workflow>;
};
interface SnapshotData {
  queue: [data: any, id: number][];
  /**
   * These events are not recoverable because they are not in any handler
   *
   * This is useful when you have `messageEvent` but you don't have any handler for it
   */
  unrecoverableQueue: [data: any, id: number][];
  /**
   * This is the version of the snapshot
   *
   * Change any of the handlers will change the version
   */
  version: string;
  missing: number[];
}
declare function withSnapshot<Workflow extends Workflow$1>(workflow: Workflow): WithSnapshotWorkflow<Workflow>;
//#endregion
export { SnapshotFn, request, withSnapshot };
//# sourceMappingURL=snapshot.d.cts.map