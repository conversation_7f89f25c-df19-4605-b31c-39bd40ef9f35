//#region src/async-context/index.browser.d.ts
declare const createAsyncContext: <T>() => {
  /**
   * You must call `getContext()` in the top level of the workflow,
   *  otherwise we will lose the async context of the workflow.
   *
   * @example
   * ```
   * workflow.handle([startEvent], async () => {
   *   const { stream } = getContext(); // ✅ this is ok
   *   await fetchData();
   * });
   *
   * workflow.handle([startEvent], async () => {
   *   await fetchData();
   *   const { stream } = getContext(); // ❌ this is not ok
   *   // we have no way
   *   to know this code was originally part of the workflow
   *   // w/o AsyncContext
   * });
   * ```
   */
  getStore: () => T | null;
  run<R>(store: T, fn: () => R): R;
};
//#endregion
export { createAsyncContext };
//# sourceMappingURL=index.browser.d.cts.map