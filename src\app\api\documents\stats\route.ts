import { NextResponse } from 'next/server';
import documentManager from '@/lib/document-manager';

export async function GET() {
  try {
    await documentManager.initialize();
    const stats = await documentManager.getStats();
    const documents = documentManager.getAllDocuments();

    return NextResponse.json({
      stats,
      documents: documents.map(doc => ({
        id: doc.id,
        filename: doc.filename,
        originalName: doc.originalName,
        size: doc.size,
        uploadDate: doc.uploadDate,
        status: doc.status,
        errorMessage: doc.errorMessage,
      })),
    });
  } catch (error) {
    console.error('Failed to get document stats:', error);
    return NextResponse.json(
      { error: 'Failed to get document statistics' },
      { status: 500 }
    );
  }
}
