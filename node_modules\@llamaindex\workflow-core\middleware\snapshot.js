import { createSubscribable, isPromiseLike } from "./utils-DhleAZ_l.js";
import { WorkflowStream, eventSource, workflowEvent } from "@llamaindex/workflow-core";

//#region src/middleware/snapshot/stable-hash.ts
function createStableHash() {
	const table = /* @__PURE__ */ new WeakMap();
	let counter = 0;
	return function stableHash(arg) {
		const type = typeof arg;
		const constructor = arg && arg.constructor;
		const isDate = constructor == Date;
		if (Object(arg) === arg && !isDate && constructor != RegExp) {
			let result = table.get(arg);
			if (result) return result;
			result = ++counter + "~";
			table.set(arg, result);
			let index;
			if (constructor == Array) {
				result = "@";
				for (index = 0; index < arg.length; index++) result += stableHash(arg[index]) + ",";
				table.set(arg, result);
			} else if (constructor == Object) {
				result = "#";
				const keys = Object.keys(arg).sort();
				while ((index = keys.pop()) !== void 0) if (arg[index] !== void 0) result += index + ":" + stableHash(arg[index]) + ",";
				table.set(arg, result);
			}
			return result;
		}
		if (isDate) return arg.toJSON();
		if (type == "symbol") return arg.toString();
		return type == "string" ? JSON.stringify(arg) : "" + arg;
	};
}

//#endregion
//#region src/middleware/snapshot.ts
/**
* @internal We don't want to expose this special event to the user
*/
const snapshotEvent = workflowEvent();
const reasonWeakMap = /* @__PURE__ */ new WeakMap();
const noop = () => {};
const request = (event, reason) => {
	const ev = snapshotEvent.with(event);
	reasonWeakMap.set(ev, reason);
	return ev;
};
function withSnapshot(workflow) {
	const requests = createSubscribable();
	const pendingRequestSetMap = /* @__PURE__ */ new WeakMap();
	const getPendingRequestSet = (context) => {
		if (!pendingRequestSetMap.has(context)) pendingRequestSetMap.set(context, /* @__PURE__ */ new Set());
		return pendingRequestSetMap.get(context);
	};
	const stableHash = createStableHash();
	/**
	* This is to indicate the version of the snapshot
	*
	* It happens when you modify the workflow, all old snapshots should be invalidated
	*/
	const versionObj = [];
	const getVersion = () => stableHash(versionObj);
	const registeredEvents = /* @__PURE__ */ new Set();
	const isContextLockedWeakMap = /* @__PURE__ */ new WeakMap();
	const isContextLocked = (context) => {
		return isContextLockedWeakMap.get(context) === true;
	};
	const isContextSnapshotReadyWeakSet = /* @__PURE__ */ new WeakSet();
	const isContextSnapshotReady = (context) => {
		return isContextSnapshotReadyWeakSet.has(context);
	};
	const contextEventQueueWeakMap = /* @__PURE__ */ new WeakMap();
	const handlerContextSetWeakMap = /* @__PURE__ */ new WeakMap();
	const collectedEventHandlerContextWeakMap = /* @__PURE__ */ new WeakMap();
	const createSnapshotFn = (context) => {
		return async function snapshotHandler() {
			if (isContextLocked(context)) throw new Error("Context is already locked, you cannot snapshot a same context twice");
			isContextLockedWeakMap.set(context, true);
			const handlerContexts = handlerContextSetWeakMap.get(context);
			await Promise.all([...handlerContexts].filter((context$1) => context$1.async).map((context$1) => context$1.pending));
			const collectedEvents = contextEventQueueWeakMap.get(context);
			const requestEvents = collectedEvents.filter((event) => snapshotEvent.include(event)).map((event) => event.data);
			const queue = collectedEvents.filter((event) => !snapshotEvent.include(event));
			isContextSnapshotReadyWeakSet.add(context);
			if (requestEvents.some((event) => !registeredEvents.has(event))) console.warn("request event is not registered in the workflow");
			const serializable = {
				queue: queue.filter((event) => eventCounterWeakMap.has(eventSource(event))).map((event) => [event.data, getEventCounter(eventSource(event))]),
				unrecoverableQueue: queue.filter((event) => !eventCounterWeakMap.has(eventSource(event))).map((event) => [event.data, getEventCounter(eventSource(event))]),
				version: getVersion(),
				missing: requestEvents.filter((event) => eventCounterWeakMap.has(event)).map((event) => getEventCounter(event))
			};
			return [requestEvents, serializable];
		};
	};
	let counter = 0;
	const eventCounterWeakMap = /* @__PURE__ */ new WeakMap();
	const counterEventMap = /* @__PURE__ */ new Map();
	const getEventCounter = (event) => {
		if (!eventCounterWeakMap.has(event)) eventCounterWeakMap.set(event, counter++);
		return eventCounterWeakMap.get(event);
	};
	const getCounterEvent = (counter$1) => {
		if (!counterEventMap.has(counter$1)) throw new Error(`event counter ${counter$1} not found`);
		return counterEventMap.get(counter$1);
	};
	function initContext(context) {
		handlerContextSetWeakMap.set(context, /* @__PURE__ */ new Set());
		contextEventQueueWeakMap.set(context, []);
		context.__internal__call_send_event.subscribe((eventData, handlerContext) => {
			contextEventQueueWeakMap.get(context).push(eventData);
			if (isContextLocked(context)) {
				if (isContextSnapshotReady(context)) console.warn("snapshot is already ready, sendEvent after snapshot is not allowed");
				if (!collectedEventHandlerContextWeakMap.has(eventData)) collectedEventHandlerContextWeakMap.set(eventData, /* @__PURE__ */ new Set());
				collectedEventHandlerContextWeakMap.get(eventData).add(handlerContext);
			}
		});
		context.__internal__call_context.subscribe((handlerContext, next) => {
			if (isContextLocked(context)) {
				handlerContext.handler = noop;
				next(handlerContext);
			} else {
				const queue = contextEventQueueWeakMap.get(context);
				handlerContext.inputs.forEach((input) => {
					queue.splice(queue.indexOf(input), 1);
				});
				const originalHandler = handlerContext.handler;
				const pendingRequests = getPendingRequestSet(context);
				const isPendingTask = pendingRequests.size !== 0;
				if (isPendingTask) handlerContext.handler = async (...events) => {
					return Promise.all([...pendingRequests]).finally(() => {
						return originalHandler(...events);
					});
				};
				handlerContextSetWeakMap.get(context).add(handlerContext);
				next(handlerContext);
			}
		});
	}
	return {
		...workflow,
		handle: (events, handler) => {
			versionObj.push([events.map(getEventCounter), handler]);
			events.forEach((event) => {
				counterEventMap.set(getEventCounter(event), event);
			});
			events.forEach((event) => {
				registeredEvents.add(event);
			});
			return workflow.handle(events, handler);
		},
		resume(data, serializable) {
			const events = data.map((d, i) => getCounterEvent(serializable.missing[i]).with(d));
			const context = workflow.createContext();
			initContext(context);
			const stream = context.stream;
			context.sendEvent(...serializable.queue.map(([data$1, id]) => {
				const event = getCounterEvent(id);
				return event.with(data$1);
			}));
			context.sendEvent(...events);
			let lazyInitStream = null;
			const snapshotFn = createSnapshotFn(context);
			return {
				...context,
				snapshot: snapshotFn,
				onRequest: (event, callback) => requests.subscribe((ev, reason) => {
					if (ev === event) return callback(reason);
				}),
				get stream() {
					if (!lazyInitStream) lazyInitStream = stream.pipeThrough(new TransformStream({ transform: (event, controller) => {
						if (snapshotEvent.include(event)) {
							const data$1 = event.data;
							requests.publish(data$1, reasonWeakMap.get(event));
						} else controller.enqueue(event);
					} }));
					return lazyInitStream;
				}
			};
		},
		createContext() {
			const context = workflow.createContext();
			initContext(context);
			const stream = context.stream;
			let lazyInitStream = null;
			const snapshotFn = createSnapshotFn(context);
			return {
				...context,
				snapshot: snapshotFn,
				onRequest: (event, callback) => requests.subscribe((ev, reason) => {
					if (ev === event) return callback(reason);
				}),
				get stream() {
					if (!lazyInitStream) lazyInitStream = stream.pipeThrough(new TransformStream({ transform: (event, controller) => {
						if (snapshotEvent.include(event)) {
							const data = event.data;
							const results = requests.publish(data, reasonWeakMap.get(event));
							const pendingRequests = getPendingRequestSet(context);
							results.filter(isPromiseLike).forEach((promise) => {
								const task = promise.then(() => {
									pendingRequests.delete(task);
								});
								pendingRequests.add(task);
							});
						} else controller.enqueue(event);
					} }));
					return lazyInitStream;
				}
			};
		}
	};
}

//#endregion
export { request, withSnapshot };
//# sourceMappingURL=snapshot.js.map