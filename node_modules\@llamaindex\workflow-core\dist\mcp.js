import { runWorkflow } from "./run-25vavBMH.js";
import { createAsyncContext } from "@llamaindex/workflow-core/async-context";
import { z } from "zod";

//#region src/mcp.ts
const requestHandlerExtraAsyncLocalStorage = createAsyncContext();
const getReqHandlerExtra = () => {
	const extra = requestHandlerExtraAsyncLocalStorage.getStore();
	if (!extra) throw new Error("Request handler extra not set");
	return extra;
};
function mcpTool(workflow, start, stop) {
	return async (args, extra) => requestHandlerExtraAsyncLocalStorage.run(extra, async () => {
		const { data } = await runWorkflow(workflow, start.with(args), stop);
		return data;
	});
}

//#endregion
export { getReqHandlerExtra, mcpTool };
//# sourceMappingURL=mcp.js.map