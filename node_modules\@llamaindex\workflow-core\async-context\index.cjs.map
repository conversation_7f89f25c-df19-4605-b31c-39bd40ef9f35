{"version": 3, "file": "index.cjs", "names": ["AsyncLocalStorage", "store: T", "fn: () => R"], "sources": ["../src/async-context/index.ts"], "sourcesContent": ["import { AsyncLocalStorage } from \"node:async_hooks\";\n\nexport const createAsyncContext = <T>() => {\n  const als = new AsyncLocalStorage<T>();\n  return {\n    getStore: () => als.getStore(),\n    run<R>(store: T, fn: () => R) {\n      return als.run(store, fn);\n    },\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAa,qBAAqB,MAAS;CACzC,MAAM,MAAM,IAAIA;AAChB,QAAO;EACL,UAAU,MAAM,IAAI,UAAU;EAC9B,IAAOC,OAAUC,IAAa;AAC5B,UAAO,IAAI,IAAI,OAAO,GAAG;EAC1B;CACF;AACF"}