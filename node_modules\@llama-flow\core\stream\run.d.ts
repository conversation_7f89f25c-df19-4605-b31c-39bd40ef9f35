import * as _llama_flow_core0 from "@llama-flow/core";
import { Workflow, WorkflowEvent, WorkflowEventData } from "@llama-flow/core";

//#region src/stream/run.d.ts
/**
* Runs a workflow with the provided events and returns the resulting stream.
*
* ```ts
* const events = await run(workflow, startEvent.with("42")).toArray();
* ```
*
*/
/**
 * Runs a workflow with the provided events and returns the resulting stream.
 *
 * ```ts
 * const events = await run(workflow, startEvent.with("42")).toArray();
 * ```
 *
 */
declare function run(workflow: Workflow, events: WorkflowEventData<any> | WorkflowEventData<any>[]): _llama_flow_core0.WorkflowStream<WorkflowEventData<any>>;
/**
 * Runs a workflow with a specified input event and returns the first matching event of the specified output type.
 *
 * @deprecated Use `stream.until().toArray()` for a more idiomatic approach.
 * @example
 * ```ts
 * const result = await runWorkflow(workflow, startEvent.with("42"), stopEvent);
 * console.log(`Result: ${result.data === 1 ? 'positive' : 'negative'}`);
 * ```
 */
declare function runWorkflow<Input, Output>(workflow: Workflow, inputEvent: WorkflowEventData<Input>, outputEvent: WorkflowEvent<Output>): Promise<WorkflowEventData<Output>>;
/**
 * Runs a workflow with a specified input event and collects all events until a specified output event is encountered.
 * Returns an array containing all events including the final output event.
 *
 * @deprecated Use `stream.until().toArray()` for a more idiomatic approach.
 * @example
 * ```ts
 * const allEvents = await runAndCollect(workflow, startEvent.with("42"), stopEvent);
 * const finalEvent = allEvents[allEvents.length - 1];
 * console.log(`Result: ${finalEvent.data === 1 ? 'positive' : 'negative'}`);
 * ```
 */
declare function runAndCollect<Input, Output>(workflow: Workflow, inputEvent: WorkflowEventData<Input>, outputEvent: WorkflowEvent<Output>): Promise<WorkflowEventData<any>[]>;
/**
 * Runs a workflow with a specified input event and returns an async iterable stream of all events
 * until a specified output event is encountered.
 *
 * This allows processing events one by one without collecting them all upfront.
 *
 * @deprecated Use `stream.until().toArray()` for a more idiomatic approach.
 * @example
 * ```ts
 * const eventStream = runStream(workflow, startEvent.with("42"), stopEvent);
 * for await (const event of eventStream) {
 *   console.log(`Processing event: ${event}`);
 *   // Do something with each event as it arrives
 * }
 * ```
 */
declare function runStream<Input, Output>(workflow: Workflow, inputEvent: WorkflowEventData<Input>, outputEvent: WorkflowEvent<Output>): AsyncIterable<WorkflowEventData<any>>;

//#endregion
export { run, runAndCollect, runStream, runWorkflow };
//# sourceMappingURL=run.d.ts.map