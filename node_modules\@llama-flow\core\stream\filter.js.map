{"version": 3, "file": "filter.js", "names": ["stream: ReadableStream<Event> | WorkflowStream<Event>", "cond: (event: Event) => event is Final"], "sources": ["../src/stream/filter.ts"], "sourcesContent": ["import type {\n  WorkflowEvent,\n  WorkflowEventData,\n  WorkflowStream,\n} from \"@llama-flow/core\";\n\n/**\n * @deprecated use `stream.filter` instead. This will be removed in the next minor version.\n */\nexport function filter<\n  Event extends WorkflowEventData<any>,\n  Final extends Event,\n>(\n  stream: ReadableStream<Event> | WorkflowStream<Event>,\n  cond: (event: Event) => event is Final,\n): ReadableStream<Final> | WorkflowStream<Final> {\n  return stream.pipeThrough(\n    new TransformStream<Event, Final>({\n      transform(event, controller) {\n        if (cond(event)) {\n          controller.enqueue(event);\n        }\n      },\n    }),\n  );\n}\n"], "mappings": ";;;;AASA,SAAgB,OAIdA,QACAC,MAC+C;AAC/C,QAAO,OAAO,YACZ,IAAI,gBAA8B,EAChC,UAAU,OAAO,YAAY;AAC3B,MAAI,KAAK,MAAM,CACb,YAAW,QAAQ,MAAM;CAE5B,EACF,GACF;AACF"}