import { WorkflowEvent, WorkflowEventData, WorkflowStream } from "@llama-flow/core";

//#region src/stream/find.d.ts
/**
* Consume a stream of events with a given event and time.
*/
/**
 * Consume a stream of events with a given event and time.
 */
declare function find<T>(stream: ReadableStream<WorkflowEventData<any>> | WorkflowStream, event: WorkflowEvent<T>): Promise<WorkflowEventData<T>>;

//#endregion
export { find };
//# sourceMappingURL=find.d.ts.map