import { isPromiseLike } from "./utils-DuU-pzVd.js";
import { WorkflowStream, getContext } from "@llama-flow/core";

//#region src/middleware/trace-events/create-handler-decorator.ts
const namespace = "decorator";
let counter = 0;
const decoratorRegistry = new Map();
function createHandlerDecorator(config) {
	const uid = `${namespace}:${counter++}`;
	decoratorRegistry.set(uid, {
		handlers: new WeakSet(),
		debugLabel: config.debugLabel ?? uid,
		getInitialValue: config.getInitialValue,
		onAfterHandler: config.onAfterHandler,
		onBeforeHandler: config.onBeforeHandler
	});
	return function(handler) {
		decoratorRegistry.get(uid).handlers.add(handler);
		return handler;
	};
}

//#endregion
//#region src/middleware/trace-events/run-once.ts
const noop = function noop$1() {};
const runOnce = createHandlerDecorator({
	debugLabel: "onceHook",
	getInitialValue: () => false,
	onBeforeHandler: (handler, _, tracked) => tracked ? noop : handler,
	onAfterHandler: () => true
});

//#endregion
//#region src/middleware/trace-events.ts
const tracingWeakMap = new WeakMap();
const contextTraceWeakMap = new WeakMap();
const eventToHandlerContextWeakMap = new WeakMap();
function getEventOrigins(eventData, context = getContext()) {
	let currentContext = eventToHandlerContextWeakMap.get(eventData);
	if (!currentContext) throw new Error("Event context not found, this should not happen. Please report this issue with a reproducible example.");
	do {
		const workflowContext = contextTraceWeakMap.get(currentContext.prev);
		if (workflowContext === context) return currentContext.inputs;
		currentContext = currentContext.prev;
	} while (currentContext.prev);
	throw new Error("Event context not found, this should not happen. Please report this issue with a reproducible example.");
}
function withTraceEvents(workflow) {
	return {
		...workflow,
		substream: (eventData, stream) => {
			const rootContext = eventToHandlerContextWeakMap.get(eventData);
			return stream.pipeThrough(new TransformStream({ transform(eventData$1, controller) {
				let isInSameContext = false;
				let currentEventContext = eventToHandlerContextWeakMap.get(eventData$1);
				while (currentEventContext) {
					if (currentEventContext === rootContext) {
						isInSameContext = true;
						break;
					}
					currentEventContext = currentEventContext.prev;
				}
				if (isInSameContext) controller.enqueue(eventData$1);
			} }));
		},
		handle: (accept, handler) => {
			workflow.handle(accept, handler);
			return { get handler() {
				return handler;
			} };
		},
		createContext() {
			const context = workflow.createContext();
			tracingWeakMap.set(context, new WeakMap());
			context.__internal__call_send_event.subscribe((event, handlerContext) => {
				eventToHandlerContextWeakMap.set(event, handlerContext);
			});
			context.__internal__call_context.subscribe((handlerContext, next) => {
				handlerContext.inputs.forEach((input) => {
					if (!eventToHandlerContextWeakMap.has(input)) console.warn("unregistered event detected");
					eventToHandlerContextWeakMap.set(input, handlerContext);
				});
				const inputEvents = handlerContext.inputEvents;
				const handlersWeakMap = tracingWeakMap.get(context);
				if (!handlersWeakMap.has(inputEvents)) handlersWeakMap.set(inputEvents, new WeakMap());
				const handlerWeakMap = handlersWeakMap.get(inputEvents);
				const originalHandler = handlerContext.handler;
				let finalHandler = originalHandler;
				let handlerMiddleware;
				if (!handlerWeakMap) throw new Error("Handler context is not defined, this should not happen. Please report this issue with a reproducible example.");
				const tracingContext = handlerWeakMap.get(originalHandler) ?? {};
				if (!handlerWeakMap.has(originalHandler)) handlerWeakMap.set(originalHandler, tracingContext);
				const onAfterHandlers = [];
				const onBeforeHandlers = [];
				handlerMiddleware = (...args) => {
					const context$1 = getContext();
					contextTraceWeakMap.set(handlerContext, context$1);
					const result = onBeforeHandlers.reduce((next$1, cb) => {
						return cb(next$1);
					}, finalHandler)(...args);
					if (isPromiseLike(result)) return result.then((result$1) => {
						onAfterHandlers.forEach((cb) => {
							cb();
						});
						return result$1;
					});
					else {
						onAfterHandlers.forEach((cb) => {
							cb();
						});
						return result;
					}
				};
				[...decoratorRegistry].filter(([, { handlers }]) => handlers.has(handlerContext.handler)).forEach(([name, { getInitialValue, onAfterHandler, onBeforeHandler }]) => {
					if (!tracingContext[name]) tracingContext[name] = getInitialValue();
					onBeforeHandlers.push((next$1) => onBeforeHandler(next$1, handlerContext, tracingContext[name]));
					onAfterHandlers.push(() => {
						tracingContext[name] = onAfterHandler(tracingContext[name]);
					});
				});
				next({
					...handlerContext,
					handler: handlerMiddleware
				});
			});
			return context;
		}
	};
}

//#endregion
export { createHandlerDecorator, getEventOrigins, runOnce, withTraceEvents };
//# sourceMappingURL=trace-events.js.map