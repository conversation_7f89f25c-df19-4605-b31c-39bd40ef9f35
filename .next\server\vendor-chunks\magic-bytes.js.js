"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/magic-bytes.js";
exports.ids = ["vendor-chunks/magic-bytes.js"];
exports.modules = {

/***/ "(rsc)/./node_modules/magic-bytes.js/dist/index.js":
/*!***************************************************!*\
  !*** ./node_modules/magic-bytes.js/dist/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.register = exports.filetypeextension = exports.filetypemime = exports.filetypename = exports.filetypeinfo = void 0;\nconst pattern_tree_1 = __webpack_require__(/*! ./model/pattern-tree */ \"(rsc)/./node_modules/magic-bytes.js/dist/model/pattern-tree.js\");\nconst toHex_1 = __webpack_require__(/*! ./model/toHex */ \"(rsc)/./node_modules/magic-bytes.js/dist/model/toHex.js\");\nconst patternTree = pattern_tree_1.createTree();\nconst filetypeinfo = (bytes) => {\n    let tree = patternTree;\n    for (const k of Object.keys(tree.offset)) {\n        const offset = toHex_1.fromHex(k);\n        const offsetExceedsFile = offset >= bytes.length;\n        if (offsetExceedsFile) {\n            continue;\n        }\n        const node = patternTree.offset[k];\n        const guessed = walkTree(offset, bytes, node);\n        if (guessed.length > 0) {\n            return guessed;\n        }\n    }\n    if (tree.noOffset === null) {\n        return [];\n    }\n    return walkTree(0, bytes, tree.noOffset);\n};\nexports.filetypeinfo = filetypeinfo;\nconst walkTree = (index, bytes, node) => {\n    let step = node;\n    let guessFile = [];\n    while (true) {\n        const currentByte = toHex_1.toHex(bytes[index]);\n        if (step.bytes[\"?\"] && !step.bytes[currentByte]) {\n            step = step.bytes[\"?\"];\n        }\n        else {\n            step = step.bytes[currentByte];\n        }\n        if (!step) {\n            return guessFile;\n        }\n        if (step && step.matches) {\n            guessFile = step.matches.slice(0);\n        }\n        index += 1;\n    }\n};\nexports[\"default\"] = exports.filetypeinfo;\nconst filetypename = (bytes) => exports.filetypeinfo(bytes).map((e) => e.typename);\nexports.filetypename = filetypename;\nconst filetypemime = (bytes) => exports.filetypeinfo(bytes)\n    .map((e) => (e.mime ? e.mime : null))\n    .filter((x) => x !== null);\nexports.filetypemime = filetypemime;\nconst filetypeextension = (bytes) => exports.filetypeinfo(bytes)\n    .map((e) => (e.extension ? e.extension : null))\n    .filter((x) => x !== null);\nexports.filetypeextension = filetypeextension;\nconst register = (typename, signature, additionalInfo, offset) => {\n    pattern_tree_1.add(typename, signature, additionalInfo, offset);\n};\nexports.register = register;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/magic-bytes.js/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/magic-bytes.js/dist/model/pattern-tree.js":
/*!****************************************************************!*\
  !*** ./node_modules/magic-bytes.js/dist/model/pattern-tree.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createTree = exports.add = void 0;\nconst toHex_1 = __webpack_require__(/*! ./toHex */ \"(rsc)/./node_modules/magic-bytes.js/dist/model/toHex.js\");\nconst tree_1 = __webpack_require__(/*! ./tree */ \"(rsc)/./node_modules/magic-bytes.js/dist/model/tree.js\");\n// https://en.wikipedia.org/wiki/List_of_file_signatures\nlet tree = {\n    noOffset: null,\n    offset: {},\n};\nconst add = (typename, signature, additionalInfo, offset) => {\n    if (offset) {\n        const existing = tree.offset[toHex_1.toHex(offset)];\n        if (!existing) {\n            tree.offset[toHex_1.toHex(offset)] = tree_1.createComplexNode(typename, signature.map((e) => e.toLowerCase()), additionalInfo);\n        }\n        else {\n            const merged = tree_1.merge(tree_1.createNode(typename, signature.map((e) => e.toLowerCase()), additionalInfo), { ...existing });\n            tree.offset[toHex_1.toHex(offset)] = merged;\n        }\n    }\n    else {\n        if (tree.noOffset === null) {\n            tree.noOffset = tree_1.createComplexNode(typename, signature.map((e) => e.toLowerCase()), additionalInfo);\n        }\n        else {\n            tree.noOffset = tree_1.merge(tree_1.createNode(typename, signature.map((e) => e.toLowerCase()), additionalInfo), tree.noOffset);\n        }\n    }\n};\nexports.add = add;\nexports.add(\"gif\", [\"0x47\", \"0x49\", \"0x46\", \"0x38\", \"0x37\", \"0x61\"], {\n    mime: \"image/gif\",\n    extension: \"gif\",\n});\nexports.add(\"gif\", [\"0x47\", \"0x49\", \"0x46\", \"0x38\", \"0x39\", \"0x61\"], {\n    mime: \"image/gif\",\n    extension: \"gif\",\n});\nexports.add(\"jpg\", [\"0xFF\", \"0xD8\", \"0xFF\"], {\n    mime: \"image/jpeg\",\n    extension: \"jpeg\",\n});\nexports.add(\"webp\", [\n    \"0x52\",\n    \"0x49\",\n    \"0x46\",\n    \"0x46\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x57\",\n    \"0x45\",\n    \"0x42\",\n    \"0x50\",\n], { mime: \"image/webp\", extension: \"webp\" });\nexports.add(\"heif\", [\"0x66\", \"0x74\", \"0x79\", \"0x70\", \"0x6D\", \"0x69\", \"0x66\", \"0x31\"], { mime: \"image/heif\", extension: \"heif\" }, 4);\nexports.add(\"heif\", [\"0x66\", \"0x74\", \"0x79\", \"0x70\", \"0x68\", \"0x65\", \"0x69\", \"0x63\"], { mime: \"image/heif\", extension: \"heic\" }, 4);\nexports.add(\"rpm\", [\"0xed\", \"0xab\", \"0xee\", \"0xdb\"]);\nexports.add(\"bin\", [\"0x53\", \"0x50\", \"0x30\", \"0x31\"], {\n    mime: \"application/octet-stream\",\n    extension: \"bin\",\n});\nexports.add(\"pic\", [\"0x00\"]);\nexports.add(\"pif\", [\"0x00\"]);\nexports.add(\"sea\", [\"0x00\"]);\nexports.add(\"ytr\", [\"0x00\"]);\n// 66747970\n// 6D703432\nexports.add(\"mp4\", [\"0x66\", \"0x74\", \"0x79\", \"0x70\"], { mime: \"video/mp4\", extension: \"mp4\" }, 0x4);\nexports.add(\"ttf\", [\"0x00\", \"0x01\", \"0x00\", \"0x00\", \"0x00\"], {\n    mime: \"font/ttf\",\n    extension: \"ttf\",\n});\nexports.add(\"otf\", [\"0x4F\", \"0x54\", \"0x54\", \"0x4F\"], {\n    mime: \"font/otf\",\n    extension: \"otf\",\n});\nexports.add(\"eot\", [\"0x50\", \"0x4C\"], {\n    mime: \"application/vnd.ms-fontobject\",\n    extension: \"eot\",\n});\nexports.add(\"woff\", [\"0x77\", \"0x4F\", \"0x46\", \"0x46\"], {\n    mime: \"font/woff\",\n    extension: \"woff\",\n});\nexports.add(\"woff2\", [\"0x77\", \"0x4F\", \"0x46\", \"0x32\"], {\n    mime: \"font/woff2\",\n    extension: \"woff2\",\n});\nexports.add(\"pdb\", [\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n]);\nexports.add(\"dba\", [\"0xBE\", \"0xBA\", \"0xFE\", \"0xCA\"]);\nexports.add(\"dba2\", [\"0x00\", \"0x01\", \"0x42\", \"0x44\"]);\nexports.add(\"tda\", [\"0x00\", \"0x01\", \"0x44\", \"0x54\"]);\nexports.add(\"tda2\", [\"0x00\", \"0x01\", \"0x00\", \"0x00\"]);\nexports.add(\"ico\", [\"0x00\", \"0x00\", \"0x01\", \"0x00\"], {\n    mime: \"image/x-icon\",\n    extension: \"ico\",\n});\nexports.add(\"3gp\", [\"0x66\", \"0x74\", \"0x79\", \"0x70\", \"0x33\", \"0x67\"]);\nexports.add(\"z\", [\"0x1F\", \"0x9D\"]);\nexports.add(\"tar.z\", [\"0x1F\", \"0xA0\"]);\nexports.add(\"bac\", [\n    \"0x42\",\n    \"0x41\",\n    \"0x43\",\n    \"0x4B\",\n    \"0x4D\",\n    \"0x49\",\n    \"0x4B\",\n    \"0x45\",\n    \"0x44\",\n    \"0x49\",\n    \"0x53\",\n    \"0x4B\",\n]);\nexports.add(\"bz2\", [\"0x42\", \"0x5A\", \"0x68\"], {\n    mime: \"application/x-bzip2\",\n    extension: \"bz2\",\n});\nexports.add(\"tif\", [\"0x49\", \"0x49\", \"0x2A\", \"0x00\"], {\n    mime: \"image/tiff\",\n    extension: \"tif\",\n});\nexports.add(\"tiff\", [\"0x4D\", \"0x4D\", \"0x00\", \"0x2A\"], {\n    mime: \"image/tiff\",\n    extension: \"tiff\",\n});\nexports.add(\"cr2\", [\n    \"0x49\",\n    \"0x49\",\n    \"0x2A\",\n    \"0x00\",\n    \"0x10\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x43\",\n    \"0x52\",\n]);\nexports.add(\"cin\", [\"0x80\", \"0x2A\", \"0x5F\", \"0xD7\"]);\nexports.add(\"cin1\", [\"0x52\", \"0x4E\", \"0x43\", \"0x01\"]);\nexports.add(\"cin2\", [\"0x52\", \"0x4E\", \"0x43\", \"0x02\"]);\nexports.add(\"dpx\", [\"0x53\", \"0x44\", \"0x50\", \"0x58\"]);\nexports.add(\"dpx2\", [\"0x58\", \"0x50\", \"0x44\", \"0x53\"]);\nexports.add(\"exr\", [\"0x76\", \"0x2F\", \"0x31\", \"0x01\"]);\nexports.add(\"bpg\", [\"0x42\", \"0x50\", \"0x47\", \"0xFB\"]);\nexports.add(\"ilbm\", [\n    \"0x46\",\n    \"0x4F\",\n    \"0x52\",\n    \"0x4D\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x49\",\n    \"0x4C\",\n    \"0x42\",\n    \"0x4D\",\n]);\nexports.add(\"8svx\", [\n    \"0x46\",\n    \"0x4F\",\n    \"0x52\",\n    \"0x4D\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x38\",\n    \"0x53\",\n    \"0x56\",\n    \"0x58\",\n]);\nexports.add(\"acbm\", [\n    \"0x46\",\n    \"0x4F\",\n    \"0x52\",\n    \"0x4D\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x41\",\n    \"0x43\",\n    \"0x42\",\n    \"0x4D\",\n]);\nexports.add(\"anbm\", [\n    \"0x46\",\n    \"0x4F\",\n    \"0x52\",\n    \"0x4D\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x41\",\n    \"0x4E\",\n    \"0x42\",\n    \"0x4D\",\n]);\nexports.add(\"anim\", [\n    \"0x46\",\n    \"0x4F\",\n    \"0x52\",\n    \"0x4D\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x41\",\n    \"0x4E\",\n    \"0x49\",\n    \"0x4D\",\n]);\nexports.add(\"faxx\", [\n    \"0x46\",\n    \"0x4F\",\n    \"0x52\",\n    \"0x4D\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x46\",\n    \"0x41\",\n    \"0x58\",\n    \"0x58\",\n]);\nexports.add(\"ftxt\", [\n    \"0x46\",\n    \"0x4F\",\n    \"0x52\",\n    \"0x4D\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x46\",\n    \"0x54\",\n    \"0x58\",\n    \"0x54\",\n]);\nexports.add(\"smus\", [\n    \"0x46\",\n    \"0x4F\",\n    \"0x52\",\n    \"0x4D\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x53\",\n    \"0x4D\",\n    \"0x55\",\n    \"0x53\",\n]);\nexports.add(\"cmus\", [\n    \"0x46\",\n    \"0x4F\",\n    \"0x52\",\n    \"0x4D\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x43\",\n    \"0x4D\",\n    \"0x55\",\n    \"0x53\",\n]);\nexports.add(\"yuvn\", [\n    \"0x46\",\n    \"0x4F\",\n    \"0x52\",\n    \"0x4D\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x59\",\n    \"0x55\",\n    \"0x56\",\n    \"0x4E\",\n]);\nexports.add(\"iff\", [\n    \"0x46\",\n    \"0x4F\",\n    \"0x52\",\n    \"0x4D\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x46\",\n    \"0x41\",\n    \"0x4E\",\n    \"0x54\",\n]);\nexports.add(\"aiff\", [\n    \"0x46\",\n    \"0x4F\",\n    \"0x52\",\n    \"0x4D\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x41\",\n    \"0x49\",\n    \"0x46\",\n    \"0x46\",\n], { mime: \"audio/x-aiff\", extension: \"aiff\" });\nexports.add(\"idx\", [\"0x49\", \"0x4E\", \"0x44\", \"0x58\"]);\nexports.add(\"lz\", [\"0x4C\", \"0x5A\", \"0x49\", \"0x50\"]);\nexports.add(\"exe\", [\"0x4D\", \"0x5A\"]);\nexports.add(\"zip\", [\"0x50\", \"0x4B\", \"0x03\", \"0x04\"], {\n    mime: \"application/zip\",\n    extension: \"zip\",\n});\nexports.add(\"zip\", [\"0x50\", \"0x4B\", \"0x05\", \"0x06\"], {\n    mime: \"application/zip\",\n    extension: \"zip\",\n});\nexports.add(\"zip\", [\"0x50\", \"0x4B\", \"0x07\", \"0x08\"], {\n    mime: \"application/zip\",\n    extension: \"zip\",\n});\nexports.add(\"jar\", [\"0x50\", \"0x4B\", \"0x03\", \"0x04\"], {\n    mime: \"application/java-archive\",\n    extension: \"jar\",\n});\nexports.add(\"jar\", [\"0x50\", \"0x4B\", \"0x05\", \"0x06\"], {\n    mime: \"application/java-archive\",\n    extension: \"jar\",\n});\nexports.add(\"jar\", [\"0x50\", \"0x4B\", \"0x07\", \"0x08\"], {\n    mime: \"application/java-archive\",\n    extension: \"jar\",\n});\nexports.add(\"odt\", [\"0x50\", \"0x4B\", \"0x03\", \"0x04\"], {\n    mime: \"application/vnd.oasis.opendocument.text\",\n    extension: \"odt\",\n});\nexports.add(\"odt\", [\"0x50\", \"0x4B\", \"0x05\", \"0x06\"], {\n    mime: \"application/vnd.oasis.opendocument.text\",\n    extension: \"odt\",\n});\nexports.add(\"odt\", [\"0x50\", \"0x4B\", \"0x07\", \"0x08\"], {\n    mime: \"application/vnd.oasis.opendocument.text\",\n    extension: \"odt\",\n});\nexports.add(\"ods\", [\"0x50\", \"0x4B\", \"0x03\", \"0x04\"], {\n    mime: \"application/vnd.oasis.opendocument.spreadsheet\",\n    extension: \"ods\",\n});\nexports.add(\"ods\", [\"0x50\", \"0x4B\", \"0x05\", \"0x06\"], {\n    mime: \"application/vnd.oasis.opendocument.spreadsheet\",\n    extension: \"ods\",\n});\nexports.add(\"ods\", [\"0x50\", \"0x4B\", \"0x07\", \"0x08\"], {\n    mime: \"application/vnd.oasis.opendocument.spreadsheet\",\n    extension: \"ods\",\n});\nexports.add(\"odp\", [\"0x50\", \"0x4B\", \"0x03\", \"0x04\"], {\n    mime: \"application/vnd.oasis.opendocument.presentation\",\n    extension: \"odp\",\n});\nexports.add(\"odp\", [\"0x50\", \"0x4B\", \"0x05\", \"0x06\"], {\n    mime: \"application/vnd.oasis.opendocument.presentation\",\n    extension: \"odp\",\n});\nexports.add(\"odp\", [\"0x50\", \"0x4B\", \"0x07\", \"0x08\"], {\n    mime: \"application/vnd.oasis.opendocument.presentation\",\n    extension: \"odp\",\n});\nexports.add(\"docx\", [\"0x50\", \"0x4B\", \"0x03\", \"0x04\"], {\n    mime: \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n    extension: \"docx\",\n});\nexports.add(\"docx\", [\"0x50\", \"0x4B\", \"0x05\", \"0x06\"], {\n    mime: \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n    extension: \"docx\",\n});\nexports.add(\"docx\", [\"0x50\", \"0x4B\", \"0x07\", \"0x08\"], {\n    mime: \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n    extension: \"docx\",\n});\nexports.add(\"xlsx\", [\"0x50\", \"0x4B\", \"0x03\", \"0x04\"], {\n    mime: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n    extension: \"xlsx\",\n});\nexports.add(\"xlsx\", [\"0x50\", \"0x4B\", \"0x05\", \"0x06\"], {\n    mime: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n    extension: \"xlsx\",\n});\nexports.add(\"xlsx\", [\"0x50\", \"0x4B\", \"0x07\", \"0x08\"], {\n    mime: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n    extension: \"xlsx\",\n});\nexports.add(\"pptx\", [\"0x50\", \"0x4B\", \"0x03\", \"0x04\"], {\n    mime: \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n    extension: \"pptx\",\n});\nexports.add(\"pptx\", [\"0x50\", \"0x4B\", \"0x05\", \"0x06\"], {\n    mime: \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n    extension: \"pptx\",\n});\nexports.add(\"pptx\", [\"0x50\", \"0x4B\", \"0x07\", \"0x08\"], {\n    mime: \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n    extension: \"pptx\",\n});\nexports.add(\"vsdx\", [\"0x50\", \"0x4B\", \"0x03\", \"0x04\"], {\n    mime: \"application/vnd.ms-visio.drawing\",\n    extension: \"vsdx\",\n});\nexports.add(\"vsdx\", [\"0x50\", \"0x4B\", \"0x05\", \"0x06\"], {\n    mime: \"application/vnd.ms-visio.drawing\",\n    extension: \"vsdx\",\n});\nexports.add(\"vsdx\", [\"0x50\", \"0x4B\", \"0x07\", \"0x08\"], {\n    mime: \"application/vnd.ms-visio.drawing\",\n    extension: \"vsdx\",\n});\nexports.add(\"apk\", [\"0x50\", \"0x4B\", \"0x03\", \"0x04\"], {\n    mime: \"application/vnd.android.package-archive\",\n    extension: \"apk\",\n});\nexports.add(\"apk\", [\"0x50\", \"0x4B\", \"0x05\", \"0x06\"], {\n    mime: \"application/vnd.android.package-archive\",\n    extension: \"apk\",\n});\nexports.add(\"apk\", [\"0x50\", \"0x4B\", \"0x07\", \"0x08\"], {\n    mime: \"application/vnd.android.package-archive\",\n    extension: \"apk\",\n});\nexports.add(\"aar\", [\"0x50\", \"0x4B\", \"0x03\", \"0x04\"], {\n    mime: \"application/vnd.android.package-archive\",\n    extension: \"aar\",\n});\nexports.add(\"aar\", [\"0x50\", \"0x4B\", \"0x05\", \"0x06\"], {\n    mime: \"application/vnd.android.package-archive\",\n    extension: \"aar\",\n});\nexports.add(\"aar\", [\"0x50\", \"0x4B\", \"0x07\", \"0x08\"], {\n    mime: \"application/vnd.android.package-archive\",\n    extension: \"aar\",\n});\nexports.add(\"rar\", [\"0x52\", \"0x61\", \"0x72\", \"0x21\", \"0x1A\", \"0x07\", \"0x00\"], {\n    mime: \"application/vnd.rar\",\n    extension: \"rar\",\n});\nexports.add(\"rar\", [\"0x52\", \"0x61\", \"0x72\", \"0x21\", \"0x1A\", \"0x07\", \"0x01\", \"0x00\"], {\n    mime: \"application/vnd.rar\",\n    extension: \"rar\",\n});\nexports.add(\"rar\", [\"0x7F\", \"0x45\", \"0x4C\", \"0x46\"], {\n    mime: \"application/vnd.rar\",\n    extension: \"rar\",\n});\nexports.add(\"png\", [\"0x89\", \"0x50\", \"0x4E\", \"0x47\", \"0x0D\", \"0x0A\", \"0x1A\", \"0x0A\"], {\n    mime: \"image/png\",\n    extension: \"png\",\n});\nexports.add(\"apng\", [\"0x89\", \"0x50\", \"0x4E\", \"0x47\", \"0x0D\", \"0x0A\", \"0x1A\", \"0x0A\"], {\n    mime: \"image/apng\",\n    extension: \"apng\",\n});\nexports.add(\"class\", [\"0xCA\", \"0xFE\", \"0xBA\", \"0xBE\"]);\nexports.add(\"class\", [\"0xEF\", \"0xBB\", \"0xBF\"]);\nexports.add(\"class\", [\"0xFE\", \"0xed\", \"0xFA\", \"0xCE\"], undefined, 0x1000);\nexports.add(\"class\", [\"0xFE\", \"0xed\", \"0xFA\", \"0xCF\"], undefined, 0x1000);\nexports.add(\"class\", [\"0xCE\", \"0xFA\", \"0xed\", \"0xFE\"]);\nexports.add(\"class\", [\"0xCF\", \"0xFA\", \"0xed\", \"0xFE\"]);\nexports.add(\"class\", [\"0xFF\", \"0xFE\"]);\nexports.add(\"class\", [\"0xFF\", \"0xFE\"]);\nexports.add(\"class\", [\"0xFF\", \"0xFE\", \"0x00\", \"0x00\"]);\nexports.add(\"ps\", [\"0x25\", \"0x21\", \"0x50\", \"0x53\"], {\n    mime: \"application/postscript\",\n    extension: \".ps\",\n});\nexports.add(\"pdf\", [\"0x25\", \"0x50\", \"0x44\", \"0x46\"], {\n    mime: \"application/pdf\",\n    extension: \"pdf\",\n});\nexports.add(\"asf\", [\n    \"0x30\",\n    \"0x26\",\n    \"0xB2\",\n    \"0x75\",\n    \"0x8E\",\n    \"0x66\",\n    \"0xCF\",\n    \"0x11\",\n    \"0xA6\",\n    \"0xD9\",\n    \"0x00\",\n    \"0xAA\",\n    \"0x00\",\n    \"0x62\",\n    \"0xCE\",\n    \"0x6C\",\n]);\nexports.add(\"wma\", [\n    \"0x30\",\n    \"0x26\",\n    \"0xB2\",\n    \"0x75\",\n    \"0x8E\",\n    \"0x66\",\n    \"0xCF\",\n    \"0x11\",\n    \"0xA6\",\n    \"0xD9\",\n    \"0x00\",\n    \"0xAA\",\n    \"0x00\",\n    \"0x62\",\n    \"0xCE\",\n    \"0x6C\",\n]);\nexports.add(\"wmv\", [\n    \"0x30\",\n    \"0x26\",\n    \"0xB2\",\n    \"0x75\",\n    \"0x8E\",\n    \"0x66\",\n    \"0xCF\",\n    \"0x11\",\n    \"0xA6\",\n    \"0xD9\",\n    \"0x00\",\n    \"0xAA\",\n    \"0x00\",\n    \"0x62\",\n    \"0xCE\",\n    \"0x6C\",\n]);\nexports.add(\"deploymentimage\", [\n    \"0x24\",\n    \"0x53\",\n    \"0x44\",\n    \"0x49\",\n    \"0x30\",\n    \"0x30\",\n    \"0x30\",\n    \"0x31\",\n]);\n// ogg video ' theora'\nexports.add(\"ogv\", [\n    \"0x4F\",\n    \"0x67\",\n    \"0x67\",\n    \"0x53\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x80\",\n    \"0x74\",\n    \"0x68\",\n    \"0x65\",\n    \"0x6F\",\n    \"0x72\",\n    \"0x61\",\n], {\n    mime: \"video/ogg\",\n    extension: \"ogv\",\n});\n// ogg video '\\x01video'\nexports.add(\"ogm\", [\n    \"0x4F\",\n    \"0x67\",\n    \"0x67\",\n    \"0x53\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x01\",\n    \"0x76\",\n    \"0x69\",\n    \"0x64\",\n    \"0x65\",\n    \"0x6F\",\n    \"0x00\",\n], {\n    mime: \"video/ogg\",\n    extension: \"ogm\",\n});\n// ogg audio ' FLAC'\nexports.add(\"oga\", [\n    \"0x4F\",\n    \"0x67\",\n    \"0x67\",\n    \"0x53\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x7F\",\n    \"0x46\",\n    \"0x4C\",\n    \"0x41\",\n    \"0x43\",\n], {\n    mime: \"audio/ogg\",\n    extension: \"oga\",\n});\n// ogg audio 'Speex  '\nexports.add(\"spx\", [\n    \"0x4F\",\n    \"0x67\",\n    \"0x67\",\n    \"0x53\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x53\",\n    \"0x70\",\n    \"0x65\",\n    \"0x65\",\n    \"0x78\",\n    \"0x20\",\n    \"0x20\",\n], {\n    mime: \"audio/ogg\",\n    extension: \"spx\",\n});\n// ogg audio '\\x01vorbis '\nexports.add(\"ogg\", [\n    \"0x4F\",\n    \"0x67\",\n    \"0x67\",\n    \"0x53\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x01\",\n    \"0x76\",\n    \"0x6F\",\n    \"0x72\",\n    \"0x62\",\n    \"0x69\",\n    \"0x73\",\n], {\n    mime: \"audio/ogg\",\n    extension: \"ogg\",\n});\n// default OGG container\nexports.add(\"ogx\", [\"0x4F\", \"0x67\", \"0x67\", \"0x53\"], {\n    mime: \"application/ogg\",\n    extension: \"ogx\",\n});\nexports.add(\"psd\", [\"0x38\", \"0x42\", \"0x50\", \"0x53\"], {\n    mime: \"application/x-photoshop\",\n    extension: \"psd\",\n});\nexports.add(\"clip\", [\"0x43\", \"0x53\", \"0x46\", \"0x43\", \"0x48\", \"0x55\", \"0x4e\", \"0x4b\"]);\nexports.add(\"wav\", [\n    \"0x52\",\n    \"0x49\",\n    \"0x46\",\n    \"0x46\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x57\",\n    \"0x41\",\n    \"0x56\",\n    \"0x45\",\n], { mime: \"audio/x-wav\", extension: \"wav\" });\nexports.add(\"avi\", [\n    \"0x52\",\n    \"0x49\",\n    \"0x46\",\n    \"0x46\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"?\",\n    \"0x41\",\n    \"0x56\",\n    \"0x49\",\n    \"0x20\",\n], { mime: \"video/x-msvideo\", extension: \"avi\" });\nexports.add(\"mp3\", [\"0xFF\", \"0xFB\"], { mime: \"audio/mpeg\", extension: \"mp3\" });\nexports.add(\"mp3\", [\"0xFF\", \"0xF3\"], { mime: \"audio/mpeg\", extension: \"mp3\" });\nexports.add(\"mp3\", [\"0xFF\", \"0xF2\"], { mime: \"audio/mpeg\", extension: \"mp3\" });\nexports.add(\"mp3\", [\"0x49\", \"0x44\", \"0x33\"], { mime: \"audio/mpeg\", extension: \"mp3\" });\nexports.add(\"bmp\", [\"0x42\", \"0x4D\"], { mime: \"image/bmp\", extension: \"bmp\" });\nexports.add(\"iso\", [\"0x43\", \"0x44\", \"0x30\", \"0x30\", \"0x31\"]);\nexports.add(\"flac\", [\"0x66\", \"0x4C\", \"0x61\", \"0x43\"]);\nexports.add(\"mid\", [\"0x4D\", \"0x54\", \"0x68\", \"0x64\"], {\n    mime: \"audio/midi\",\n    extension: \"mid\",\n});\nexports.add(\"midi\", [\"0x4D\", \"0x54\", \"0x68\", \"0x64\"], {\n    mime: \"audio/midi\",\n    extension: \"midi\",\n});\nexports.add(\"doc\", [\"0xD0\", \"0xCF\", \"0x11\", \"0xE0\", \"0xA1\", \"0xB1\", \"0x1A\", \"0xE1\"], {\n    mime: \"application/msword\",\n    extension: \"doc\",\n});\nexports.add(\"xls\", [\"0xD0\", \"0xCF\", \"0x11\", \"0xE0\", \"0xA1\", \"0xB1\", \"0x1A\", \"0xE1\"], {\n    mime: \"application/vnd.ms-excel\",\n    extension: \"xls\",\n});\nexports.add(\"ppt\", [\"0xD0\", \"0xCF\", \"0x11\", \"0xE0\", \"0xA1\", \"0xB1\", \"0x1A\", \"0xE1\"], {\n    mime: \"application/vnd.ms-powerpoint\",\n    extension: \"ppt\",\n});\nexports.add(\"msg\", [\"0xD0\", \"0xCF\", \"0x11\", \"0xE0\", \"0xA1\", \"0xB1\", \"0x1A\", \"0xE1\"]);\nexports.add(\"dex\", [\"0x64\", \"0x65\", \"0x78\", \"0x0A\", \"0x30\", \"0x33\", \"0x35\", \"0x00\"]);\nexports.add(\"vmdk\", [\"0x4B\", \"0x44\", \"0x4D\"]);\nexports.add(\"crx\", [\"0x43\", \"0x72\", \"0x32\", \"0x34\"]);\nexports.add(\"fh8\", [\"0x41\", \"0x47\", \"0x44\", \"0x33\"]);\nexports.add(\"cwk\", [\n    \"0x05\",\n    \"0x07\",\n    \"0x00\",\n    \"0x00\",\n    \"0x42\",\n    \"0x4F\",\n    \"0x42\",\n    \"0x4F\",\n    \"0x05\",\n    \"0x07\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x01\",\n]);\nexports.add(\"cwk\", [\n    \"0x06\",\n    \"0x07\",\n    \"0xE1\",\n    \"0x00\",\n    \"0x42\",\n    \"0x4F\",\n    \"0x42\",\n    \"0x4F\",\n    \"0x06\",\n    \"0x07\",\n    \"0xE1\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x00\",\n    \"0x01\",\n]);\nexports.add(\"toast\", [\"0x45\", \"0x52\", \"0x02\", \"0x00\", \"0x00\", \"0x00\"]);\nexports.add(\"toast\", [\"0x8B\", \"0x45\", \"0x52\", \"0x02\", \"0x00\", \"0x00\", \"0x00\"]);\nexports.add(\"dmg\", [\"0x78\", \"0x01\", \"0x73\", \"0x0D\", \"0x62\", \"0x62\", \"0x60\"]);\nexports.add(\"xar\", [\"0x78\", \"0x61\", \"0x72\", \"0x21\"]);\nexports.add(\"dat\", [\"0x50\", \"0x4D\", \"0x4F\", \"0x43\", \"0x43\", \"0x4D\", \"0x4F\", \"0x43\"]);\nexports.add(\"nes\", [\"0x4E\", \"0x45\", \"0x53\", \"0x1A\"]);\nexports.add(\"tar\", [\"0x75\", \"0x73\", \"0x74\", \"0x61\", \"0x72\", \"0x00\", \"0x30\", \"0x30\"], {\n    // As per Mozilla documentation available at:\n    // https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types\n    // or wikipedia page:\n    // https://en.wikipedia.org/wiki/List_of_archive_formats\n    mime: \"application/x-tar\",\n    extension: \"tar\",\n}, 0x101);\nexports.add(\"tar\", [\"0x75\", \"0x73\", \"0x74\", \"0x61\", \"0x72\", \"0x20\", \"0x20\", \"0x00\"], {\n    mime: \"application/x-tar\",\n    extension: \"tar\",\n}, 0x101);\nexports.add(\"tox\", [\"0x74\", \"0x6F\", \"0x78\", \"0x33\"]);\nexports.add(\"mlv\", [\"0x4D\", \"0x4C\", \"0x56\", \"0x49\"]);\nexports.add(\"windowsupdate\", [\n    \"0x44\",\n    \"0x43\",\n    \"0x4D\",\n    \"0x01\",\n    \"0x50\",\n    \"0x41\",\n    \"0x33\",\n    \"0x30\",\n]);\nexports.add(\"7z\", [\"0x37\", \"0x7A\", \"0xBC\", \"0xAF\", \"0x27\", \"0x1C\"], {\n    mime: \"application/x-7z-compressed\",\n    extension: \"7z\",\n});\nexports.add(\"gz\", [\"0x1F\", \"0x8B\"], { mime: \"application/gzip\", extension: \"gz\" });\nexports.add(\"tar.gz\", [\"0x1F\", \"0x8B\"], {\n    mime: \"application/gzip\",\n    extension: \"tar.gz\",\n});\nexports.add(\"xz\", [\"0xFD\", \"0x37\", \"0x7A\", \"0x58\", \"0x5A\", \"0x00\", \"0x00\"], {\n    mime: \"application/gzip\",\n    extension: \"xz\",\n});\nexports.add(\"tar.xz\", [\"0xFD\", \"0x37\", \"0x7A\", \"0x58\", \"0x5A\", \"0x00\", \"0x00\"], {\n    mime: \"application/gzip\",\n    extension: \"tar.xz\",\n});\nexports.add(\"lz2\", [\"0x04\", \"0x22\", \"0x4D\", \"0x18\"]);\nexports.add(\"cab\", [\"0x4D\", \"0x53\", \"0x43\", \"0x46\"]);\nexports.add(\"mkv\", [\"0x1A\", \"0x45\", \"0xDF\", \"0xA3\"], {\n    mime: \"video/x-matroska\",\n    extension: \"mkv\",\n});\nexports.add(\"mka\", [\"0x1A\", \"0x45\", \"0xDF\", \"0xA3\"], {\n    mime: \"audio/x-matroska\",\n    extension: \"mka\",\n});\nexports.add(\"mks\", [\"0x1A\", \"0x45\", \"0xDF\", \"0xA3\"], {\n    mime: \"video/x-matroska\",\n    extension: \"mks\",\n});\nexports.add(\"mk3d\", [\"0x1A\", \"0x45\", \"0xDF\", \"0xA3\"]);\nexports.add(\"webm\", [\"0x1A\", \"0x45\", \"0xDF\", \"0xA3\"], {\n    mime: \"audio/webm\",\n    extension: \"webm\",\n});\nexports.add(\"dcm\", [\"0x44\", \"0x49\", \"0x43\", \"0x4D\"], undefined, 0x80);\nexports.add(\"xml\", [\"0x3C\", \"0x3f\", \"0x78\", \"0x6d\", \"0x6C\", \"0x20\"], {\n    mime: \"application/xml\",\n    extension: \"xml\",\n});\nexports.add(\"wasm\", [\"0x00\", \"0x61\", \"0x73\", \"0x6d\"], {\n    mime: \"application/wasm\",\n    extension: \"wasm\",\n});\nexports.add(\"lep\", [\"0xCF\", \"0x84\", \"0x01\"]);\nexports.add(\"swf\", [\"0x43\", \"0x57\", \"0x53\"], {\n    mime: \"application/x-shockwave-flash\",\n    extension: \"swf\",\n});\nexports.add(\"swf\", [\"0x46\", \"0x57\", \"0x53\"], {\n    mime: \"application/x-shockwave-flash\",\n    extension: \"swf\",\n});\nexports.add(\"deb\", [\"0x21\", \"0x3C\", \"0x61\", \"0x72\", \"0x63\", \"0x68\", \"0x3E\"]);\nexports.add(\"rtf\", [\"0x7B\", \"0x5C\", \"0x72\", \"0x74\", \"0x66\", \"0x31\"], {\n    mime: \"application/rtf\",\n    extension: \"rtf\",\n});\nexports.add(\"m2p\", [\"0x00\", \"0x00\", \"0x01\", \"0xBA\"]);\nexports.add(\"vob\", [\"0x00\", \"0x00\", \"0x01\", \"0xBA\"]);\nexports.add(\"mpg\", [\"0x00\", \"0x00\", \"0x01\", \"0xBA\"], {\n    mime: \"video/mpeg\",\n    extension: \"mpg\",\n});\nexports.add(\"mpeg\", [\"0x00\", \"0x00\", \"0x01\", \"0xBA\"], {\n    mime: \"video/mpeg\",\n    extension: \"mpeg\",\n});\nexports.add(\"mpeg\", [\"0x47\"], { mime: \"video/mpeg\", extension: \"mpeg\" });\nexports.add(\"mpeg\", [\"0x00\", \"0x00\", \"0x01\", \"0xB3\"], {\n    mime: \"video/mpeg\",\n    extension: \"mpeg\",\n});\n// mov 'free' TODO: find test file\nexports.add(\"mov\", [\"0x66\", \"0x72\", \"0x65\", \"0x65\"], {\n    mime: \"video/quicktime\",\n    extension: \"mov\",\n}, 0x4);\n// mov 'mdat'\nexports.add(\"mov\", [\"0x6D\", \"0x64\", \"0x61\", \"0x74\"], {\n    mime: \"video/quicktime\",\n    extension: \"mov\",\n}, 0x4);\n// mov 'moov' TODO: find test file\nexports.add(\"mov\", [\"0x6D\", \"0x6F\", \"0x6F\", \"0x76\"], {\n    mime: \"video/quicktime\",\n    extension: \"mov\",\n}, 0x4);\n// move 'wide' TODO: find test file\nexports.add(\"mov\", [\"0x77\", \"0x69\", \"0x64\", \"0x65\"], {\n    mime: \"video/quicktime\",\n    extension: \"mov\",\n}, 0x4);\n// mov 'ftypqt'\nexports.add(\"mov\", [\"0x66\", \"0x74\", \"0x79\", \"0x70\", \"0x71\", \"0x74\"], {\n    mime: \"video/quicktime\",\n    extension: \"mov\",\n}, 0x4);\nexports.add(\"hl2demo\", [\"0x48\", \"0x4C\", \"0x32\", \"0x44\", \"0x45\", \"0x4D\", \"0x4F\"]);\nexports.add(\"txt\", [\"0xEF\", \"0xBB\", \"0xBF\"], {\n    mime: \"text/plain; charset=UTF-8\",\n    extension: \"txt\",\n});\nexports.add(\"txt\", [\"0xFF\", \"0xFE\"], {\n    mime: \"text/plain; charset=UTF-16LE\",\n    extension: \"txt\",\n});\nexports.add(\"txt\", [\"0xFE\", \"0xFF\"], {\n    mime: \"text/plain; charset=UTF-16BE\",\n    extension: \"txt\",\n});\nexports.add(\"txt\", [\"0xFF\", \"0xFE\", \"0x00\", \"0x00\"], {\n    mime: \"text/plain; charset=UTF-32LE\",\n    extension: \"txt\",\n});\nexports.add(\"txt\", [\"0x00\", \"0x00\", \"0xFE\", \"0xFF\"], {\n    mime: \"text/plain; charset=UTF-32BE\",\n    extension: \"txt\",\n});\nexports.add(\"SubRip\", [\"0x31\", \"0x0D\", \"0x0A\", \"0x30\", \"0x30\", \"0x3A\"], {\n    mime: \"application/x-subrip\",\n    extension: \"srt\",\n});\nexports.add(\"WebVTT\", [\n    \"0xEF\",\n    \"0xBB\",\n    \"0xBF\",\n    \"0x57\",\n    \"0x45\",\n    \"0x42\",\n    \"0x56\",\n    \"0x54\",\n    \"0x54\",\n    \"0x0A\",\n], {\n    mime: \"text/vtt\",\n    extension: \"vtt\",\n});\nexports.add(\"WebVTT\", [\n    \"0xEF\",\n    \"0xBB\",\n    \"0xBF\",\n    \"0x57\",\n    \"0x45\",\n    \"0x42\",\n    \"0x56\",\n    \"0x54\",\n    \"0x54\",\n    \"0x0D\",\n], {\n    mime: \"text/vtt\",\n    extension: \"vtt\",\n});\nexports.add(\"WebVTT\", [\n    \"0xEF\",\n    \"0xBB\",\n    \"0xBF\",\n    \"0x57\",\n    \"0x45\",\n    \"0x42\",\n    \"0x56\",\n    \"0x54\",\n    \"0x54\",\n    \"0x20\",\n], {\n    mime: \"text/vtt\",\n    extension: \"vtt\",\n});\nexports.add(\"WebVTT\", [\n    \"0xEF\",\n    \"0xBB\",\n    \"0xBF\",\n    \"0x57\",\n    \"0x45\",\n    \"0x42\",\n    \"0x56\",\n    \"0x54\",\n    \"0x54\",\n    \"0x09\",\n], {\n    mime: \"text/vtt\",\n    extension: \"vtt\",\n});\nexports.add(\"WebVTT\", [\"0x57\", \"0x45\", \"0x42\", \"0x56\", \"0x54\", \"0x54\", \"0x0A\"], {\n    mime: \"text/vtt\",\n    extension: \"vtt\",\n});\nexports.add(\"WebVTT\", [\"0x57\", \"0x45\", \"0x42\", \"0x56\", \"0x54\", \"0x54\", \"0x0D\"], {\n    mime: \"text/vtt\",\n    extension: \"vtt\",\n});\nexports.add(\"WebVTT\", [\"0x57\", \"0x45\", \"0x42\", \"0x56\", \"0x54\", \"0x54\", \"0x20\"], {\n    mime: \"text/vtt\",\n    extension: \"vtt\",\n});\nexports.add(\"WebVTT\", [\"0x57\", \"0x45\", \"0x42\", \"0x56\", \"0x54\", \"0x54\", \"0x09\"], {\n    mime: \"text/vtt\",\n    extension: \"vtt\",\n});\nexports.add(\"Json\", [\"0x7B\"], {\n    mime: \"application/json\",\n    extension: \".json\",\n});\nexports.add(\"Json\", [\"0x5B\"], {\n    mime: \"application/json\",\n    extension: \".json\",\n});\nexports.add(\"ELF\", [\"0x7F\", \"0x45\", \"0x4C\", \"0x46\"], {\n    mime: \"application/x-executable\",\n    extension: \".elf\",\n});\nexports.add(\"Mach-O\", [\"0xFE\", \"0xED\", \"0xFA\", \"0xC\"], {\n    mime: \"application/x-mach-binary\",\n    extension: \".o\",\n});\nexports.add(\"Mach-O\", [\"0xFE\", \"0xED\", \"0xFA\", \"0xCF\"], {\n    mime: \"application/x-executable\",\n    extension: \"elf\",\n});\nexports.add(\"EML\", [\"0x52\", \"0x65\", \"0x63\", \"0x65\", \"0x69\", \"0x76\", \"0x65\", \"0x64\", \"0x3A\"], {\n    mime: \"message/rfc822\",\n    extension: \".eml\",\n});\nexports.add(\"SVG\", [\"0x3c\", \"0x73\", \"0x76\", \"0x67\"], {\n    mime: \"image/svg+xml\",\n    extension: \"svg\",\n});\nexports.add(\"avif\", [\"0x66\", \"0x74\", \"0x79\", \"0x70\", \"0x61\", \"0x76\", \"0x69\", \"0x66\"], {\n    mime: \"image/avif\",\n    extension: \"avif\",\n}, 4);\nconst createTree = () => tree;\nexports.createTree = createTree;\nexports[\"default\"] = () => tree;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/magic-bytes.js/dist/model/pattern-tree.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/magic-bytes.js/dist/model/toHex.js":
/*!*********************************************************!*\
  !*** ./node_modules/magic-bytes.js/dist/model/toHex.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.fromHex = exports.toHex = void 0;\nconst hex = (num) => new Number(num).toString(16).toLowerCase();\nconst toHex = (num) => `0x${hex(num).length === 1 ? \"0\" + hex(num) : hex(num)}`;\nexports.toHex = toHex;\nconst fromHex = (hex) => new Number(hex);\nexports.fromHex = fromHex;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWFnaWMtYnl0ZXMuanMvZGlzdC9tb2RlbC90b0hleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxlQUFlLEdBQUcsYUFBYTtBQUMvQjtBQUNBLDRCQUE0QixrREFBa0Q7QUFDOUUsYUFBYTtBQUNiO0FBQ0EsZUFBZSIsInNvdXJjZXMiOlsiRDpcXGNvZGVcXGNoYXRkb2MtdjFcXG5vZGVfbW9kdWxlc1xcbWFnaWMtYnl0ZXMuanNcXGRpc3RcXG1vZGVsXFx0b0hleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuZnJvbUhleCA9IGV4cG9ydHMudG9IZXggPSB2b2lkIDA7XG5jb25zdCBoZXggPSAobnVtKSA9PiBuZXcgTnVtYmVyKG51bSkudG9TdHJpbmcoMTYpLnRvTG93ZXJDYXNlKCk7XG5jb25zdCB0b0hleCA9IChudW0pID0+IGAweCR7aGV4KG51bSkubGVuZ3RoID09PSAxID8gXCIwXCIgKyBoZXgobnVtKSA6IGhleChudW0pfWA7XG5leHBvcnRzLnRvSGV4ID0gdG9IZXg7XG5jb25zdCBmcm9tSGV4ID0gKGhleCkgPT4gbmV3IE51bWJlcihoZXgpO1xuZXhwb3J0cy5mcm9tSGV4ID0gZnJvbUhleDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/magic-bytes.js/dist/model/toHex.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/magic-bytes.js/dist/model/tree.js":
/*!********************************************************!*\
  !*** ./node_modules/magic-bytes.js/dist/model/tree.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createComplexNode = exports.createNode = exports.merge = void 0;\nconst createMatch = (leaf) => ({\n    typename: leaf.typename,\n    mime: leaf.info.mime,\n    extension: leaf.info.extension,\n});\nconst isLeafNode = (tree, path) => tree && path.length === 0;\nconst merge = (node, tree) => {\n    if (node.bytes.length === 0)\n        return tree;\n    const [currentByte, ...path] = node.bytes;\n    const currentTree = tree.bytes[currentByte];\n    // traversed to end. Just add key to leaf.\n    if (isLeafNode(currentTree, path)) {\n        const matchingNode = tree.bytes[currentByte];\n        tree.bytes[currentByte] = {\n            ...matchingNode,\n            matches: [\n                ...(matchingNode.matches ?? []),\n                createMatch(node),\n            ],\n        };\n        return tree;\n    }\n    // Path exists already, Merge subtree\n    if (tree.bytes[currentByte]) {\n        tree.bytes[currentByte] = exports.merge(exports.createNode(node.typename, path, node.info), tree.bytes[currentByte]);\n    }\n    else { // Tree did not exist before\n        tree.bytes[currentByte] = exports.createComplexNode(node.typename, path, node.info);\n    }\n    return tree;\n};\nexports.merge = merge;\nconst createNode = (typename, bytes, info) => {\n    return { typename, bytes, info: info ? info : {} };\n};\nexports.createNode = createNode;\nconst createComplexNode = (typename, bytes, info) => {\n    let obj = {\n        bytes: {},\n        matches: undefined,\n    };\n    const [currentKey, ...path] = bytes;\n    if (bytes.length === 0) {\n        return {\n            matches: [\n                createMatch({\n                    typename: typename,\n                    info: info ? { extension: info.extension, mime: info.mime } : {},\n                }),\n            ],\n            bytes: {},\n        };\n    }\n    obj.bytes[currentKey] = exports.createComplexNode(typename, path, info);\n    return obj;\n};\nexports.createComplexNode = createComplexNode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/magic-bytes.js/dist/model/tree.js\n");

/***/ })

};
;