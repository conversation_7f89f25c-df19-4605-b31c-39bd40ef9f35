{"name": "llamaindex", "version": "0.11.13", "license": "MIT", "type": "module", "keywords": ["llm", "llama", "openai", "gpt", "data science", "prompt", "prompt engineering", "chatgpt", "machine learning", "ml", "embedding", "vectorstore", "data framework", "llamaindex"], "dependencies": {"@types/lodash": "^4.17.7", "@types/node": "^22.9.0", "lodash": "^4.17.21", "magic-bytes.js": "^1.10.0", "@llamaindex/cloud": "4.0.18", "@llamaindex/core": "0.6.13", "@llamaindex/env": "0.1.30", "@llamaindex/node-parser": "2.0.13", "@llamaindex/workflow": "1.1.13"}, "devDependencies": {"ajv": "^8.17.1", "@types/node": "^22.9.0"}, "engines": {"node": ">=18.0.0"}, "types": "./dist/type/index.d.ts", "main": "./dist/cjs/index.js", "exports": {".": {"react-server": {"types": "./dist/type/index.react-server.d.ts", "default": "./dist/index.react-server.js"}, "workerd": {"types": "./dist/type/index.workerd.d.ts", "default": "./dist/index.workerd.js"}, "edge-light": {"types": "./dist/type/index.edge.d.ts", "default": "./dist/index.edge.js"}, "import": {"types": "./dist/type/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/type/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./agent": {"require": {"types": "./agent/dist/index.d.cts", "default": "./agent/dist/index.cjs"}, "import": {"types": "./agent/dist/index.d.ts", "default": "./agent/dist/index.js"}, "default": "./agent/dist/index.js"}, "./cloud": {"require": {"types": "./cloud/dist/index.d.cts", "default": "./cloud/dist/index.cjs"}, "import": {"types": "./cloud/dist/index.d.ts", "default": "./cloud/dist/index.js"}, "default": "./cloud/dist/index.js"}, "./engines": {"require": {"types": "./engines/dist/index.d.cts", "default": "./engines/dist/index.cjs"}, "import": {"types": "./engines/dist/index.d.ts", "default": "./engines/dist/index.js"}, "default": "./engines/dist/index.js"}, "./evaluation": {"require": {"types": "./evaluation/dist/index.d.cts", "default": "./evaluation/dist/index.cjs"}, "import": {"types": "./evaluation/dist/index.d.ts", "default": "./evaluation/dist/index.js"}, "default": "./evaluation/dist/index.js"}, "./extractors": {"require": {"types": "./extractors/dist/index.d.cts", "default": "./extractors/dist/index.cjs"}, "import": {"types": "./extractors/dist/index.d.ts", "default": "./extractors/dist/index.js"}, "default": "./extractors/dist/index.js"}, "./indices": {"require": {"types": "./indices/dist/index.d.cts", "default": "./indices/dist/index.cjs"}, "import": {"types": "./indices/dist/index.d.ts", "default": "./indices/dist/index.js"}, "default": "./indices/dist/index.js"}, "./ingestion": {"require": {"types": "./ingestion/dist/index.d.cts", "default": "./ingestion/dist/index.cjs"}, "import": {"types": "./ingestion/dist/index.d.ts", "default": "./ingestion/dist/index.js"}, "default": "./ingestion/dist/index.js"}, "./objects": {"require": {"types": "./objects/dist/index.d.cts", "default": "./objects/dist/index.cjs"}, "import": {"types": "./objects/dist/index.d.ts", "default": "./objects/dist/index.js"}, "default": "./objects/dist/index.js"}, "./node-parser": {"require": {"types": "./node-parser/dist/index.d.cts", "default": "./node-parser/dist/index.cjs"}, "import": {"types": "./node-parser/dist/index.d.ts", "default": "./node-parser/dist/index.js"}, "default": "./node-parser/dist/index.js"}, "./postprocessors": {"require": {"types": "./postprocessors/dist/index.d.cts", "default": "./postprocessors/dist/index.cjs"}, "import": {"types": "./postprocessors/dist/index.d.ts", "default": "./postprocessors/dist/index.js"}, "default": "./postprocessors/dist/index.js"}, "./selectors": {"require": {"types": "./selectors/dist/index.d.cts", "default": "./selectors/dist/index.cjs"}, "import": {"types": "./selectors/dist/index.d.ts", "default": "./selectors/dist/index.js"}, "default": "./selectors/dist/index.js"}, "./storage": {"require": {"types": "./storage/dist/index.d.cts", "default": "./storage/dist/index.cjs"}, "import": {"types": "./storage/dist/index.d.ts", "default": "./storage/dist/index.js"}, "default": "./storage/dist/index.js"}, "./tools": {"require": {"types": "./tools/dist/index.d.cts", "default": "./tools/dist/index.cjs"}, "import": {"types": "./tools/dist/index.d.ts", "default": "./tools/dist/index.js"}, "default": "./tools/dist/index.js"}, "./vector-store": {"require": {"types": "./vector-store/dist/index.d.cts", "default": "./vector-store/dist/index.cjs"}, "import": {"types": "./vector-store/dist/index.d.ts", "default": "./vector-store/dist/index.js"}, "default": "./vector-store/dist/index.js"}, "./next": {"require": {"types": "./next/dist/index.d.cts", "default": "./next/dist/index.cjs"}, "import": {"types": "./next/dist/index.d.ts", "default": "./next/dist/index.js"}, "default": "./next/dist/index.js"}}, "files": ["dist", "agent", "cloud", "engines", "evaluation", "extractors", "indices", "ingestion", "objects", "node-parser", "postprocessors", "selectors", "storage", "tools", "vector-store", "next", "CHANGELOG.md", "examples", "!**/*.tsbuildinfo"], "repository": {"type": "git", "url": "git+https://github.com/run-llama/LlamaIndexTS.git", "directory": "packages/llamaindex"}, "scripts": {"lint": "eslint .", "build": "bunchee", "copy": "cp -r ../../README.md ../../LICENSE .", "postbuild": "pnpm run copy && node -e \"require('fs').writeFileSync('./dist/cjs/package.json', JSON.stringify({ type: 'commonjs' }))\"", "dev": "bunchee --watch"}}