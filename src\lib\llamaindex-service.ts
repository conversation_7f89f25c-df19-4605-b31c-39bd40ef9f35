import {
  Document,
  VectorStoreIndex,
  SimpleDirectoryReader,
  OpenAI,
  Settings,
  storageContextFromDefaults,
  ContextChatEngine,
} from "llamaindex";
import path from "path";
import { existsSync } from "fs";

// Configure LlamaIndex settings
Settings.llm = new OpenAI({
  model: "gpt-4",
  apiKey: process.env.OPENAI_API_KEY,
});

interface DocumentMetadata {
  id: string;
  filename: string;
  uploadDate: string;
}

interface QueryResult {
  response: string;
  sources: Array<{
    document: string;
    chunk: string;
    relevance: number;
  }>;
}

class LlamaIndexService {
  private index: VectorStoreIndex | null = null;
  private chatEngine: ContextChatEngine | null = null;
  private documents: Map<string, DocumentMetadata> = new Map();
  private readonly uploadDir = path.join(process.cwd(), 'uploads');
  private readonly indexDir = path.join(process.cwd(), 'index_storage');

  async initialize() {
    try {
      // Try to load existing index
      if (existsSync(this.indexDir)) {
        const storageContext = await storageContextFromDefaults({
          persistDir: this.indexDir,
        });
        this.index = await VectorStoreIndex.init({
          storageContext,
        });
        console.log("Loaded existing index");
      } else {
        // Create new empty index
        this.index = await VectorStoreIndex.fromDocuments([]);
        console.log("Created new index");
      }

      // Initialize chat engine
      this.chatEngine = this.index.asChatEngine({
        chatMode: "context",
      });

    } catch (error) {
      console.error("Failed to initialize LlamaIndex:", error);
      throw error;
    }
  }

  async addDocument(filePath: string, documentId: string, filename: string): Promise<void> {
    try {
      if (!this.index) {
        await this.initialize();
      }

      // Read the document
      const reader = new SimpleDirectoryReader();
      const documents = await reader.loadData({
        directoryPath: path.dirname(filePath),
        fileFilter: (fileName) => fileName === path.basename(filePath),
      });

      if (documents.length === 0) {
        throw new Error("No documents found to index");
      }

      // Add metadata to documents
      documents.forEach((doc) => {
        doc.metadata = {
          ...doc.metadata,
          documentId,
          filename,
          uploadDate: new Date().toISOString(),
        };
      });

      // Add documents to index
      for (const document of documents) {
        await this.index!.insertNodes([document]);
      }

      // Store document metadata
      this.documents.set(documentId, {
        id: documentId,
        filename,
        uploadDate: new Date().toISOString(),
      });

      // Persist the index
      await this.persistIndex();

      console.log(`Document ${filename} added to index`);
    } catch (error) {
      console.error("Failed to add document:", error);
      throw error;
    }
  }

  async removeDocument(documentId: string): Promise<void> {
    try {
      if (!this.index) {
        await this.initialize();
      }

      // Note: LlamaIndex doesn't have a direct way to remove documents by metadata
      // In a production environment, you might want to rebuild the index
      // For now, we'll just remove from our metadata tracking
      this.documents.delete(documentId);

      console.log(`Document ${documentId} removed from tracking`);
    } catch (error) {
      console.error("Failed to remove document:", error);
      throw error;
    }
  }

  async query(message: string, chatHistory: Array<{ role: string; content: string }> = []): Promise<QueryResult> {
    try {
      if (!this.index || !this.chatEngine) {
        await this.initialize();
      }

      // Query the index
      const response = await this.chatEngine!.chat({
        message,
        chatHistory,
      });

      // Extract sources from the response
      const sources = response.sourceNodes?.map((node) => ({
        document: node.metadata?.filename || "Unknown document",
        chunk: node.getContent().substring(0, 200) + "...",
        relevance: node.score || 0,
      })) || [];

      return {
        response: response.response,
        sources,
      };
    } catch (error) {
      console.error("Failed to query:", error);
      throw error;
    }
  }

  async getDocuments(): Promise<DocumentMetadata[]> {
    return Array.from(this.documents.values());
  }

  private async persistIndex(): Promise<void> {
    try {
      if (this.index) {
        const storageContext = await storageContextFromDefaults({
          persistDir: this.indexDir,
        });
        await this.index.storageContext.persist(this.indexDir);
      }
    } catch (error) {
      console.error("Failed to persist index:", error);
    }
  }
}

// Singleton instance
const llamaIndexService = new LlamaIndexService();

export default llamaIndexService;
