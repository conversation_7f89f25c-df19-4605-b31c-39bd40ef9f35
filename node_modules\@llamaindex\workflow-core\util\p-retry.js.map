{"version": 3, "file": "p-retry.js", "names": ["handler: <PERSON><PERSON><AcceptEvents, Result>", "options: Options"], "sources": ["../src/util/p-retry.ts"], "sourcesContent": ["import type { Handler, WorkflowEvent } from \"@llamaindex/workflow-core\";\nimport type { Options } from \"p-retry\";\nimport pRetry from \"p-retry\";\n\nexport function pRetryHandler<\n  const AcceptEvents extends WorkflowEvent<any>[],\n  Result extends ReturnType<WorkflowEvent<any>[\"with\"]> | void,\n>(\n  handler: <PERSON><PERSON><AcceptEvents, Result>,\n  options: Options,\n): <PERSON><PERSON><AcceptEvents, Result> {\n  return async (\n    ...args: Parameters<Handler<AcceptEvents, Result>>\n  ): Promise<Result> => {\n    const fn = () => handler(...args);\n    return pRetry(fn, options);\n  };\n}\n"], "mappings": ";;;AAIA,SAAgB,cAIdA,SACAC,SAC+B;AAC/B,QAAO,OACL,GAAG,SACiB;EACpB,MAAM,KAAK,MAAM,QAAQ,GAAG,KAAK;AACjC,SAAO,OAAO,IAAI,QAAQ;CAC3B;AACF"}