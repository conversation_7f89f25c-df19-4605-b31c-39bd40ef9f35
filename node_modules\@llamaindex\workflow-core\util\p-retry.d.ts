import { Options } from "p-retry";
import { Hand<PERSON>, WorkflowEvent } from "@llamaindex/workflow-core";

//#region src/util/p-retry.d.ts
declare function pRetryHandler<const AcceptEvents extends WorkflowEvent<any>[], Result extends ReturnType<WorkflowEvent<any>["with"]> | void>(handler: <PERSON><PERSON><AcceptEvents, Result>, options: Options): Handler<AcceptEvents, Result>;
//#endregion
export { pRetryHandler };
//# sourceMappingURL=p-retry.d.ts.map