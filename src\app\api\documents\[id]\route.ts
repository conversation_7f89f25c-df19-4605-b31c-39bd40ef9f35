import { NextRequest, NextResponse } from 'next/server';
import { unlink } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import llamaIndexService from '@/lib/llamaindex-service';

const UPLOAD_DIR = path.join(process.cwd(), 'uploads');

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      );
    }

    // Find the file with this ID (check both .txt and .md extensions)
    const possibleExtensions = ['.txt', '.md'];
    let filePath: string | null = null;

    for (const ext of possibleExtensions) {
      const testPath = path.join(UPLOAD_DIR, `${id}${ext}`);
      if (existsSync(testPath)) {
        filePath = testPath;
        break;
      }
    }

    if (!filePath) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Delete the file
    await unlink(filePath);

    // Remove from LlamaIndex vector store
    try {
      await llamaIndexService.removeDocument(id);
    } catch (indexError) {
      console.error('Failed to remove document from index:', indexError);
      // Continue even if index removal fails - file is still deleted
    }

    return NextResponse.json({
      message: 'Document deleted successfully'
    });

  } catch (error) {
    console.error('Delete error:', error);
    return NextResponse.json(
      { error: 'Failed to delete document' },
      { status: 500 }
    );
  }
}
