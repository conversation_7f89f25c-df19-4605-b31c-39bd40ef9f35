/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_code_chatdoc_v1_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_code_chatdoc_v1_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_llamaindex_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/llamaindex-service */ \"(rsc)/./src/lib/llamaindex-service.ts\");\n\n\nasync function POST(request) {\n    try {\n        const { message, history } = await request.json();\n        if (!message || typeof message !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message is required'\n            }, {\n                status: 400\n            });\n        }\n        // Check if DeepSeek API key is configured\n        if (!process.env.DEEPSEEK_API_KEY || process.env.DEEPSEEK_API_KEY === 'your_deepseek_api_key_here') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: \"⚠️ DeepSeek API key is not configured. Please add your DeepSeek API key to the .env.local file to enable AI-powered responses.\\n\\nTo get started:\\n1. Get an API key from https://platform.deepseek.com/api_keys\\n2. Add it to your .env.local file as DEEPSEEK_API_KEY=your_key_here\\n3. Restart the development server\\n\\n🔧 **DeepSeek Integration Active** - This application now uses DeepSeek's reasoning models for enhanced document analysis!\",\n                sources: [],\n                timestamp: new Date().toISOString()\n            });\n        }\n        // Convert chat history to LlamaIndex format\n        const chatHistory = (history || []).map((msg)=>({\n                role: msg.type === 'user' ? 'user' : 'assistant',\n                content: msg.content\n            }));\n        try {\n            // Query using LlamaIndex with DeepSeek\n            const result = await _lib_llamaindex_service__WEBPACK_IMPORTED_MODULE_1__[\"default\"].query(message, chatHistory);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: result.response,\n                sources: result.sources,\n                timestamp: new Date().toISOString()\n            });\n        } catch (queryError) {\n            console.error('DeepSeek LlamaIndex query error:', queryError);\n            // Fallback response if DeepSeek/LlamaIndex fails\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: `I apologize, but I encountered an error while processing your question: \"${message}\".\n\n**Possible issues:**\n- DeepSeek API key might be invalid or expired\n- No documents have been uploaded yet\n- Network connectivity issues\n\n**🔧 DeepSeek Integration Status:**\n- ✅ DeepSeek API configured\n- ⚠️ Error occurred during query processing\n\nPlease check your API key and ensure you have uploaded some documents, then try again.\n\n**Error details:** ${queryError instanceof Error ? queryError.message : 'Unknown error'}`,\n                sources: [],\n                timestamp: new Date().toISOString()\n            });\n        }\n    } catch (error) {\n        console.error('Chat error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process chat message'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jaGF0L3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF3RDtBQUNDO0FBZ0JsRCxlQUFlRSxLQUFLQyxPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLE9BQU8sRUFBRSxHQUFHLE1BQU1GLFFBQVFHLElBQUk7UUFFL0MsSUFBSSxDQUFDRixXQUFXLE9BQU9BLFlBQVksVUFBVTtZQUMzQyxPQUFPSixxREFBWUEsQ0FBQ00sSUFBSSxDQUN0QjtnQkFBRUMsT0FBTztZQUFzQixHQUMvQjtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsMENBQTBDO1FBQzFDLElBQUksQ0FBQ0MsUUFBUUMsR0FBRyxDQUFDQyxnQkFBZ0IsSUFBSUYsUUFBUUMsR0FBRyxDQUFDQyxnQkFBZ0IsS0FBSyw4QkFBOEI7WUFDbEcsT0FBT1gscURBQVlBLENBQUNNLElBQUksQ0FBQztnQkFDdkJNLFVBQVU7Z0JBQ1ZDLFNBQVMsRUFBRTtnQkFDWEMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ25DO1FBQ0Y7UUFFQSw0Q0FBNEM7UUFDNUMsTUFBTUMsY0FBYyxDQUFDWixXQUFXLEVBQUUsRUFBRWEsR0FBRyxDQUFDLENBQUNDLE1BQXNCO2dCQUM3REMsTUFBTUQsSUFBSUUsSUFBSSxLQUFLLFNBQVMsU0FBUztnQkFDckNDLFNBQVNILElBQUlHLE9BQU87WUFDdEI7UUFFQSxJQUFJO1lBQ0YsdUNBQXVDO1lBQ3ZDLE1BQU1DLFNBQVMsTUFBTXRCLCtEQUFpQkEsQ0FBQ3VCLEtBQUssQ0FBQ3BCLFNBQVNhO1lBRXRELE9BQU9qQixxREFBWUEsQ0FBQ00sSUFBSSxDQUFDO2dCQUN2Qk0sVUFBVVcsT0FBT1gsUUFBUTtnQkFDekJDLFNBQVNVLE9BQU9WLE9BQU87Z0JBQ3ZCQyxXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDbkM7UUFDRixFQUFFLE9BQU9TLFlBQVk7WUFDbkJDLFFBQVFuQixLQUFLLENBQUMsb0NBQW9Da0I7WUFFbEQsaURBQWlEO1lBQ2pELE9BQU96QixxREFBWUEsQ0FBQ00sSUFBSSxDQUFDO2dCQUN2Qk0sVUFBVSxDQUFDLHlFQUF5RSxFQUFFUixRQUFROzs7Ozs7Ozs7Ozs7O21CQWFuRixFQUFFcUIsc0JBQXNCRSxRQUFRRixXQUFXckIsT0FBTyxHQUFHLGlCQUFpQjtnQkFDakZTLFNBQVMsRUFBRTtnQkFDWEMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ25DO1FBQ0Y7SUFFRixFQUFFLE9BQU9ULE9BQU87UUFDZG1CLFFBQVFuQixLQUFLLENBQUMsZUFBZUE7UUFDN0IsT0FBT1AscURBQVlBLENBQUNNLElBQUksQ0FDdEI7WUFBRUMsT0FBTztRQUFpQyxHQUMxQztZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRiIsInNvdXJjZXMiOlsiRDpcXGNvZGVcXGNoYXRkb2MtdjFcXHNyY1xcYXBwXFxhcGlcXGNoYXRcXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgbGxhbWFJbmRleFNlcnZpY2UgZnJvbSAnQC9saWIvbGxhbWFpbmRleC1zZXJ2aWNlJztcblxuaW50ZXJmYWNlIENoYXRNZXNzYWdlIHtcbiAgaWQ6IHN0cmluZztcbiAgdHlwZTogJ3VzZXInIHwgJ2Fzc2lzdGFudCc7XG4gIGNvbnRlbnQ6IHN0cmluZztcbiAgc291cmNlcz86IFNvdXJjZVtdO1xuICB0aW1lc3RhbXA6IERhdGU7XG59XG5cbmludGVyZmFjZSBTb3VyY2Uge1xuICBkb2N1bWVudDogc3RyaW5nO1xuICBjaHVuazogc3RyaW5nO1xuICByZWxldmFuY2U6IG51bWJlcjtcbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IG1lc3NhZ2UsIGhpc3RvcnkgfSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xuXG4gICAgaWYgKCFtZXNzYWdlIHx8IHR5cGVvZiBtZXNzYWdlICE9PSAnc3RyaW5nJykge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnTWVzc2FnZSBpcyByZXF1aXJlZCcgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIERlZXBTZWVrIEFQSSBrZXkgaXMgY29uZmlndXJlZFxuICAgIGlmICghcHJvY2Vzcy5lbnYuREVFUFNFRUtfQVBJX0tFWSB8fCBwcm9jZXNzLmVudi5ERUVQU0VFS19BUElfS0VZID09PSAneW91cl9kZWVwc2Vla19hcGlfa2V5X2hlcmUnKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICByZXNwb25zZTogXCLimqDvuI8gRGVlcFNlZWsgQVBJIGtleSBpcyBub3QgY29uZmlndXJlZC4gUGxlYXNlIGFkZCB5b3VyIERlZXBTZWVrIEFQSSBrZXkgdG8gdGhlIC5lbnYubG9jYWwgZmlsZSB0byBlbmFibGUgQUktcG93ZXJlZCByZXNwb25zZXMuXFxuXFxuVG8gZ2V0IHN0YXJ0ZWQ6XFxuMS4gR2V0IGFuIEFQSSBrZXkgZnJvbSBodHRwczovL3BsYXRmb3JtLmRlZXBzZWVrLmNvbS9hcGlfa2V5c1xcbjIuIEFkZCBpdCB0byB5b3VyIC5lbnYubG9jYWwgZmlsZSBhcyBERUVQU0VFS19BUElfS0VZPXlvdXJfa2V5X2hlcmVcXG4zLiBSZXN0YXJ0IHRoZSBkZXZlbG9wbWVudCBzZXJ2ZXJcXG5cXG7wn5SnICoqRGVlcFNlZWsgSW50ZWdyYXRpb24gQWN0aXZlKiogLSBUaGlzIGFwcGxpY2F0aW9uIG5vdyB1c2VzIERlZXBTZWVrJ3MgcmVhc29uaW5nIG1vZGVscyBmb3IgZW5oYW5jZWQgZG9jdW1lbnQgYW5hbHlzaXMhXCIsXG4gICAgICAgIHNvdXJjZXM6IFtdLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gQ29udmVydCBjaGF0IGhpc3RvcnkgdG8gTGxhbWFJbmRleCBmb3JtYXRcbiAgICBjb25zdCBjaGF0SGlzdG9yeSA9IChoaXN0b3J5IHx8IFtdKS5tYXAoKG1zZzogQ2hhdE1lc3NhZ2UpID0+ICh7XG4gICAgICByb2xlOiBtc2cudHlwZSA9PT0gJ3VzZXInID8gJ3VzZXInIDogJ2Fzc2lzdGFudCcsXG4gICAgICBjb250ZW50OiBtc2cuY29udGVudCxcbiAgICB9KSk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gUXVlcnkgdXNpbmcgTGxhbWFJbmRleCB3aXRoIERlZXBTZWVrXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBsbGFtYUluZGV4U2VydmljZS5xdWVyeShtZXNzYWdlLCBjaGF0SGlzdG9yeSk7XG5cbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIHJlc3BvbnNlOiByZXN1bHQucmVzcG9uc2UsXG4gICAgICAgIHNvdXJjZXM6IHJlc3VsdC5zb3VyY2VzLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAocXVlcnlFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRGVlcFNlZWsgTGxhbWFJbmRleCBxdWVyeSBlcnJvcjonLCBxdWVyeUVycm9yKTtcblxuICAgICAgLy8gRmFsbGJhY2sgcmVzcG9uc2UgaWYgRGVlcFNlZWsvTGxhbWFJbmRleCBmYWlsc1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgcmVzcG9uc2U6IGBJIGFwb2xvZ2l6ZSwgYnV0IEkgZW5jb3VudGVyZWQgYW4gZXJyb3Igd2hpbGUgcHJvY2Vzc2luZyB5b3VyIHF1ZXN0aW9uOiBcIiR7bWVzc2FnZX1cIi5cblxuKipQb3NzaWJsZSBpc3N1ZXM6Kipcbi0gRGVlcFNlZWsgQVBJIGtleSBtaWdodCBiZSBpbnZhbGlkIG9yIGV4cGlyZWRcbi0gTm8gZG9jdW1lbnRzIGhhdmUgYmVlbiB1cGxvYWRlZCB5ZXRcbi0gTmV0d29yayBjb25uZWN0aXZpdHkgaXNzdWVzXG5cbioq8J+UpyBEZWVwU2VlayBJbnRlZ3JhdGlvbiBTdGF0dXM6Kipcbi0g4pyFIERlZXBTZWVrIEFQSSBjb25maWd1cmVkXG4tIOKaoO+4jyBFcnJvciBvY2N1cnJlZCBkdXJpbmcgcXVlcnkgcHJvY2Vzc2luZ1xuXG5QbGVhc2UgY2hlY2sgeW91ciBBUEkga2V5IGFuZCBlbnN1cmUgeW91IGhhdmUgdXBsb2FkZWQgc29tZSBkb2N1bWVudHMsIHRoZW4gdHJ5IGFnYWluLlxuXG4qKkVycm9yIGRldGFpbHM6KiogJHtxdWVyeUVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBxdWVyeUVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcid9YCxcbiAgICAgICAgc291cmNlczogW10sXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KTtcbiAgICB9XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdDaGF0IGVycm9yOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIHByb2Nlc3MgY2hhdCBtZXNzYWdlJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImxsYW1hSW5kZXhTZXJ2aWNlIiwiUE9TVCIsInJlcXVlc3QiLCJtZXNzYWdlIiwiaGlzdG9yeSIsImpzb24iLCJlcnJvciIsInN0YXR1cyIsInByb2Nlc3MiLCJlbnYiLCJERUVQU0VFS19BUElfS0VZIiwicmVzcG9uc2UiLCJzb3VyY2VzIiwidGltZXN0YW1wIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiY2hhdEhpc3RvcnkiLCJtYXAiLCJtc2ciLCJyb2xlIiwidHlwZSIsImNvbnRlbnQiLCJyZXN1bHQiLCJxdWVyeSIsInF1ZXJ5RXJyb3IiLCJjb25zb2xlIiwiRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/deepseek-llm.ts":
/*!*********************************!*\
  !*** ./src/lib/deepseek-llm.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DeepSeekService: () => (/* binding */ DeepSeekService)\n/* harmony export */ });\nclass DeepSeekService {\n    constructor(config){\n        this.config = {\n            temperature: 0.7,\n            maxTokens: 4000,\n            ...config\n        };\n    }\n    async chat(messages) {\n        try {\n            const response = await fetch(`${this.config.baseURL}/chat/completions`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${this.config.apiKey}`\n                },\n                body: JSON.stringify({\n                    model: this.config.model,\n                    messages: messages.map((msg)=>({\n                            role: msg.role,\n                            content: msg.content\n                        })),\n                    temperature: this.config.temperature,\n                    max_tokens: this.config.maxTokens,\n                    stream: false\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(`DeepSeek API error: ${response.status} - ${errorData.error?.message || response.statusText}`);\n            }\n            const data = await response.json();\n            if (!data.choices || data.choices.length === 0) {\n                throw new Error('No response from DeepSeek API');\n            }\n            const choice = data.choices[0];\n            return {\n                message: {\n                    role: choice.message.role,\n                    content: choice.message.content\n                },\n                raw: data\n            };\n        } catch (error) {\n            console.error('DeepSeek LLM error:', error);\n            throw error;\n        }\n    }\n    async queryWithContext(message, context) {\n        const messages = [\n            {\n                role: 'system',\n                content: `You are a helpful AI assistant that answers questions based on the provided document context. Use the context to provide accurate and relevant answers. If the context doesn't contain enough information to answer the question, say so clearly.\n\nContext:\n${context}`\n            },\n            {\n                role: 'user',\n                content: message\n            }\n        ];\n        const response = await this.chat(messages);\n        return response.message.content;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/deepseek-llm.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/document-manager.ts":
/*!*************************************!*\
  !*** ./src/lib/document-manager.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nclass DocumentManager {\n    async initialize() {\n        try {\n            if ((0,fs__WEBPACK_IMPORTED_MODULE_1__.existsSync)(this.metadataFile)) {\n                const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.readFile)(this.metadataFile, 'utf-8');\n                const documentsArray = JSON.parse(data);\n                this.documents = new Map(documentsArray.map((doc)=>[\n                        doc.id,\n                        doc\n                    ]));\n                console.log(`Loaded ${this.documents.size} document metadata entries`);\n            }\n        } catch (error) {\n            console.error('Failed to load document metadata:', error);\n        }\n    }\n    async addDocument(metadata) {\n        const fullMetadata = {\n            ...metadata,\n            uploadDate: new Date().toISOString(),\n            lastModified: new Date().toISOString(),\n            status: 'uploading'\n        };\n        this.documents.set(metadata.id, fullMetadata);\n        await this.saveMetadata();\n        return fullMetadata;\n    }\n    async updateDocumentStatus(id, status, errorMessage) {\n        const doc = this.documents.get(id);\n        if (doc) {\n            doc.status = status;\n            doc.lastModified = new Date().toISOString();\n            if (errorMessage) {\n                doc.errorMessage = errorMessage;\n            }\n            this.documents.set(id, doc);\n            await this.saveMetadata();\n        }\n    }\n    async removeDocument(id) {\n        this.documents.delete(id);\n        await this.saveMetadata();\n    }\n    getDocument(id) {\n        return this.documents.get(id);\n    }\n    getAllDocuments() {\n        return Array.from(this.documents.values());\n    }\n    getDocumentsByStatus(status) {\n        return Array.from(this.documents.values()).filter((doc)=>doc.status === status);\n    }\n    async saveMetadata() {\n        try {\n            const documentsArray = Array.from(this.documents.values());\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.writeFile)(this.metadataFile, JSON.stringify(documentsArray, null, 2));\n        } catch (error) {\n            console.error('Failed to save document metadata:', error);\n        }\n    }\n    async getStats() {\n        const docs = Array.from(this.documents.values());\n        return {\n            total: docs.length,\n            indexed: docs.filter((d)=>d.status === 'indexed').length,\n            processing: docs.filter((d)=>d.status === 'processing').length,\n            errors: docs.filter((d)=>d.status === 'error').length,\n            totalSize: docs.reduce((sum, doc)=>sum + doc.size, 0)\n        };\n    }\n    constructor(){\n        this.metadataFile = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'documents_metadata.json');\n        this.documents = new Map();\n    }\n}\n// Singleton instance\nconst documentManager = new DocumentManager();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (documentManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/document-manager.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/llamaindex-service.ts":
/*!***************************************!*\
  !*** ./src/lib/llamaindex-service.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _document_manager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./document-manager */ \"(rsc)/./src/lib/document-manager.ts\");\n/* harmony import */ var _deepseek_llm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./deepseek-llm */ \"(rsc)/./src/lib/deepseek-llm.ts\");\n// Simplified document service using DeepSeek directly\n\n\n\n// Create DeepSeek service instance\nconst deepSeekService = new _deepseek_llm__WEBPACK_IMPORTED_MODULE_2__.DeepSeekService({\n    apiKey: process.env.DEEPSEEK_API_KEY || \"\",\n    baseURL: process.env.DEEPSEEK_BASE_URL || \"https://api.deepseek.com\",\n    model: process.env.DEEPSEEK_CHAT_MODEL || \"deepseek-chat\",\n    temperature: 0.7,\n    maxTokens: 4000\n});\nclass LlamaIndexService {\n    async initialize() {\n        try {\n            // Initialize document manager\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].initialize();\n            console.log(\"Document service initialized with DeepSeek integration\");\n        } catch (error) {\n            console.error(\"Failed to initialize document service:\", error);\n            throw error;\n        }\n    }\n    async addDocument(filePath, documentId, filename, fileSize) {\n        try {\n            await this.initialize();\n            // Update document status to processing\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateDocumentStatus(documentId, 'processing');\n            // Read the document content directly\n            const fs = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! fs/promises */ \"fs/promises\", 23));\n            const content = await fs.readFile(filePath, 'utf-8');\n            // Store document in memory for now (in production, you'd use a proper vector database)\n            this.documents.set(documentId, {\n                content,\n                metadata: {\n                    id: documentId,\n                    filename,\n                    originalName: filename,\n                    size: fileSize,\n                    uploadDate: new Date().toISOString(),\n                    lastModified: new Date().toISOString(),\n                    type: path__WEBPACK_IMPORTED_MODULE_0___default().extname(filename),\n                    status: 'indexed'\n                }\n            });\n            // Update document status to indexed\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateDocumentStatus(documentId, 'indexed');\n            console.log(`Document ${filename} added to document store`);\n        } catch (error) {\n            console.error(\"Failed to add document:\", error);\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateDocumentStatus(documentId, 'error', error instanceof Error ? error.message : 'Unknown error');\n            throw error;\n        }\n    }\n    async removeDocument(documentId) {\n        try {\n            await this.initialize();\n            // Remove from document manager\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].removeDocument(documentId);\n            // Remove from our document store\n            this.documents.delete(documentId);\n            console.log(`Document ${documentId} removed from document store`);\n        } catch (error) {\n            console.error(\"Failed to remove document:\", error);\n            throw error;\n        }\n    }\n    async query(message, _chatHistory = []) {\n        try {\n            await this.initialize();\n            // Get all document contents for context\n            const documentContents = Array.from(this.documents.values());\n            if (documentContents.length === 0) {\n                return {\n                    response: \"I don't have any documents to search through. Please upload some documents first and wait for them to be processed.\",\n                    sources: []\n                };\n            }\n            // Create context from all documents\n            const context = documentContents.map((doc)=>`Document: ${doc.metadata.filename}\\nContent: ${doc.content}`).join('\\n\\n---\\n\\n');\n            // Use DeepSeek to answer the question with context\n            const response = await deepSeekService.queryWithContext(message, context);\n            // Create sources from the documents (simple approach)\n            const sources = documentContents.map((doc)=>({\n                    document: doc.metadata.filename,\n                    chunk: doc.content.substring(0, 200) + \"...\",\n                    relevance: 0.8\n                }));\n            return {\n                response,\n                sources\n            };\n        } catch (error) {\n            console.error(\"Failed to query with DeepSeek:\", error);\n            throw error;\n        }\n    }\n    async getDocuments() {\n        return Array.from(this.documents.values()).map((doc)=>doc.metadata);\n    }\n    constructor(){\n        this.documents = new Map();\n    }\n}\n// Singleton instance\nconst llamaIndexService = new LlamaIndexService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (llamaIndexService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/llamaindex-service.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();