/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_code_chatdoc_v1_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_code_chatdoc_v1_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_llamaindex_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/llamaindex-service */ \"(rsc)/./src/lib/llamaindex-service.ts\");\n\n\nasync function POST(request) {\n    try {\n        const { message, history } = await request.json();\n        if (!message || typeof message !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message is required'\n            }, {\n                status: 400\n            });\n        }\n        // Check if OpenAI API key is configured\n        if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: \"⚠️ OpenAI API key is not configured. Please add your OpenAI API key to the .env.local file to enable AI-powered responses.\\n\\nTo get started:\\n1. Get an API key from https://platform.openai.com/api-keys\\n2. Add it to your .env.local file as OPENAI_API_KEY=your_key_here\\n3. Restart the development server\",\n                sources: [],\n                timestamp: new Date().toISOString()\n            });\n        }\n        // Convert chat history to LlamaIndex format\n        const chatHistory = (history || []).map((msg)=>({\n                role: msg.type === 'user' ? 'user' : 'assistant',\n                content: msg.content\n            }));\n        try {\n            // Query using LlamaIndex\n            const result = await _lib_llamaindex_service__WEBPACK_IMPORTED_MODULE_1__[\"default\"].query(message, chatHistory);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: result.response,\n                sources: result.sources,\n                timestamp: new Date().toISOString()\n            });\n        } catch (queryError) {\n            console.error('LlamaIndex query error:', queryError);\n            // Fallback response if LlamaIndex fails\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: `I apologize, but I encountered an error while processing your question: \"${message}\". This might be because no documents have been uploaded yet, or there was an issue with the AI service. Please make sure you have uploaded some documents and try again.`,\n                sources: [],\n                timestamp: new Date().toISOString()\n            });\n        }\n    } catch (error) {\n        console.error('Chat error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process chat message'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/document-manager.ts":
/*!*************************************!*\
  !*** ./src/lib/document-manager.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nclass DocumentManager {\n    async initialize() {\n        try {\n            if ((0,fs__WEBPACK_IMPORTED_MODULE_1__.existsSync)(this.metadataFile)) {\n                const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.readFile)(this.metadataFile, 'utf-8');\n                const documentsArray = JSON.parse(data);\n                this.documents = new Map(documentsArray.map((doc)=>[\n                        doc.id,\n                        doc\n                    ]));\n                console.log(`Loaded ${this.documents.size} document metadata entries`);\n            }\n        } catch (error) {\n            console.error('Failed to load document metadata:', error);\n        }\n    }\n    async addDocument(metadata) {\n        const fullMetadata = {\n            ...metadata,\n            uploadDate: new Date().toISOString(),\n            lastModified: new Date().toISOString(),\n            status: 'uploading'\n        };\n        this.documents.set(metadata.id, fullMetadata);\n        await this.saveMetadata();\n        return fullMetadata;\n    }\n    async updateDocumentStatus(id, status, errorMessage) {\n        const doc = this.documents.get(id);\n        if (doc) {\n            doc.status = status;\n            doc.lastModified = new Date().toISOString();\n            if (errorMessage) {\n                doc.errorMessage = errorMessage;\n            }\n            this.documents.set(id, doc);\n            await this.saveMetadata();\n        }\n    }\n    async removeDocument(id) {\n        this.documents.delete(id);\n        await this.saveMetadata();\n    }\n    getDocument(id) {\n        return this.documents.get(id);\n    }\n    getAllDocuments() {\n        return Array.from(this.documents.values());\n    }\n    getDocumentsByStatus(status) {\n        return Array.from(this.documents.values()).filter((doc)=>doc.status === status);\n    }\n    async saveMetadata() {\n        try {\n            const documentsArray = Array.from(this.documents.values());\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.writeFile)(this.metadataFile, JSON.stringify(documentsArray, null, 2));\n        } catch (error) {\n            console.error('Failed to save document metadata:', error);\n        }\n    }\n    async getStats() {\n        const docs = Array.from(this.documents.values());\n        return {\n            total: docs.length,\n            indexed: docs.filter((d)=>d.status === 'indexed').length,\n            processing: docs.filter((d)=>d.status === 'processing').length,\n            errors: docs.filter((d)=>d.status === 'error').length,\n            totalSize: docs.reduce((sum, doc)=>sum + doc.size, 0)\n        };\n    }\n    constructor(){\n        this.metadataFile = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'documents_metadata.json');\n        this.documents = new Map();\n    }\n}\n// Singleton instance\nconst documentManager = new DocumentManager();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (documentManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/document-manager.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/llamaindex-service.ts":
/*!***************************************!*\
  !*** ./src/lib/llamaindex-service.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var llamaindex__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! llamaindex */ \"(rsc)/./node_modules/llamaindex/dist/index.react-server.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _document_manager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./document-manager */ \"(rsc)/./src/lib/document-manager.ts\");\n\n\n\n\n// Configure LlamaIndex settings\nllamaindex__WEBPACK_IMPORTED_MODULE_0__.Settings.llm = new llamaindex__WEBPACK_IMPORTED_MODULE_0__.OpenAI({\n    model: \"gpt-4\",\n    apiKey: process.env.OPENAI_API_KEY\n});\nclass LlamaIndexService {\n    async initialize() {\n        try {\n            // Initialize document manager\n            await _document_manager__WEBPACK_IMPORTED_MODULE_3__[\"default\"].initialize();\n            // Try to load existing index\n            if ((0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(this.indexDir)) {\n                const storageContext = await (0,llamaindex__WEBPACK_IMPORTED_MODULE_0__.storageContextFromDefaults)({\n                    persistDir: this.indexDir\n                });\n                this.index = await llamaindex__WEBPACK_IMPORTED_MODULE_0__.VectorStoreIndex.init({\n                    storageContext\n                });\n                console.log(\"Loaded existing index\");\n            } else {\n                // Create new empty index\n                this.index = await llamaindex__WEBPACK_IMPORTED_MODULE_0__.VectorStoreIndex.fromDocuments([]);\n                console.log(\"Created new index\");\n            }\n        // Index is ready for querying\n        } catch (error) {\n            console.error(\"Failed to initialize LlamaIndex:\", error);\n            throw error;\n        }\n    }\n    async addDocument(filePath, documentId, filename, fileSize) {\n        try {\n            if (!this.index) {\n                await this.initialize();\n            }\n            // Update document status to processing\n            await _document_manager__WEBPACK_IMPORTED_MODULE_3__[\"default\"].updateDocumentStatus(documentId, 'processing');\n            // Read the document\n            const reader = new llamaindex__WEBPACK_IMPORTED_MODULE_0__.SimpleDirectoryReader();\n            const documents = await reader.loadData({\n                directoryPath: path__WEBPACK_IMPORTED_MODULE_1___default().dirname(filePath),\n                fileFilter: (fileName)=>fileName === path__WEBPACK_IMPORTED_MODULE_1___default().basename(filePath)\n            });\n            if (documents.length === 0) {\n                throw new Error(\"No documents found to index\");\n            }\n            // Add metadata to documents\n            documents.forEach((doc)=>{\n                doc.metadata = {\n                    ...doc.metadata,\n                    documentId,\n                    filename,\n                    uploadDate: new Date().toISOString()\n                };\n            });\n            // Add documents to index\n            for (const document of documents){\n                await this.index.insertNodes([\n                    document\n                ]);\n            }\n            // Persist the index\n            await this.persistIndex();\n            // Update document status to indexed\n            await _document_manager__WEBPACK_IMPORTED_MODULE_3__[\"default\"].updateDocumentStatus(documentId, 'indexed');\n            console.log(`Document ${filename} added to index`);\n        } catch (error) {\n            console.error(\"Failed to add document:\", error);\n            await _document_manager__WEBPACK_IMPORTED_MODULE_3__[\"default\"].updateDocumentStatus(documentId, 'error', error instanceof Error ? error.message : 'Unknown error');\n            throw error;\n        }\n    }\n    async removeDocument(documentId) {\n        try {\n            if (!this.index) {\n                await this.initialize();\n            }\n            // Remove from document manager\n            await _document_manager__WEBPACK_IMPORTED_MODULE_3__[\"default\"].removeDocument(documentId);\n            // Note: LlamaIndex doesn't have a direct way to remove documents by metadata\n            // In a production environment, you might want to rebuild the index\n            // For now, we'll just remove from our metadata tracking\n            this.documents.delete(documentId);\n            console.log(`Document ${documentId} removed from tracking`);\n        } catch (error) {\n            console.error(\"Failed to remove document:\", error);\n            throw error;\n        }\n    }\n    async query(message, chatHistory = []) {\n        try {\n            if (!this.index) {\n                await this.initialize();\n            }\n            // Use query engine instead of chat engine for better compatibility\n            const queryEngine = this.index.asQueryEngine();\n            const response = await queryEngine.query({\n                query: message\n            });\n            // Extract sources from the response\n            const sources = response.sourceNodes?.map((node, index)=>({\n                    document: node.node.metadata?.filename || `Document ${index + 1}`,\n                    chunk: `Relevant content from ${node.node.metadata?.filename || 'document'}...`,\n                    relevance: node.score || 0\n                })) || [];\n            return {\n                response: response.toString(),\n                sources\n            };\n        } catch (error) {\n            console.error(\"Failed to query:\", error);\n            throw error;\n        }\n    }\n    async getDocuments() {\n        return Array.from(this.documents.values());\n    }\n    async persistIndex() {\n        try {\n            if (this.index) {\n                // The index should automatically persist when using storageContext\n                console.log(\"Index persistence handled by LlamaIndex\");\n            }\n        } catch (error) {\n            console.error(\"Failed to persist index:\", error);\n        }\n    }\n    constructor(){\n        this.index = null;\n        this.documents = new Map();\n        this.uploadDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'uploads');\n        this.indexDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'index_storage');\n    }\n}\n// Singleton instance\nconst llamaIndexService = new LlamaIndexService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (llamaIndexService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/llamaindex-service.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:fs/promises":
/*!***********************************!*\
  !*** external "node:fs/promises" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs/promises");

/***/ }),

/***/ "node:module":
/*!******************************!*\
  !*** external "node:module" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:module");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod-to-json-schema","vendor-chunks/@llamaindex","vendor-chunks/llamaindex","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/zod","vendor-chunks/magic-bytes.js","vendor-chunks/htmlparser2","vendor-chunks/peberminta","vendor-chunks/js-tiktoken","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/selderee","vendor-chunks/parseley","vendor-chunks/leac","vendor-chunks/html-to-text","vendor-chunks/domelementtype","vendor-chunks/@selderee","vendor-chunks/lodash","vendor-chunks/deepmerge","vendor-chunks/base64-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();