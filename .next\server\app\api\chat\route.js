/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_code_chatdoc_v1_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_code_chatdoc_v1_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function POST(request) {\n    try {\n        const { message, history } = await request.json();\n        if (!message || typeof message !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message is required'\n            }, {\n                status: 400\n            });\n        }\n        // Check if OpenAI API key is configured\n        if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: \"⚠️ OpenAI API key is not configured. Please add your OpenAI API key to the .env.local file to enable AI-powered responses.\\n\\nTo get started:\\n1. Get an API key from https://platform.openai.com/api-keys\\n2. Add it to your .env.local file as OPENAI_API_KEY=your_key_here\\n3. Restart the development server\",\n                sources: [],\n                timestamp: new Date().toISOString()\n            });\n        }\n        // For now, provide a demo response until LlamaIndex is properly configured\n        const demoResponse = `Thank you for your question: \"${message}\"\n\n🚧 **Demo Mode** - This is a demonstration of the ChatDoc interface.\n\nTo enable full AI-powered document chat:\n1. Configure your OpenAI API key in .env.local\n2. Upload some documents (.txt or .md files)\n3. Ask questions about your uploaded content\n\n**Current Status:**\n- ✅ Frontend interface working\n- ✅ File upload system working\n- ✅ Document management working\n- ⚠️ AI integration pending API key configuration\n\nOnce configured, I'll be able to:\n- Analyze your uploaded documents\n- Answer questions based on document content\n- Provide source attribution and relevant text chunks\n- Maintain conversation context`;\n        const demoSources = [\n            {\n                document: \"demo-document.txt\",\n                chunk: \"This is a demonstration of how source attribution will work once documents are uploaded and processed...\",\n                relevance: 0.95\n            }\n        ];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            response: demoResponse,\n            sources: demoSources,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('Chat error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process chat message'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();