import { WorkflowStream } from "@llamaindex/workflow-core";

//#region src/stream/consumer.ts
const noopStream = new WritableStream({ write: () => {} });
/**
* A no-op function that consumes a stream of events and does nothing with them.
*
* Do not collect the raw stream from `workflow.createContext()`
* or `getContext()`, it's infinite and will never finish
*
* @deprecated uss `await stream.toArray()` instead
*/
const nothing = async (stream) => {
	await stream.pipeTo(noopStream);
};
/**
* Collects all events from a stream and returns them as an array.
*
* Do not collect the raw stream from `workflow.createContext()`
* or getContext()`, it's infinite and will never finish.
*
* @deprecated uss `await stream.toArray()` instead
*/
const collect = async (stream) => {
	const events = [];
	await stream.pipeTo(new WritableStream({ write: (event) => {
		events.push(event);
	} }));
	return events;
};

//#endregion
export { collect, nothing };
//# sourceMappingURL=consumer.js.map