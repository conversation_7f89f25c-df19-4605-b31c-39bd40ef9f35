{"version": 3, "file": "index.js", "names": ["config?: WorkflowEventConfig<DebugLabel>", "instance: WorkflowEventData<any>", "data: Data", "instance: unknown", "data: unknown", "value: unknown", "acceptEventTypes: WorkflowEvent<any>[]", "inputEventData: WorkflowEventData<any>[]", "acceptance: WorkflowEventData<any>[]", "i", "subscribable: Subscribable<Args, R>", "callback: (...args: any) => any", "results: unknown[]", "currentStore: T | null", "store: T", "fn: () => R", "event: WorkflowEventData<any>", "controller: TransformStreamDefaultController<string>", "eventMap: Record<string, WorkflowEvent<any>>", "data: string", "controller: TransformStreamDefaultController<WorkflowEventData<any>>", "#eventMap", "eventMap", "event: WorkflowEvent<T>", "handler: (event: WorkflowEventData<T>) => void", "#subscribable", "subscribable: Subscribable<[R], void> | null", "rootStream: ReadableStream<R> | null", "#stream", "unsubscribe: () => void", "stream: ReadableStream<WorkflowEventData<any>>", "response: Response", "init?: ResponseInit", "reason?: any", "transform: ReadableWritablePair<T, R>", "options?: StreamPipeOptions", "destination: WritableStream<R>", "callback: (item: R) => void", "item: R", "callback: (item: R) => T", "options?: ReadableStreamIteratorOptions", "limit: number", "predicate:\n      | WorkflowEvent<InferWorkflowEventData<R>>\n      | ((event: R) => boolean)\n      | R", "predicate:\n      | WorkflowEvent<InferWorkflowEventData<R>>\n      | R\n      | ((item: R) => boolean)", "events: R[]", "queue: WorkflowEventData<any>[]", "handler: <PERSON><PERSON><WorkflowEvent<any>[], any>", "inputEvents: WorkflowEvent<any>[]", "inputs: WorkflowEventData<any>[]", "parentContext: HandlerContext", "handlerAbortController: AbortController", "handlerContext: HandlerContext", "i", "context: HandlerContext", "result: any", "lazyLoadStream: WorkflowStream | null", "newEvent: WorkflowEventData<any>", "handlerRootContext: HandlerContext", "accept: AcceptEvents", "handler: <PERSON><PERSON><AcceptEvents, Result>"], "sources": ["../../src/core/event.ts", "../../src/core/utils.ts", "../../src/async-context/index.browser.ts", "../../src/core/stream.ts", "../../src/core/context.ts", "../../src/core/workflow.ts"], "sourcesContent": ["declare const opaqueSymbol: unique symbol;\n\nconst eventMap = new WeakMap<WorkflowEvent<any>, WeakSet<object>>();\nconst refMap = new WeakMap<WorkflowEventData<any>, WorkflowEvent<any>>();\nlet i = 0;\nlet j = 0;\n\nexport type InferWorkflowEventData<T> =\n  T extends WorkflowEventData<infer U>\n    ? U\n    : T extends WorkflowEvent<infer U>\n      ? U\n      : never;\n\nexport type WorkflowEventData<Data, DebugLabel extends string = string> = {\n  get data(): Data;\n} & { readonly [opaqueSymbol]: DebugLabel };\n\nexport type WorkflowEvent<Data, DebugLabel extends string = string> = {\n  /**\n   * This is the label used for debugging purposes.\n   */\n  debugLabel?: DebugLabel;\n  /**\n   * This is the unique identifier for the event, which is used for sharing cross the network boundaries.\n   */\n  readonly uniqueId: string;\n  with(data: Data): WorkflowEventData<Data, DebugLabel>;\n  include(event: unknown): event is WorkflowEventData<Data, DebugLabel>;\n} & { readonly [opaqueSymbol]: DebugLabel };\n\nexport type WorkflowEventConfig<DebugLabel extends string = string> = {\n  debugLabel?: DebugLabel;\n  uniqueId?: string;\n};\n\nexport const workflowEvent = <Data = void, DebugLabel extends string = string>(\n  config?: WorkflowEventConfig<DebugLabel>,\n): WorkflowEvent<Data, DebugLabel> => {\n  const l1 = `${i++}`;\n  const event = {\n    debugLabel: config?.debugLabel ?? l1,\n    include: (\n      instance: WorkflowEventData<any>,\n    ): instance is WorkflowEventData<Data> => s.has(instance),\n    with: (data: Data) => {\n      const l2 = `${j++}`;\n      const ref = {\n        [Symbol.toStringTag]:\n          config?.debugLabel ?? `WorkflowEvent(${l1}.${l2})`,\n        toString: () =>\n          config?.debugLabel ? config.debugLabel : `WorkflowEvent(${l1}.${l2})`,\n        toJSON: () => {\n          return {\n            type: config?.debugLabel ? config.debugLabel : l1,\n            data,\n          };\n        },\n        get data() {\n          return data;\n        },\n      } as unknown as WorkflowEventData<Data, DebugLabel>;\n      s.add(ref);\n      refMap.set(ref, event);\n      return ref;\n    },\n  } as unknown as WorkflowEvent<Data, DebugLabel>;\n\n  const s = new WeakSet();\n  eventMap.set(event, s);\n\n  Object.defineProperty(event, Symbol.toStringTag, {\n    get: () => event?.debugLabel ?? `WorkflowEvent<${l1}>`,\n  });\n\n  Object.defineProperty(event, \"displayName\", {\n    value: event?.debugLabel ?? `WorkflowEvent<${l1}>`,\n  });\n\n  let uniqueId = config?.uniqueId;\n\n  Object.defineProperty(event, \"uniqueId\", {\n    get: () => {\n      if (!uniqueId) {\n        uniqueId = l1;\n      }\n      return uniqueId;\n    },\n    set: () => {\n      throw new Error(\"uniqueId is readonly\");\n    },\n  });\n\n  event.toString = () => config?.debugLabel ?? `WorkflowEvent<${l1}>`;\n  return event;\n};\n\n// utils\nexport const isWorkflowEvent = (\n  instance: unknown,\n): instance is WorkflowEvent<any> =>\n  typeof instance === \"object\" && instance !== null\n    ? eventMap.has(instance as any)\n    : false;\nexport const isWorkflowEventData = (\n  instance: unknown,\n): instance is WorkflowEventData<any> =>\n  typeof instance === \"object\" && instance !== null\n    ? refMap.has(instance as any)\n    : false;\nexport const eventSource = (\n  instance: unknown,\n): WorkflowEvent<any> | undefined =>\n  typeof instance === \"object\" && instance !== null\n    ? refMap.get(instance as any)\n    : undefined;\n", "import type { WorkflowEvent, WorkflowEventData } from \"./event\";\n\nexport const isEventData = (data: unknown): data is WorkflowEventData<any> =>\n  data != null && typeof data === \"object\" && \"data\" in data;\n\nexport const isPromiseLike = (value: unknown): value is PromiseLike<unknown> =>\n  value != null && typeof (value as PromiseLike<unknown>).then === \"function\";\n\nexport function flattenEvents(\n  acceptEventTypes: WorkflowEvent<any>[],\n  inputEventData: WorkflowEventData<any>[],\n): WorkflowEventData<any>[] {\n  const acceptance: WorkflowEventData<any>[] = new Array(\n    acceptEventTypes.length,\n  );\n  for (const eventData of inputEventData) {\n    for (let i = 0; i < acceptEventTypes.length; i++) {\n      if (acceptance[i]) {\n        continue;\n      }\n      if (acceptEventTypes[i]!.include(eventData)) {\n        acceptance[i] = eventData;\n        break;\n      }\n    }\n  }\n  return acceptance.filter(Boolean);\n}\n\nexport type Subscribable<Args extends any[], R> = {\n  subscribe: (callback: (...args: Args) => R) => () => void;\n  publish: (...args: Args) => unknown[];\n};\n\nconst __internal__subscribesSourcemap = new WeakMap<\n  Subscribable<any, any>,\n  Set<(...args: any[]) => any>\n>();\n\n/**\n * @internal\n */\nexport function getSubscribers<Args extends any[], R>(\n  subscribable: Subscribable<Args, R>,\n): Set<(...args: Args) => R> {\n  return __internal__subscribesSourcemap.get(subscribable)!;\n}\n\n/**\n * @internal\n */\nexport function createSubscribable<\n  FnOrArgs extends ((...args: any[]) => any) | any[],\n  R = unknown,\n>(): FnOrArgs extends (...args: any[]) => any\n  ? Subscribable<Parameters<FnOrArgs>, ReturnType<FnOrArgs>>\n  : FnOrArgs extends any[]\n    ? Subscribable<FnOrArgs, R>\n    : never {\n  const subscribers = new Set<(...args: any) => any>();\n  const obj = {\n    subscribe: (callback: (...args: any) => any) => {\n      subscribers.add(callback);\n      return () => {\n        subscribers.delete(callback);\n      };\n    },\n    publish: (...args: any) => {\n      const results: unknown[] = [];\n      for (const callback of subscribers) {\n        results.push(callback(...args));\n      }\n      return results;\n    },\n  };\n  __internal__subscribesSourcemap.set(obj, subscribers);\n  return obj as any;\n}\n", "export const createAsyncContext = <T>() => {\n  let currentStore: T | null = null;\n  return {\n    /**\n     * You must call `getContext()` in the top level of the workflow,\n     *  otherwise we will lose the async context of the workflow.\n     *\n     * @example\n     * ```\n     * workflow.handle([startEvent], async () => {\n     *   const { stream } = getContext(); // ✅ this is ok\n     *   await fetchData();\n     * });\n     *\n     * workflow.handle([startEvent], async () => {\n     *   await fetchData();\n     *   const { stream } = getContext(); // ❌ this is not ok\n     *   // we have no way\n     *   to know this code was originally part of the workflow\n     *   // w/o AsyncContext\n     * });\n     * ```\n     */\n    getStore: () => {\n      if (currentStore === null) {\n        console.warn(\n          \"Woops! Looks like you are calling `getContext` after `await fn()`. Please move `getContext` to top level of handler.\",\n        );\n      }\n      return currentStore;\n    },\n    run<R>(store: T, fn: () => R) {\n      currentStore = store;\n      try {\n        return fn();\n      } finally {\n        currentStore = null;\n      }\n    },\n  };\n};\n", "import {\n  eventSource,\n  type InferWorkflowEventData,\n  isWorkflowEvent,\n  type WorkflowEvent,\n  type WorkflowEventData,\n} from \"./event\";\nimport { createSubscribable, type Subscribable } from \"./utils\";\n\nclass JsonEncodeTransform extends TransformStream<\n  WorkflowEventData<any>,\n  string\n> {\n  constructor() {\n    super({\n      transform: (\n        event: WorkflowEventData<any>,\n        controller: TransformStreamDefaultController<string>,\n      ) => {\n        if (eventSource(event)) {\n          controller.enqueue(\n            JSON.stringify({\n              data: (event as WorkflowEventData<any>).data,\n              uniqueId: eventSource(event)!.uniqueId,\n            }) + \"\\n\",\n          );\n        }\n      },\n    });\n  }\n}\n\nclass JsonDecodeTransform extends TransformStream<\n  string,\n  WorkflowEventData<any>\n> {\n  #eventMap: Record<string, WorkflowEvent<any>>;\n\n  constructor(eventMap: Record<string, WorkflowEvent<any>>) {\n    super({\n      transform: (\n        data: string,\n        controller: TransformStreamDefaultController<WorkflowEventData<any>>,\n      ) => {\n        const lines = data\n          .split(\"\\n\")\n          .map((line) => line.trim())\n          .filter((line) => line.length > 0);\n        lines.forEach((line) => {\n          const eventData = JSON.parse(line) as {\n            data: ReturnType<WorkflowEvent<any>[\"with\"]>;\n            uniqueId: string;\n          };\n          const targetEvent = Object.values(this.#eventMap).find(\n            (e) => e.uniqueId === eventData.uniqueId,\n          );\n          if (targetEvent) {\n            const ev = targetEvent.with(\n              eventData.data,\n            ) as WorkflowEventData<any>;\n            controller.enqueue(ev);\n          } else {\n            console.warn(`Unknown event: ${eventData.uniqueId}`);\n          }\n        });\n      },\n    });\n    this.#eventMap = eventMap;\n  }\n}\n\nexport class WorkflowStream<R = any>\n  extends ReadableStream<R>\n  implements AsyncIterable<R>\n{\n  #stream: ReadableStream<R>;\n  #subscribable: Subscribable<[data: R], void>;\n\n  on<T>(\n    event: WorkflowEvent<T>,\n    handler: (event: WorkflowEventData<T>) => void,\n  ): () => void {\n    return this.#subscribable.subscribe((ev) => {\n      if (event.include(ev)) {\n        handler(ev);\n      }\n    });\n  }\n\n  constructor(\n    subscribable: Subscribable<[R], void>,\n    rootStream: ReadableStream<R>,\n  );\n  constructor(subscribable: Subscribable<[R], void>, rootStream: null);\n  constructor(subscribable: null, rootStream: ReadableStream<R> | null);\n  constructor(\n    subscribable: Subscribable<[R], void> | null,\n    rootStream: ReadableStream<R> | null,\n  ) {\n    if (!subscribable && !rootStream) {\n      throw new TypeError(\n        \"Either subscribable or root stream must be provided\",\n      );\n    }\n    super();\n    if (!subscribable) {\n      this.#subscribable = createSubscribable<[data: R], void>();\n      this.#stream = rootStream!.pipeThrough(\n        new TransformStream({\n          transform: (ev, controller) => {\n            this.#subscribable.publish(ev);\n            controller.enqueue(ev);\n          },\n        }),\n      );\n      return;\n    } else {\n      this.#subscribable = subscribable;\n      let unsubscribe: () => void;\n      this.#stream =\n        rootStream ??\n        new ReadableStream<R>({\n          start: (controller) => {\n            unsubscribe = subscribable.subscribe((event) => {\n              controller.enqueue(event);\n            });\n          },\n          cancel: () => {\n            unsubscribe();\n          },\n        });\n    }\n  }\n\n  static fromReadableStream<T = any>(\n    stream: ReadableStream<WorkflowEventData<any>>,\n  ): WorkflowStream<T> {\n    return new WorkflowStream(\n      null,\n      stream.pipeThrough(\n        new TransformStream<WorkflowEventData<any>>({\n          transform: (event, controller) => {\n            controller.enqueue(event);\n          },\n        }),\n      ),\n    );\n  }\n\n  static fromResponse(\n    response: Response,\n    eventMap: Record<string, WorkflowEvent<any>>,\n  ): WorkflowStream<WorkflowEventData<any>> {\n    const body = response.body;\n    if (!body) {\n      throw new Error(\"Response body is not readable\");\n    }\n    return new WorkflowStream(\n      null,\n      body\n        .pipeThrough(new TextDecoderStream())\n        .pipeThrough(new JsonDecodeTransform(eventMap)),\n    );\n  }\n\n  toResponse(\n    init?: ResponseInit,\n    transformer = new JsonEncodeTransform(),\n  ): R extends WorkflowEventData<any> ? Response : never {\n    return new Response(\n      (this.#stream as ReadableStream<WorkflowEventData<any>>)\n        .pipeThrough<string>(transformer)\n        .pipeThrough(new TextEncoderStream()),\n      init,\n    ) as any;\n  }\n\n  get locked() {\n    return this.#stream.locked;\n  }\n\n  [Symbol.asyncIterator](): ReadableStreamAsyncIterator<R> {\n    return this.#stream[Symbol.asyncIterator]();\n  }\n\n  cancel(reason?: any): Promise<void> {\n    return this.#stream.cancel(reason);\n  }\n\n  // make type compatible with Web ReadableStream API\n  getReader(options: { mode: \"byob\" }): ReadableStreamBYOBReader;\n  getReader(): ReadableStreamDefaultReader<R>;\n  getReader(options?: ReadableStreamGetReaderOptions): ReadableStreamReader<R>;\n  getReader(): any {\n    return this.#stream.getReader();\n  }\n\n  pipeThrough<T>(\n    transform: ReadableWritablePair<T, R>,\n    options?: StreamPipeOptions,\n  ): WorkflowStream<T> {\n    const stream = this.#stream.pipeThrough(transform, options) as any;\n    return new WorkflowStream<T>(null, stream);\n  }\n\n  pipeTo(\n    destination: WritableStream<R>,\n    options?: StreamPipeOptions,\n  ): Promise<void> {\n    return this.#stream.pipeTo(destination, options);\n  }\n\n  tee(): [WorkflowStream<R>, WorkflowStream<R>] {\n    const [l, r] = this.#stream.tee();\n    return [\n      new WorkflowStream(this.#subscribable, l),\n      new WorkflowStream(this.#subscribable, r),\n    ];\n  }\n\n  forEach(callback: (item: R) => void): Promise<void> {\n    return this.#stream.pipeTo(\n      new WritableStream({\n        write: (item: R) => {\n          callback(item);\n        },\n      }),\n    );\n  }\n\n  map<T>(callback: (item: R) => T): WorkflowStream<T> {\n    return this.pipeThrough<T>(\n      new TransformStream({\n        transform: (item, controller) => {\n          controller.enqueue(callback(item));\n        },\n      }),\n    );\n  }\n\n  values(\n    options?: ReadableStreamIteratorOptions,\n  ): ReadableStreamAsyncIterator<R> {\n    return this.#stream.values(options);\n  }\n\n  take(limit: number): WorkflowStream<R> {\n    let count = 0;\n    return this.pipeThrough(\n      new TransformStream({\n        transform: (ev, controller) => {\n          if (count < limit) {\n            controller.enqueue(ev);\n            count++;\n          }\n          if (count >= limit) {\n            controller.terminate();\n          }\n        },\n      }),\n    );\n  }\n\n  filter(\n    predicate: R extends WorkflowEventData<any>\n      ? WorkflowEvent<InferWorkflowEventData<R>>\n      : never,\n  ): WorkflowStream<R>;\n  filter(predicate: R): WorkflowStream<R>;\n  filter(predicate: (event: R) => boolean): WorkflowStream<R>;\n  filter(\n    predicate:\n      | WorkflowEvent<InferWorkflowEventData<R>>\n      | ((event: R) => boolean)\n      | R,\n  ): WorkflowStream<R> {\n    return this.pipeThrough(\n      new TransformStream({\n        transform: (ev, controller) => {\n          if (\n            typeof predicate === \"function\"\n              ? (predicate as Function)(ev)\n              : isWorkflowEvent(predicate)\n                ? predicate.include(ev)\n                : predicate === ev\n          ) {\n            controller.enqueue(ev);\n          }\n        },\n      }),\n    );\n  }\n\n  until(\n    predicate: R extends WorkflowEventData<any>\n      ? WorkflowEvent<InferWorkflowEventData<R>>\n      : never,\n  ): WorkflowStream<R>;\n  until(predicate: (item: R) => boolean): WorkflowStream<R>;\n  until(item: R): WorkflowStream<R>;\n  until(\n    predicate:\n      | WorkflowEvent<InferWorkflowEventData<R>>\n      | R\n      | ((item: R) => boolean),\n  ): WorkflowStream<R> {\n    return this.pipeThrough(\n      new TransformStream({\n        transform: (ev, controller) => {\n          controller.enqueue(ev);\n          if (\n            typeof predicate === \"function\"\n              ? (predicate as Function)(ev)\n              : isWorkflowEvent(predicate)\n                ? predicate.include(ev)\n                : predicate === ev\n          ) {\n            controller.terminate();\n          }\n        },\n      }),\n    );\n  }\n\n  async toArray(): Promise<R[]> {\n    const events: R[] = [];\n    await this.pipeTo(\n      new WritableStream({\n        write: (event) => {\n          events.push(event);\n        },\n      }),\n    );\n    return events;\n  }\n}\n", "import type {\n  WorkflowEvent,\n  WorkflowEventData,\n} from \"@llamaindex/workflow-core\";\nimport {\n  createSubscribable,\n  flattenEvents,\n  getSubscribers,\n  isEventData,\n  isPromiseLike,\n  type Subscribable,\n} from \"./utils\";\nimport { createAsyncContext } from \"@llamaindex/workflow-core/async-context\";\nimport { WorkflowStream } from \"./stream\";\n\nexport type Handler<\n  AcceptEvents extends WorkflowEvent<any>[],\n  Result extends WorkflowEventData<any> | void,\n> = (\n  ...event: {\n    [K in keyof AcceptEvents]: ReturnType<AcceptEvents[K][\"with\"]>;\n  }\n) => Result | Promise<Result>;\n\ntype BaseHandlerContext = {\n  abortController: AbortController;\n  handler: Handler<WorkflowEvent<any>[], any>;\n  // events that are accepted by the handler\n  inputEvents: WorkflowEvent<any>[];\n  // events data that are accepted by the handler\n  inputs: WorkflowEventData<any>[];\n  // events data that are emitted by the handler\n  outputs: WorkflowEventData<any>[];\n\n  //#region linked list data structure\n  prev: HandlerContext;\n  next: Set<HandlerContext>;\n  root: HandlerContext;\n  //#endregion\n};\n\ntype SyncHandlerContext = BaseHandlerContext & {\n  async: false;\n  pending: null;\n};\n\ntype AsyncHandlerContext = BaseHandlerContext & {\n  async: true;\n  pending: Promise<WorkflowEventData<any> | void> | null;\n};\n\nexport type HandlerContext = AsyncHandlerContext | SyncHandlerContext;\n\nexport type ContextNext = (\n  context: HandlerContext,\n  next: (context: HandlerContext) => void,\n) => void;\n\nexport type WorkflowContext = {\n  get stream(): WorkflowStream<WorkflowEventData<any>>;\n  get signal(): AbortSignal;\n  sendEvent: (...events: WorkflowEventData<any>[]) => void;\n\n  /**\n   * @internal\n   */\n  __internal__call_context: Subscribable<\n    Parameters<ContextNext>,\n    ReturnType<ContextNext>\n  >;\n  __internal__call_send_event: Subscribable<\n    [event: WorkflowEventData<any>, handlerContext: HandlerContext],\n    void\n  >;\n};\n\nexport const _executorAsyncLocalStorage = createAsyncContext<WorkflowContext>();\n\nexport function getContext(): WorkflowContext {\n  const context = _executorAsyncLocalStorage.getStore();\n  if (!context) {\n    throw new Error(\"No current context found\");\n  }\n  return context;\n}\n\nconst handlerContextAsyncLocalStorage = createAsyncContext<HandlerContext>();\n\nconst eventContextWeakMap = new WeakMap<\n  WorkflowEventData<any>,\n  HandlerContext\n>();\n\nexport type ExecutorParams = {\n  listeners: ReadonlyMap<\n    WorkflowEvent<any>[],\n    Set<Handler<WorkflowEvent<any>[], WorkflowEventData<any> | void>>\n  >;\n};\n\nexport const createContext = ({\n  listeners,\n}: ExecutorParams): WorkflowContext => {\n  const queue: WorkflowEventData<any>[] = [];\n  const runHandler = (\n    handler: Handler<WorkflowEvent<any>[], any>,\n    inputEvents: WorkflowEvent<any>[],\n    inputs: WorkflowEventData<any>[],\n    parentContext: HandlerContext,\n  ): void => {\n    let handlerAbortController: AbortController;\n    const handlerContext: HandlerContext = {\n      get abortController() {\n        if (!handlerAbortController) {\n          handlerAbortController = new AbortController();\n        }\n        return handlerAbortController;\n      },\n      async:\n        \"constructor\" in handler\n          ? handler.constructor.name === \"AsyncFunction\"\n          : false,\n      pending: null,\n      handler,\n      inputEvents,\n      inputs,\n      outputs: [],\n      prev: parentContext,\n      next: new Set(),\n      get root() {\n        return handlerRootContext;\n      },\n    };\n    handlerContext.prev.next.add(handlerContext);\n    const workflowContext = createWorkflowContext(handlerContext);\n    handlerContextAsyncLocalStorage.run(handlerContext, () => {\n      const cbs = [\n        ...new Set([\n          ...getSubscribers(rootWorkflowContext.__internal__call_context),\n          ...getSubscribers(workflowContext.__internal__call_context),\n        ]),\n      ];\n      _executorAsyncLocalStorage.run(workflowContext, () => {\n        //#region middleware\n        let i = 0;\n        const next = (context: HandlerContext) => {\n          if (i === cbs.length) {\n            let result: any;\n            try {\n              result = context.handler(...context.inputs);\n            } catch (error) {\n              if (handlerAbortController ?? rootAbortController) {\n                (handlerAbortController ?? rootAbortController).abort(error);\n              } else {\n                console.error(\"unhandled error in handler\", error);\n                throw error;\n              }\n            }\n            // return value is a special event\n            if (isPromiseLike(result)) {\n              (handlerContext as any).async = true;\n              (handlerContext as any).pending = result.then((event) => {\n                if (isEventData(event)) {\n                  workflowContext.sendEvent(event);\n                }\n                return event;\n              });\n            } else if (isEventData(result)) {\n              workflowContext.sendEvent(result);\n            }\n          }\n          const cb = cbs[i];\n          if (cb) {\n            i++;\n            cb(context, next);\n          }\n        };\n        next(handlerContext);\n        //#endregion\n      });\n    });\n  };\n  const queueUpdateCallback = (handlerContext: HandlerContext) => {\n    const queueSnapshot = [...queue];\n    [...listeners]\n      .filter(([events]) => {\n        const inputs = flattenEvents(events, queueSnapshot);\n        return inputs.length === events.length;\n      })\n      .map(([events, handlers]) => {\n        const inputs = flattenEvents(events, queueSnapshot);\n        inputs.forEach((input) => {\n          queue.splice(queue.indexOf(input), 1);\n        });\n        for (const handler of handlers) {\n          runHandler(handler, events, inputs, handlerContext);\n        }\n      });\n  };\n  const createWorkflowContext = (\n    handlerContext: HandlerContext,\n  ): WorkflowContext => {\n    let lazyLoadStream: WorkflowStream | null = null;\n    return {\n      get stream() {\n        if (!lazyLoadStream) {\n          const subscribable = createSubscribable<\n            [event: WorkflowEventData<any>],\n            void\n          >();\n          rootWorkflowContext.__internal__call_send_event.subscribe(\n            (newEvent: WorkflowEventData<any>) => {\n              let currentEventContext = eventContextWeakMap.get(newEvent);\n              while (currentEventContext) {\n                if (currentEventContext === handlerContext) {\n                  subscribable.publish(newEvent);\n                  break;\n                }\n                currentEventContext = currentEventContext.prev;\n              }\n            },\n          );\n          lazyLoadStream = new WorkflowStream(subscribable, null);\n        }\n        return lazyLoadStream;\n      },\n      get signal() {\n        return handlerContext.abortController.signal;\n      },\n      sendEvent: (...events) => {\n        events.forEach((event) => {\n          eventContextWeakMap.set(event, handlerContext);\n          handlerContext.outputs.push(event);\n          queue.push(event);\n          rootWorkflowContext.__internal__call_send_event.publish(\n            event,\n            handlerContext,\n          );\n          queueUpdateCallback(handlerContext);\n        });\n      },\n      __internal__call_context: createSubscribable(),\n      __internal__call_send_event: createSubscribable(),\n    };\n  };\n\n  let rootAbortController = new AbortController();\n  const handlerRootContext: HandlerContext = {\n    get abortController() {\n      if (!rootAbortController) {\n        rootAbortController = new AbortController();\n      }\n      return rootAbortController;\n    },\n    async: false,\n    pending: null,\n    inputEvents: [],\n    inputs: [],\n    outputs: [],\n    handler: null!,\n    prev: null!,\n    next: new Set(),\n    get root() {\n      return handlerRootContext;\n    },\n  };\n\n  const rootWorkflowContext = createWorkflowContext(handlerRootContext);\n  return rootWorkflowContext;\n};\n", "import { type WorkflowEvent, type WorkflowEventData } from \"./event\";\nimport { createContext, type Handler, type WorkflowContext } from \"./context\";\n\nexport type Workflow = {\n  handle<\n    const AcceptEvents extends WorkflowEvent<any>[],\n    Result extends ReturnType<WorkflowEvent<any>[\"with\"]> | void,\n  >(\n    accept: AcceptEvents,\n    handler: Handler<AcceptEvents, Result>,\n  ): void;\n  createContext(): WorkflowContext;\n};\n\nexport const createWorkflow = (): Workflow => {\n  const config = {\n    steps: new Map<\n      WorkflowEvent<any>[],\n      Set<Handler<WorkflowEvent<any>[], WorkflowEventData<any> | void>>\n    >(),\n  };\n\n  return {\n    handle: <\n      const AcceptEvents extends WorkflowEvent<any>[],\n      Result extends ReturnType<WorkflowEvent<any>[\"with\"]> | void,\n    >(\n      accept: AcceptEvents,\n      handler: Handler<AcceptEvents, Result>,\n    ): void => {\n      if (config.steps.has(accept)) {\n        const set = config.steps.get(accept) as Set<\n          Handler<AcceptEvents, Result>\n        >;\n        set.add(handler);\n      } else {\n        const set = new Set<Handler<AcceptEvents, Result>>();\n        set.add(handler);\n        config.steps.set(\n          accept,\n          set as Set<\n            Handler<WorkflowEvent<any>[], WorkflowEventData<any> | void>\n          >,\n        );\n      }\n    },\n    createContext() {\n      return createContext({\n        listeners: config.steps,\n      });\n    },\n  };\n};\n"], "mappings": ";AAEA,MAAM,2BAAW,IAAI;AACrB,MAAM,yBAAS,IAAI;AACnB,IAAI,IAAI;AACR,IAAI,IAAI;AA+BR,MAAa,gBAAgB,CAC3BA,WACoC;CACpC,MAAM,QAAQ;CACd,MAAM,QAAQ;EACZ,YAAY,QAAQ,cAAc;EAClC,SAAS,CACPC,aACwC,EAAE,IAAI,SAAS;EACzD,MAAM,CAACC,SAAe;GACpB,MAAM,QAAQ;GACd,MAAM,MAAM;KACT,OAAO,cACN,QAAQ,eAAe,gBAAgB,GAAG,GAAG,GAAG;IAClD,UAAU,MACR,QAAQ,aAAa,OAAO,cAAc,gBAAgB,GAAG,GAAG,GAAG;IACrE,QAAQ,MAAM;AACZ,YAAO;MACL,MAAM,QAAQ,aAAa,OAAO,aAAa;MAC/C;KACD;IACF;IACD,IAAI,OAAO;AACT,YAAO;IACR;GACF;AACD,KAAE,IAAI,IAAI;AACV,UAAO,IAAI,KAAK,MAAM;AACtB,UAAO;EACR;CACF;CAED,MAAM,oBAAI,IAAI;AACd,UAAS,IAAI,OAAO,EAAE;AAEtB,QAAO,eAAe,OAAO,OAAO,aAAa,EAC/C,KAAK,MAAM,OAAO,eAAe,gBAAgB,GAAG,GACrD,EAAC;AAEF,QAAO,eAAe,OAAO,eAAe,EAC1C,OAAO,OAAO,eAAe,gBAAgB,GAAG,GACjD,EAAC;CAEF,IAAI,WAAW,QAAQ;AAEvB,QAAO,eAAe,OAAO,YAAY;EACvC,KAAK,MAAM;AACT,QAAK,SACH,YAAW;AAEb,UAAO;EACR;EACD,KAAK,MAAM;AACT,SAAM,IAAI,MAAM;EACjB;CACF,EAAC;AAEF,OAAM,WAAW,MAAM,QAAQ,eAAe,gBAAgB,GAAG;AACjE,QAAO;AACR;AAGD,MAAa,kBAAkB,CAC7BC,oBAEO,aAAa,YAAY,aAAa,OACzC,SAAS,IAAI,SAAgB,GAC7B;AACN,MAAa,sBAAsB,CACjCA,oBAEO,aAAa,YAAY,aAAa,OACzC,OAAO,IAAI,SAAgB,GAC3B;AACN,MAAa,cAAc,CACzBA,oBAEO,aAAa,YAAY,aAAa,OACzC,OAAO,IAAI,SAAgB;;;;AChHjC,MAAa,cAAc,CAACC,SAC1B,QAAQ,eAAe,SAAS,YAAY,UAAU;AAExD,MAAa,gBAAgB,CAACC,UAC5B,SAAS,eAAgB,MAA+B,SAAS;AAEnE,SAAgB,cACdC,kBACAC,gBAC0B;CAC1B,MAAMC,aAAuC,IAAI,MAC/C,iBAAiB;AAEnB,MAAK,MAAM,aAAa,eACtB,MAAK,IAAIC,MAAI,GAAGA,MAAI,iBAAiB,QAAQA,OAAK;AAChD,MAAI,WAAWA,KACb;AAEF,MAAI,iBAAiBA,KAAI,QAAQ,UAAU,EAAE;AAC3C,cAAWA,OAAK;AAChB;EACD;CACF;AAEH,QAAO,WAAW,OAAO,QAAQ;AAClC;AAOD,MAAM,kDAAkC,IAAI;;;;AAQ5C,SAAgB,eACdC,cAC2B;AAC3B,QAAO,gCAAgC,IAAI,aAAa;AACzD;;;;AAKD,SAAgB,qBAOJ;CACV,MAAM,8BAAc,IAAI;CACxB,MAAM,MAAM;EACV,WAAW,CAACC,aAAoC;AAC9C,eAAY,IAAI,SAAS;AACzB,UAAO,MAAM;AACX,gBAAY,OAAO,SAAS;GAC7B;EACF;EACD,SAAS,CAAC,GAAG,SAAc;GACzB,MAAMC,UAAqB,CAAE;AAC7B,QAAK,MAAM,YAAY,YACrB,SAAQ,KAAK,SAAS,GAAG,KAAK,CAAC;AAEjC,UAAO;EACR;CACF;AACD,iCAAgC,IAAI,KAAK,YAAY;AACrD,QAAO;AACR;;;;AC7ED,MAAa,qBAAqB,MAAS;CACzC,IAAIC,eAAyB;AAC7B,QAAO;EAqBL,UAAU,MAAM;AACd,OAAI,iBAAiB,KACnB,SAAQ,KACN,uHACD;AAEH,UAAO;EACR;EACD,IAAOC,OAAUC,IAAa;AAC5B,kBAAe;AACf,OAAI;AACF,WAAO,IAAI;GACZ,UAAS;AACR,mBAAe;GAChB;EACF;CACF;AACF;;;;AC/BD,IAAM,sBAAN,cAAkC,gBAGhC;CACA,cAAc;AACZ,QAAM,EACJ,WAAW,CACTC,OACAC,eACG;AACH,OAAI,YAAY,MAAM,CACpB,YAAW,QACT,KAAK,UAAU;IACb,MAAO,MAAiC;IACxC,UAAU,YAAY,MAAM,CAAE;GAC/B,EAAC,GAAG,KACN;EAEJ,EACF,EAAC;CACH;AACF;AAED,IAAM,sBAAN,cAAkC,gBAGhC;CACA;CAEA,YAAYC,YAA8C;AACxD,QAAM,EACJ,WAAW,CACTC,MACAC,eACG;GACH,MAAM,QAAQ,KACX,MAAM,KAAK,CACX,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC,CAC1B,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;AACpC,SAAM,QAAQ,CAAC,SAAS;IACtB,MAAM,YAAY,KAAK,MAAM,KAAK;IAIlC,MAAM,cAAc,OAAO,OAAO,KAAKC,UAAU,CAAC,KAChD,CAAC,MAAM,EAAE,aAAa,UAAU,SACjC;AACD,QAAI,aAAa;KACf,MAAM,KAAK,YAAY,KACrB,UAAU,KACX;AACD,gBAAW,QAAQ,GAAG;IACvB,MACC,SAAQ,MAAM,iBAAiB,UAAU,WAAW;GAEvD,EAAC;EACH,EACF,EAAC;AACF,OAAKA,YAAYC;CAClB;AACF;AAED,IAAa,iBAAb,MAAa,uBACH,eAEV;CACE;CACA;CAEA,GACEC,OACAC,SACY;AACZ,SAAO,KAAKC,cAAc,UAAU,CAAC,OAAO;AAC1C,OAAI,MAAM,QAAQ,GAAG,CACnB,SAAQ,GAAG;EAEd,EAAC;CACH;CAQD,YACEC,cACAC,YACA;AACA,OAAK,iBAAiB,WACpB,OAAM,IAAI,UACR;AAGJ,SAAO;AACP,OAAK,cAAc;AACjB,QAAKF,gBAAgB,oBAAqC;AAC1D,QAAKG,UAAU,WAAY,YACzB,IAAI,gBAAgB,EAClB,WAAW,CAAC,IAAI,eAAe;AAC7B,SAAKH,cAAc,QAAQ,GAAG;AAC9B,eAAW,QAAQ,GAAG;GACvB,EACF,GACF;AACD;EACD,OAAM;AACL,QAAKA,gBAAgB;GACrB,IAAII;AACJ,QAAKD,UACH,cACA,IAAI,eAAkB;IACpB,OAAO,CAAC,eAAe;AACrB,mBAAc,aAAa,UAAU,CAAC,UAAU;AAC9C,iBAAW,QAAQ,MAAM;KAC1B,EAAC;IACH;IACD,QAAQ,MAAM;AACZ,kBAAa;IACd;GACF;EACJ;CACF;CAED,OAAO,mBACLE,QACmB;AACnB,SAAO,IAAI,eACT,MACA,OAAO,YACL,IAAI,gBAAwC,EAC1C,WAAW,CAAC,OAAO,eAAe;AAChC,cAAW,QAAQ,MAAM;EAC1B,EACF,GACF;CAEJ;CAED,OAAO,aACLC,UACAb,YACwC;EACxC,MAAM,OAAO,SAAS;AACtB,OAAK,KACH,OAAM,IAAI,MAAM;AAElB,SAAO,IAAI,eACT,MACA,KACG,YAAY,IAAI,oBAAoB,CACpC,YAAY,IAAI,oBAAoBI,YAAU;CAEpD;CAED,WACEU,MACA,cAAc,IAAI,uBACmC;AACrD,SAAO,IAAI,SACT,AAAC,KAAKJ,QACH,YAAoB,YAAY,CAChC,YAAY,IAAI,oBAAoB,EACvC;CAEH;CAED,IAAI,SAAS;AACX,SAAO,KAAKA,QAAQ;CACrB;CAED,CAAC,OAAO,iBAAiD;AACvD,SAAO,KAAKA,QAAQ,OAAO,gBAAgB;CAC5C;CAED,OAAOK,QAA6B;AAClC,SAAO,KAAKL,QAAQ,OAAO,OAAO;CACnC;CAMD,YAAiB;AACf,SAAO,KAAKA,QAAQ,WAAW;CAChC;CAED,YACEM,WACAC,SACmB;EACnB,MAAM,SAAS,KAAKP,QAAQ,YAAY,WAAW,QAAQ;AAC3D,SAAO,IAAI,eAAkB,MAAM;CACpC;CAED,OACEQ,aACAD,SACe;AACf,SAAO,KAAKP,QAAQ,OAAO,aAAa,QAAQ;CACjD;CAED,MAA8C;EAC5C,MAAM,CAAC,GAAG,EAAE,GAAG,KAAKA,QAAQ,KAAK;AACjC,SAAO,CACL,IAAI,eAAe,KAAKH,eAAe,IACvC,IAAI,eAAe,KAAKA,eAAe,EACxC;CACF;CAED,QAAQY,UAA4C;AAClD,SAAO,KAAKT,QAAQ,OAClB,IAAI,eAAe,EACjB,OAAO,CAACU,SAAY;AAClB,YAAS,KAAK;EACf,EACF,GACF;CACF;CAED,IAAOC,UAA6C;AAClD,SAAO,KAAK,YACV,IAAI,gBAAgB,EAClB,WAAW,CAAC,MAAM,eAAe;AAC/B,cAAW,QAAQ,SAAS,KAAK,CAAC;EACnC,EACF,GACF;CACF;CAED,OACEC,SACgC;AAChC,SAAO,KAAKZ,QAAQ,OAAO,QAAQ;CACpC;CAED,KAAKa,OAAkC;EACrC,IAAI,QAAQ;AACZ,SAAO,KAAK,YACV,IAAI,gBAAgB,EAClB,WAAW,CAAC,IAAI,eAAe;AAC7B,OAAI,QAAQ,OAAO;AACjB,eAAW,QAAQ,GAAG;AACtB;GACD;AACD,OAAI,SAAS,MACX,YAAW,WAAW;EAEzB,EACF,GACF;CACF;CASD,OACEC,WAImB;AACnB,SAAO,KAAK,YACV,IAAI,gBAAgB,EAClB,WAAW,CAAC,IAAI,eAAe;AAC7B,cACS,cAAc,aACjB,AAAC,UAAuB,GAAG,GAC3B,gBAAgB,UAAU,GACxB,UAAU,QAAQ,GAAG,GACrB,cAAc,GAEpB,YAAW,QAAQ,GAAG;EAEzB,EACF,GACF;CACF;CASD,MACEC,WAImB;AACnB,SAAO,KAAK,YACV,IAAI,gBAAgB,EAClB,WAAW,CAAC,IAAI,eAAe;AAC7B,cAAW,QAAQ,GAAG;AACtB,cACS,cAAc,aACjB,AAAC,UAAuB,GAAG,GAC3B,gBAAgB,UAAU,GACxB,UAAU,QAAQ,GAAG,GACrB,cAAc,GAEpB,YAAW,WAAW;EAEzB,EACF,GACF;CACF;CAED,MAAM,UAAwB;EAC5B,MAAMC,SAAc,CAAE;AACtB,QAAM,KAAK,OACT,IAAI,eAAe,EACjB,OAAO,CAAC,UAAU;AAChB,UAAO,KAAK,MAAM;EACnB,EACF,GACF;AACD,SAAO;CACR;AACF;;;;ACnQD,MAAa,6BAA6B,oBAAqC;AAE/E,SAAgB,aAA8B;CAC5C,MAAM,UAAU,2BAA2B,UAAU;AACrD,MAAK,QACH,OAAM,IAAI,MAAM;AAElB,QAAO;AACR;AAED,MAAM,kCAAkC,oBAAoC;AAE5E,MAAM,sCAAsB,IAAI;AAYhC,MAAa,gBAAgB,CAAC,EAC5B,WACe,KAAsB;CACrC,MAAMC,QAAkC,CAAE;CAC1C,MAAM,aAAa,CACjBC,SACAC,aACAC,QACAC,kBACS;EACT,IAAIC;EACJ,MAAMC,iBAAiC;GACrC,IAAI,kBAAkB;AACpB,SAAK,uBACH,0BAAyB,IAAI;AAE/B,WAAO;GACR;GACD,OACE,iBAAiB,UACb,QAAQ,YAAY,SAAS,kBAC7B;GACN,SAAS;GACT;GACA;GACA;GACA,SAAS,CAAE;GACX,MAAM;GACN,sBAAM,IAAI;GACV,IAAI,OAAO;AACT,WAAO;GACR;EACF;AACD,iBAAe,KAAK,KAAK,IAAI,eAAe;EAC5C,MAAM,kBAAkB,sBAAsB,eAAe;AAC7D,kCAAgC,IAAI,gBAAgB,MAAM;GACxD,MAAM,MAAM,CACV,GAAG,IAAI,IAAI,CACT,GAAG,eAAe,oBAAoB,yBAAyB,EAC/D,GAAG,eAAe,gBAAgB,yBAAyB,AAC5D,EACF;AACD,8BAA2B,IAAI,iBAAiB,MAAM;IAEpD,IAAIC,MAAI;IACR,MAAM,OAAO,CAACC,YAA4B;AACxC,SAAID,QAAM,IAAI,QAAQ;MACpB,IAAIE;AACJ,UAAI;AACF,gBAAS,QAAQ,QAAQ,GAAG,QAAQ,OAAO;MAC5C,SAAQ,OAAO;AACd,WAAI,0BAA0B,oBAC5B,EAAC,0BAA0B,qBAAqB,MAAM,MAAM;YACvD;AACL,gBAAQ,MAAM,8BAA8B,MAAM;AAClD,cAAM;OACP;MACF;AAED,UAAI,cAAc,OAAO,EAAE;AACzB,OAAC,eAAuB,QAAQ;AAChC,OAAC,eAAuB,UAAU,OAAO,KAAK,CAAC,UAAU;AACvD,YAAI,YAAY,MAAM,CACpB,iBAAgB,UAAU,MAAM;AAElC,eAAO;OACR,EAAC;MACH,WAAU,YAAY,OAAO,CAC5B,iBAAgB,UAAU,OAAO;KAEpC;KACD,MAAM,KAAK,IAAIF;AACf,SAAI,IAAI;AACN;AACA,SAAG,SAAS,KAAK;KAClB;IACF;AACD,SAAK,eAAe;GAErB,EAAC;EACH,EAAC;CACH;CACD,MAAM,sBAAsB,CAACD,mBAAmC;EAC9D,MAAM,gBAAgB,CAAC,GAAG,KAAM;AAChC,GAAC,GAAG,SAAU,EACX,OAAO,CAAC,CAAC,OAAO,KAAK;GACpB,MAAM,SAAS,cAAc,QAAQ,cAAc;AACnD,UAAO,OAAO,WAAW,OAAO;EACjC,EAAC,CACD,IAAI,CAAC,CAAC,QAAQ,SAAS,KAAK;GAC3B,MAAM,SAAS,cAAc,QAAQ,cAAc;AACnD,UAAO,QAAQ,CAAC,UAAU;AACxB,UAAM,OAAO,MAAM,QAAQ,MAAM,EAAE,EAAE;GACtC,EAAC;AACF,QAAK,MAAM,WAAW,SACpB,YAAW,SAAS,QAAQ,QAAQ,eAAe;EAEtD,EAAC;CACL;CACD,MAAM,wBAAwB,CAC5BA,mBACoB;EACpB,IAAII,iBAAwC;AAC5C,SAAO;GACL,IAAI,SAAS;AACX,SAAK,gBAAgB;KACnB,MAAM,eAAe,oBAGlB;AACH,yBAAoB,4BAA4B,UAC9C,CAACC,aAAqC;MACpC,IAAI,sBAAsB,oBAAoB,IAAI,SAAS;AAC3D,aAAO,qBAAqB;AAC1B,WAAI,wBAAwB,gBAAgB;AAC1C,qBAAa,QAAQ,SAAS;AAC9B;OACD;AACD,6BAAsB,oBAAoB;MAC3C;KACF,EACF;AACD,sBAAiB,IAAI,eAAe,cAAc;IACnD;AACD,WAAO;GACR;GACD,IAAI,SAAS;AACX,WAAO,eAAe,gBAAgB;GACvC;GACD,WAAW,CAAC,GAAG,WAAW;AACxB,WAAO,QAAQ,CAAC,UAAU;AACxB,yBAAoB,IAAI,OAAO,eAAe;AAC9C,oBAAe,QAAQ,KAAK,MAAM;AAClC,WAAM,KAAK,MAAM;AACjB,yBAAoB,4BAA4B,QAC9C,OACA,eACD;AACD,yBAAoB,eAAe;IACpC,EAAC;GACH;GACD,0BAA0B,oBAAoB;GAC9C,6BAA6B,oBAAoB;EAClD;CACF;CAED,IAAI,sBAAsB,IAAI;CAC9B,MAAMC,qBAAqC;EACzC,IAAI,kBAAkB;AACpB,QAAK,oBACH,uBAAsB,IAAI;AAE5B,UAAO;EACR;EACD,OAAO;EACP,SAAS;EACT,aAAa,CAAE;EACf,QAAQ,CAAE;EACV,SAAS,CAAE;EACX,SAAS;EACT,MAAM;EACN,sBAAM,IAAI;EACV,IAAI,OAAO;AACT,UAAO;EACR;CACF;CAED,MAAM,sBAAsB,sBAAsB,mBAAmB;AACrE,QAAO;AACR;;;;AC/PD,MAAa,iBAAiB,MAAgB;CAC5C,MAAM,SAAS,EACb,uBAAO,IAAI,MAIZ;AAED,QAAO;EACL,QAAQ,CAINC,QACAC,YACS;AACT,OAAI,OAAO,MAAM,IAAI,OAAO,EAAE;IAC5B,MAAM,MAAM,OAAO,MAAM,IAAI,OAAO;AAGpC,QAAI,IAAI,QAAQ;GACjB,OAAM;IACL,MAAM,sBAAM,IAAI;AAChB,QAAI,IAAI,QAAQ;AAChB,WAAO,MAAM,IACX,QACA,IAGD;GACF;EACF;EACD,gBAAgB;AACd,UAAO,cAAc,EACnB,WAAW,OAAO,MACnB,EAAC;EACH;CACF;AACF"}