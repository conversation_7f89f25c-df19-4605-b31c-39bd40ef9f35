//#region src/async-context/index.browser.ts
const createAsyncContext = () => {
	let currentStore = null;
	return {
		getStore: () => {
			if (currentStore === null) console.warn("Woops! Looks like you are calling `getContext` after `await fn()`. Please move `getContext` to top level of handler.");
			return currentStore;
		},
		run(store, fn) {
			currentStore = store;
			try {
				return fn();
			} finally {
				currentStore = null;
			}
		}
	};
};

//#endregion
export { createAsyncContext };
//# sourceMappingURL=index.browser.js.map