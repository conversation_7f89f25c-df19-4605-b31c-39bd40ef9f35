import { z } from "zod";
import { workflowEvent } from "@llama-flow/core";

//#region src/util/zod.ts
const zodEvent = (schema, config) => {
	const event = workflowEvent(config);
	const originalWith = event.with;
	return Object.assign(event, {
		with: (data) => {
			schema.parse(data);
			return originalWith(data);
		},
		get schema() {
			return schema;
		}
	});
};

//#endregion
export { zodEvent };
//# sourceMappingURL=zod.js.map