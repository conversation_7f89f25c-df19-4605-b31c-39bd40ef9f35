const require_chunk = require('./chunk-CUT6urMc.cjs');
const __llama_flow_core = require_chunk.__toESM(require("@llama-flow/core"));

//#region src/middleware/validation.ts
function withValidation(workflow, validation) {
	const createSafeSendEvent = (...events) => {
		const outputs = validation.filter(([inputs]) => inputs.every((input, idx) => input.include(events[idx]))).map(([_, outputs$1]) => outputs$1);
		const store = (0, __llama_flow_core.getContext)();
		const originalSendEvent = store.sendEvent;
		return (...inputs) => {
			for (let i = 0; i < outputs.length; i++) {
				const output = outputs[i];
				if (output.length === inputs.length) {
					if (output.every((e, idx) => e.include(inputs[idx]))) return originalSendEvent(...inputs);
				}
			}
			console.warn("Invalid input detected [%s]", inputs.map((i) => i.data).join(", "));
			return originalSendEvent(...inputs);
		};
	};
	return {
		...workflow,
		strictHandle: (accept, handler) => {
			const wrappedHandler = (...events) => {
				const context = (0, __llama_flow_core.getContext)();
				return handler(context.safeSendEvent, ...events);
			};
			return workflow.handle(accept, wrappedHandler);
		},
		createContext() {
			const context = workflow.createContext();
			context.__internal__call_context.subscribe((context$1, next) => {
				(0, __llama_flow_core.getContext)().safeSendEvent = createSafeSendEvent(...context$1.inputs);
				next(context$1);
			});
			return context;
		}
	};
}

//#endregion
exports.withValidation = withValidation;
//# sourceMappingURL=validation.cjs.map