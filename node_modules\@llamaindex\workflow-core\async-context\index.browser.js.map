{"version": 3, "file": "index.browser.js", "names": ["currentStore: T | null", "store: T", "fn: () => R"], "sources": ["../src/async-context/index.browser.ts"], "sourcesContent": ["export const createAsyncContext = <T>() => {\n  let currentStore: T | null = null;\n  return {\n    /**\n     * You must call `getContext()` in the top level of the workflow,\n     *  otherwise we will lose the async context of the workflow.\n     *\n     * @example\n     * ```\n     * workflow.handle([startEvent], async () => {\n     *   const { stream } = getContext(); // ✅ this is ok\n     *   await fetchData();\n     * });\n     *\n     * workflow.handle([startEvent], async () => {\n     *   await fetchData();\n     *   const { stream } = getContext(); // ❌ this is not ok\n     *   // we have no way\n     *   to know this code was originally part of the workflow\n     *   // w/o AsyncContext\n     * });\n     * ```\n     */\n    getStore: () => {\n      if (currentStore === null) {\n        console.warn(\n          \"Woops! Looks like you are calling `getContext` after `await fn()`. Please move `getContext` to top level of handler.\",\n        );\n      }\n      return currentStore;\n    },\n    run<R>(store: T, fn: () => R) {\n      currentStore = store;\n      try {\n        return fn();\n      } finally {\n        currentStore = null;\n      }\n    },\n  };\n};\n"], "mappings": ";AAAA,MAAa,qBAAqB,MAAS;CACzC,IAAIA,eAAyB;AAC7B,QAAO;EAqBL,UAAU,MAAM;AACd,OAAI,iBAAiB,KACnB,SAAQ,KACN,uHACD;AAEH,UAAO;EACR;EACD,IAAOC,OAAUC,IAAa;AAC5B,kBAAe;AACf,OAAI;AACF,WAAO,IAAI;GACZ,UAAS;AACR,mBAAe;GAChB;EACF;CACF;AACF"}