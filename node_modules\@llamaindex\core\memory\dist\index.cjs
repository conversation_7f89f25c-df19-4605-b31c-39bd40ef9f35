Object.defineProperty(exports, '__esModule', { value: true });

var index_cjs = require('../../global/dist/index.cjs');
var index_cjs$2 = require('../../storage/chat-store/dist/index.cjs');
var index_cjs$1 = require('../../utils/dist/index.cjs');
var tokenizers = require('@llamaindex/env/tokenizers');
var index_cjs$3 = require('../../prompts/dist/index.cjs');
var env = require('@llamaindex/env');

const DEFAULT_TOKEN_LIMIT_RATIO = 0.75;
const DEFAULT_CHAT_STORE_KEY = "chat_history";
/**
 * A ChatMemory is used to keep the state of back and forth chat messages
 * @deprecated Use Memory instead.
 */ class BaseMemory {
    _tokenCountForMessages(messages) {
        if (messages.length === 0) {
            return 0;
        }
        const tokenizer = index_cjs.Settings.tokenizer;
        const str = messages.map((m)=>index_cjs$1.extractText(m.content)).join(" ");
        return tokenizer.encode(str).length;
    }
}
/**
 * @deprecated Use Memory with snapshot feature with your own storage instead.
 */ class BaseChatStoreMemory extends BaseMemory {
    constructor(chatStore = new index_cjs$2.SimpleChatStore(), chatStoreKey = DEFAULT_CHAT_STORE_KEY){
        super(), this.chatStore = chatStore, this.chatStoreKey = chatStoreKey;
    }
    getAllMessages() {
        return this.chatStore.getMessages(this.chatStoreKey);
    }
    put(messages) {
        this.chatStore.addMessage(this.chatStoreKey, messages);
    }
    set(messages) {
        this.chatStore.setMessages(this.chatStoreKey, messages);
    }
    reset() {
        this.chatStore.deleteMessages(this.chatStoreKey);
    }
}

/**
 * @deprecated Use Memory instead.
 */ class ChatMemoryBuffer extends BaseChatStoreMemory {
    constructor(options){
        super(options?.chatStore, options?.chatStoreKey);
        const llm = options?.llm ?? index_cjs.Settings.llm;
        const contextWindow = llm.metadata.contextWindow;
        this.tokenLimit = options?.tokenLimit ?? Math.ceil(contextWindow * DEFAULT_TOKEN_LIMIT_RATIO);
        if (options?.chatHistory) {
            this.chatStore.setMessages(this.chatStoreKey, options.chatHistory);
        }
    }
    async getMessages(transientMessages, initialTokenCount = 0) {
        const messages = await this.getAllMessages();
        if (initialTokenCount > this.tokenLimit) {
            throw new Error("Initial token count exceeds token limit");
        }
        // Add input messages as transient messages
        const messagesWithInput = transientMessages ? [
            ...transientMessages,
            ...messages
        ] : messages;
        let messageCount = messagesWithInput.length;
        let currentMessages = messagesWithInput.slice(-messageCount);
        let tokenCount = this._tokenCountForMessages(messagesWithInput) + initialTokenCount;
        while(tokenCount > this.tokenLimit && messageCount > 1){
            messageCount -= 1;
            if (messagesWithInput.at(-messageCount).role === "assistant") {
                messageCount -= 1;
            }
            currentMessages = messagesWithInput.slice(-messageCount);
            tokenCount = this._tokenCountForMessages(currentMessages) + initialTokenCount;
        }
        if (tokenCount > this.tokenLimit && messageCount <= 0) {
            return [];
        }
        return messagesWithInput.slice(-messageCount);
    }
}

/**
 * @deprecated Use Memory instead.
 */ class ChatSummaryMemoryBuffer extends BaseMemory {
    constructor(options){
        super();
        this.messages = options?.messages ?? [];
        this.summaryPrompt = options?.summaryPrompt ?? index_cjs$3.defaultSummaryPrompt;
        this.llm = options?.llm ?? index_cjs.Settings.llm;
        if (!this.llm.metadata.maxTokens) {
            throw new Error("LLM maxTokens is not set. Needed so the summarizer ensures the context window size of the LLM.");
        }
        this.tokenizer = options?.tokenizer ?? tokenizers.tokenizers.tokenizer();
        this.tokensToSummarize = this.llm.metadata.contextWindow - this.llm.metadata.maxTokens;
        if (this.tokensToSummarize < this.llm.metadata.contextWindow * 0.25) {
            throw new Error("The number of tokens that trigger the summarize process are less than 25% of the context window. Try lowering maxTokens or use a model with a larger context window.");
        }
    }
    async summarize() {
        // get the conversation messages to create summary
        const messagesToSummarize = this.calcConversationMessages();
        let promptMessages;
        do {
            promptMessages = [
                {
                    content: this.summaryPrompt.format({
                        context: index_cjs$1.messagesToHistory(messagesToSummarize)
                    }),
                    role: "user",
                    options: {}
                }
            ];
            // remove oldest message until the chat history is short enough for the context window
            messagesToSummarize.shift();
        }while (this.tokenizer.encode(promptMessages[0].content).length > this.tokensToSummarize)
        const response = await this.llm.chat({
            messages: promptMessages
        });
        return {
            content: response.message.content,
            role: "memory"
        };
    }
    // Find last summary message
    get lastSummaryIndex() {
        const reversedMessages = this.messages.slice().reverse();
        const index = reversedMessages.findIndex((message)=>message.role === "memory");
        if (index === -1) {
            return null;
        }
        return this.messages.length - 1 - index;
    }
    getLastSummary() {
        const lastSummaryIndex = this.lastSummaryIndex;
        return lastSummaryIndex ? this.messages[lastSummaryIndex] : null;
    }
    get systemMessages() {
        // get array of all system messages
        return this.messages.filter((message)=>message.role === "system");
    }
    get nonSystemMessages() {
        // get array of all non-system messages
        return this.messages.filter((message)=>message.role !== "system");
    }
    /**
   * Calculates the messages that describe the conversation so far.
   * If there's no memory, all non-system messages are used.
   * If there's a memory, uses all messages after the last summary message.
   */ calcConversationMessages(transformSummary) {
        const lastSummaryIndex = this.lastSummaryIndex;
        if (!lastSummaryIndex) {
            // there's no memory, so just use all non-system messages
            return this.nonSystemMessages;
        } else {
            // there's a memory, so use all messages after the last summary message
            // and convert summary message so it can be send to the LLM
            const summaryMessage = transformSummary ? {
                content: `Summary of the conversation so far: ${this.messages[lastSummaryIndex].content}`,
                role: "system"
            } : this.messages[lastSummaryIndex];
            return [
                summaryMessage,
                ...this.messages.slice(lastSummaryIndex + 1)
            ];
        }
    }
    calcCurrentRequestMessages(transientMessages) {
        // currently, we're sending:
        // system messages first, then transient messages and then the messages that describe the conversation so far
        return [
            ...this.systemMessages,
            ...transientMessages ? transientMessages : [],
            ...this.calcConversationMessages(true)
        ];
    }
    reset() {
        this.messages = [];
    }
    async getMessages(transientMessages) {
        const requestMessages = this.calcCurrentRequestMessages(transientMessages);
        // get tokens of current request messages and the transient messages
        const tokens = requestMessages.reduce((count, message)=>count + this.tokenizer.encode(index_cjs$1.extractText(message.content)).length, 0);
        if (tokens > this.tokensToSummarize) {
            // if there are too many tokens for the next request, call summarize
            const memoryMessage = await this.summarize();
            const lastMessage = this.messages.at(-1);
            if (lastMessage && lastMessage.role === "user") {
                // if last message is a user message, ensure that it's sent after the new memory message
                this.messages.pop();
                this.messages.push(memoryMessage);
                this.messages.push(lastMessage);
            } else {
                // otherwise just add the memory message
                this.messages.push(memoryMessage);
            }
            // TODO: we still might have too many tokens
            // e.g. too large system messages or transient messages
            // how should we deal with that?
            return this.calcCurrentRequestMessages(transientMessages);
        }
        return requestMessages;
    }
    async getAllMessages() {
        return this.getMessages();
    }
    put(message) {
        this.messages.push(message);
    }
}

class ChatMessageAdapter {
    fromMemory(message) {
        return {
            content: message.content,
            role: message.role,
            options: message.options
        };
    }
    toMemory(message) {
        return {
            id: env.randomUUID(),
            createdAt: new Date(),
            ...message
        };
    }
    isCompatible(message) {
        return !!(message && typeof message === "object" && "role" in message && message.role && "content" in message);
    }
}

/**
 * Utility class for converting between LlamaIndex ChatMessage and Vercel UI Message formats
 */ class VercelMessageAdapter {
    /**
   * Convert LlamaIndex ChatMessage to Vercel UI Message format
   */ fromMemory(memoryMessage) {
        const parts = this.convertMessageContentToVercelParts(memoryMessage.content);
        // Convert role to UI message role
        let role;
        switch(memoryMessage.role){
            case "system":
            case "user":
            case "assistant":
                role = memoryMessage.role;
                break;
            case "memory":
                role = "system";
                break;
            case "developer":
                role = "user";
                break;
            default:
                role = "user"; // Default fallback, should not happen
        }
        return {
            id: memoryMessage.id,
            role,
            content: index_cjs$1.extractText(memoryMessage.content),
            parts,
            createdAt: memoryMessage.createdAt,
            annotations: memoryMessage.annotations
        };
    }
    /**
   * Convert Vercel UI Message to LlamaIndex ChatMessage format
   */ toMemory(uiMessage) {
        // Convert UI message role to MessageType
        let role;
        switch(uiMessage.role){
            case "system":
            case "user":
            case "assistant":
                role = uiMessage.role;
                break;
            case "data":
                role = "user"; // Map data role to user
                break;
            default:
                role = "user"; // Default fallback, should not happen
        }
        // Convert parts to MessageContent
        const content = this.convertVercelPartsToMessageContent(uiMessage.parts);
        return {
            id: uiMessage.id,
            content: content ?? uiMessage.content,
            role,
            createdAt: uiMessage.createdAt,
            annotations: uiMessage.annotations
        };
    }
    /**
   * Validate if object matches VercelMessage structure
   */ isCompatible(message) {
        return !!(message && typeof message === "object" && "role" in message && "content" in message && "parts" in message);
    }
    /**
   * Convert UI parts to MessageContent
   */ convertVercelPartsToMessageContent(parts) {
        if (parts.length === 0) {
            return null;
        }
        const details = [];
        for (const part of parts){
            switch(part.type){
                case "file":
                    {
                        details.push({
                            type: "file",
                            data: part.data,
                            mimeType: part.mimeType
                        });
                        break;
                    }
                default:
                    // For other part types, convert to text
                    details.push({
                        type: "text",
                        text: part.text
                    });
                    break;
            }
        }
        // If only one text detail, return as string
        if (details.length === 1 && details[0]?.type === "text") {
            return details[0].text;
        }
        return details;
    }
    /**
   * Convert MessageContent to UI parts
   */ convertMessageContentToVercelParts(content) {
        if (typeof content === "string") {
            return [
                {
                    type: "text",
                    text: content
                }
            ];
        }
        const parts = [];
        for (const detail of content){
            switch(detail.type){
                case "text":
                    parts.push({
                        type: "text",
                        text: detail.text
                    });
                    break;
                case "image_url":
                    parts.push({
                        type: "text",
                        text: `[Image URL: ${detail.image_url.url}]`
                    });
                    break;
                case "audio":
                case "video":
                case "image":
                case "file":
                    parts.push({
                        type: "file",
                        data: detail.data,
                        mimeType: detail.type
                    });
                    break;
                default:
                    // For unknown types, create a text representation
                    parts.push({
                        type: "text",
                        text: JSON.stringify(detail)
                    });
            }
        }
        return parts;
    }
}

/**
 * A base class for memory blocks.
 */ class BaseMemoryBlock {
    constructor(options){
        this.id = options.id ?? `memory-block-${env.randomUUID()}`;
        this.priority = options.priority;
        this.isLongTerm = options.isLongTerm ?? true;
    }
}

const DEFAULT_EXTRACTION_PROMPT = `
You are a precise fact extraction system designed to identify key information from conversations.

CONVERSATION SEGMENT:
{{conversation}}

EXISTING FACTS:
{{existing_facts}}

INSTRUCTIONS: 
1. Review the conversation segment provided above.
2. Extract specific, concrete facts the user has disclosed or important information discovered
3. Focus on factual information like preferences, personal details, requirements, constraints, or context
4. Do not include opinions, summaries, or interpretations - only extract explicit information
5. Do not duplicate facts that are already in the existing facts list

Respond with the new facts from the conversation segment using the following JSON format:
{
  "facts": ["fact1", "fact2", "fact3", ...]
}
`;
const DEFAULT_SUMMARY_PROMPT = `
You are a precise fact condensing system designed to summarize facts in a concise manner.

EXISTING FACTS:
{{existing_facts}}

INSTRUCTIONS:
1. Review the current list of existing facts
2. Condense the facts into a more concise list, less than {{ max_facts }} facts
3. Focus on factual information like preferences, personal details, requirements, constraints, or context
4. Do not include opinions, summaries, or interpretations - only extract explicit information
5. Do not duplicate facts that are already in the existing facts list

Respond with the condensed facts using the following JSON format:
{
  "facts": ["fact1", "fact2", "fact3", ...]
}
`;
/**
 * A memory block that stores facts extracted from conversations.
 */ class FactExtractionMemoryBlock extends BaseMemoryBlock {
    constructor(options){
        super(options), this.facts = [];
        this.llm = options.llm;
        this.maxFacts = options.maxFacts;
        this.extractionPrompt = options.extractionPrompt ?? DEFAULT_EXTRACTION_PROMPT;
        this.summaryPrompt = options.summaryPrompt ?? DEFAULT_SUMMARY_PROMPT;
    }
    async get() {
        const fact = {
            id: this.id,
            content: this.facts.join("\n"),
            role: "memory"
        };
        return [
            fact
        ];
    }
    async put(messages) {
        if (messages.length === 0) {
            return;
        }
        // Format existing facts
        const existingFactsStr = `{ facts: [${this.facts.join(", ")}] }`;
        // Format conversation
        const conversation = `\n\t${messages.map((m)=>m.content).join("\n\t")}`;
        // Format prompt
        const prompt = this.extractionPrompt.replace("{{conversation}}", conversation).replace("{{existing_facts}}", existingFactsStr);
        // Call the LLM
        const response = await this.llm.complete({
            prompt
        });
        // Parse and validate the response
        const newFacts = JSON.parse(response.text);
        if (newFacts.facts === undefined || !Array.isArray(newFacts.facts)) {
            throw new Error(`[FactExtraction] Invalid response from LLM: ${response.text}`);
        }
        // No new facts, so no need to update the facts
        if (newFacts.facts.length === 0) {
            return;
        }
        // Update the facts
        this.facts.push(...newFacts.facts);
        // Condense the facts
        if (this.facts.length > this.maxFacts) {
            const existingFactsStr = `{ facts: [${this.facts.join(", ")}] }`;
            const prompt = this.summaryPrompt.replace("{{existing_facts}}", existingFactsStr).replace("{{max_facts}}", this.maxFacts.toString());
            const response = await this.llm.complete({
                prompt
            });
            const condensedFacts = JSON.parse(response.text);
            if (condensedFacts.facts === undefined || !Array.isArray(condensedFacts.facts) || condensedFacts.facts.length === 0) {
                throw new Error("Invalid response from LLM");
            }
            // Only get the first maxFacts facts (in case the LLM returned more)
            this.facts = condensedFacts.facts.slice(0, this.maxFacts);
        }
    }
}

/**
 * A memory block that stores static content that doesn't change.
 * Static content is always included in the memory context.
 */ class StaticMemoryBlock extends BaseMemoryBlock {
    constructor(options){
        super({
            ...options,
            priority: 0,
            isLongTerm: false
        });
        this.content = options.content;
        this.messageRole = options.messageRole ?? "user";
    }
    /**
   * Returns the static content.
   * The messages parameter is ignored since this block contains static content.
   */ async get() {
        return [
            {
                id: this.id,
                role: this.messageRole,
                content: this.content
            }
        ];
    }
    async put(_messages) {
    // No-op: static content doesn't change
    }
}

const DEFAULT_TOKEN_LIMIT = 30000;
const DEFAULT_SHORT_TERM_TOKEN_LIMIT_RATIO = 0.7;
class Memory {
    constructor(messages = [], options = {}){
        /**
   * Hold all messages put into the memory.
   */ this.messages = [];
        /**
   * The token limit for memory retrieval results.
   */ this.tokenLimit = DEFAULT_TOKEN_LIMIT;
        /**
   * The ratio of the token limit for short term memory.
   */ this.shortTermTokenLimitRatio = DEFAULT_SHORT_TERM_TOKEN_LIMIT_RATIO;
        /**
   * The memory blocks for the memory.
   */ this.memoryBlocks = [];
        /**
   * The cursor for the messages that have been processed into long-term memory.
   */ this.memoryCursor = 0;
        this.messages = messages;
        this.tokenLimit = options.tokenLimit ?? DEFAULT_TOKEN_LIMIT;
        this.shortTermTokenLimitRatio = options.shortTermTokenLimitRatio ?? DEFAULT_SHORT_TERM_TOKEN_LIMIT_RATIO;
        this.memoryBlocks = options.memoryBlocks ?? [];
        this.memoryCursor = options.memoryCursor ?? 0;
        this.adapters = {
            ...options.customAdapters,
            vercel: new VercelMessageAdapter(),
            llamaindex: new ChatMessageAdapter()
        };
    }
    /**
   * Add a message to the memory
   * @param message - The message to add to the memory
   */ async add(message) {
        let memoryMessage = null;
        // Try to find a compatible adapter among the other adapters
        for(const key in this.adapters){
            const adapter = this.adapters[key];
            if (adapter?.isCompatible(message)) {
                memoryMessage = adapter.toMemory(message);
                break;
            }
        }
        if (memoryMessage) {
            this.messages.push(memoryMessage);
            // Automatically manage memory blocks when new messages are added
            await this.manageMemoryBlocks();
        } else {
            throw new Error(`None of the adapters ${Object.keys(this.adapters).join(", ")} are compatible with the message. ${JSON.stringify(message)}`);
        }
    }
    /**
   * Get the messages of specific type from the memory
   * @param options - The options for the get method
   * @returns The messages of specific type
   */ async get(options = {}) {
        const { type = "llamaindex", transientMessages } = options;
        const adapter = this.adapters[type];
        if (!adapter) {
            throw new Error(`No adapter registered for type "${String(type)}"`);
        }
        let messages = this.messages;
        if (transientMessages && transientMessages.length > 0) {
            messages = [
                ...this.messages,
                ...transientMessages.map((m)=>this.adapters.llamaindex.toMemory(m))
            ];
        }
        // Convert memory messages to chat messages for memory block processing
        const chatMessages = messages.map((m)=>adapter.fromMemory(m));
        return chatMessages;
    }
    /**
   * Get the messages from the memory, optionally including transient messages.
   * only return messages that are within context window of the LLM
   * @param llm - To fit the result messages to the context window of the LLM. If not provided, the default token limit will be used.
   * @param transientMessages - Optional transient messages to include.
   * @returns The messages from the memory, optionally including transient messages.
   */ async getLLM(llm, transientMessages) {
        // Priority of result messages:
        // [Fixed blocks (priority=0), Long term blocks, Short term messages(oldest to newest), Transient messages]
        const contextWindow = llm?.metadata.contextWindow;
        const tokenLimit = contextWindow ? Math.ceil(contextWindow * DEFAULT_TOKEN_LIMIT_RATIO) : this.tokenLimit;
        // Start with fixed block messages (priority=0)
        // as it must always be included in the retrieval result
        const messages = await this.getMemoryBlockMessages(this.memoryBlocks.filter((block)=>block.priority === 0), tokenLimit);
        // remaining token limit for short-term and memory blocks content
        const remainingTokenLimit = tokenLimit - this.countMessagesToken([
            ...messages,
            ...transientMessages || []
        ]);
        // if transient messages are provided, we need to check if they fit within the token limit
        if (remainingTokenLimit < 0) {
            throw new Error(`Could not fit fixed blocks and transient messages within memory context`);
        }
        // Get messages for short-term and memory blocks
        const shortTermTokenLimit = Math.ceil(remainingTokenLimit * this.shortTermTokenLimitRatio);
        const memoryBlocksTokenLimit = remainingTokenLimit - shortTermTokenLimit;
        // Add long-term memory blocks (priority > 0)
        const longTermBlocks = [
            ...this.memoryBlocks
        ].filter((block)=>block.priority !== 0).sort((a, b)=>b.priority - a.priority);
        const longTermBlockMessages = await this.getMemoryBlockMessages(longTermBlocks, memoryBlocksTokenLimit);
        messages.push(...longTermBlockMessages);
        // Process short-term messages (newest first for token efficiency, but maintain chronological order in result)
        const shortTermMessagesResult = [];
        const unprocessedMessages = this.messages.slice(this.memoryCursor);
        // Process from newest to oldest for token efficiency
        for(let i = unprocessedMessages.length - 1; i >= 0; i--){
            const memoryMessage = unprocessedMessages[i];
            if (!memoryMessage) continue;
            const chatMessage = this.adapters.llamaindex.fromMemory(memoryMessage);
            // Check if adding this message would exceed token limit
            const newTokenCount = this.countMessagesToken(shortTermMessagesResult) + this.countMessagesToken([
                chatMessage
            ]) + this.countMessagesToken(transientMessages || []);
            if (newTokenCount > shortTermTokenLimit) {
                break;
            }
            shortTermMessagesResult.push(chatMessage);
        }
        // reverse the short-term messages to maintain chronological order (oldest to newest)
        messages.push(...shortTermMessagesResult.reverse());
        // Add transient messages at the end
        if (transientMessages && transientMessages.length > 0) {
            messages.push(...transientMessages);
        }
        return messages;
    }
    /**
   * Get the content from the memory blocks
   * also convert the content to chat messages
   * @param blocks - The blocks to get the content from
   * @param tokenLimit - The token limit for the memory blocks, if not provided, all the memory blocks will be included
   */ async getMemoryBlockMessages(blocks, tokenLimit) {
        if (blocks.length === 0) {
            return [];
        }
        // Sort memory blocks by priority (highest first)
        const sortedBlocks = [
            ...blocks
        ].sort((a, b)=>b.priority - a.priority);
        const memoryContent = [];
        // Get up to the token limit of the memory blocks
        let addedTokenCount = 0;
        for (const block of sortedBlocks){
            try {
                const content = await block.get();
                for (const message of content){
                    const chatMessage = this.adapters.llamaindex.fromMemory(message);
                    const messageTokenCount = this.countMessagesToken([
                        chatMessage
                    ]);
                    if (tokenLimit && addedTokenCount + messageTokenCount > tokenLimit) {
                        return memoryContent;
                    }
                    memoryContent.push(chatMessage);
                    addedTokenCount += messageTokenCount;
                }
            } catch (error) {
                console.warn(`Failed to get content from memory block ${block.id}:`, error);
            }
        }
        return memoryContent;
    }
    /**
   * Manage the memory blocks
   * This method processes new messages into memory blocks when short-term memory exceeds its token limit.
   * It uses a cursor system to track which messages have already been processed into long-term memory.
   */ async manageMemoryBlocks() {
        // Early return if no memory blocks configured
        if (this.memoryBlocks.length === 0) {
            return;
        }
        // Should always calculate the number
        const shortTermTokenLimit = Math.ceil(this.tokenLimit * this.shortTermTokenLimitRatio);
        // Check if unprocessed messages exceed the short term token limit
        const unprocessedMessages = this.getUnprocessedMessages();
        const unprocessedMessagesTokenCount = this.countMemoryMessagesToken(unprocessedMessages);
        if (unprocessedMessagesTokenCount <= shortTermTokenLimit) {
            // No need to manage memory blocks yet
            return;
        }
        await this.processMessagesIntoMemoryBlocks(unprocessedMessages);
        this.updateMemoryCursor(unprocessedMessages.length);
    }
    /**
   * Get messages that haven't been processed into long-term memory yet
   */ getUnprocessedMessages() {
        if (this.memoryCursor >= this.messages.length) {
            return [];
        }
        return this.messages.slice(this.memoryCursor);
    }
    /**
   * Process new messages into all memory blocks
   */ async processMessagesIntoMemoryBlocks(newMessages) {
        const longTermMemoryBlocks = this.memoryBlocks.filter((block)=>block.isLongTerm);
        const promises = longTermMemoryBlocks.map(async (block)=>{
            try {
                await block.put(newMessages);
            } catch (error) {
                console.warn(`Failed to process messages into memory block ${block.id}:`, error);
            // Continue processing other blocks even if one fails
            }
        });
        // Wait for all memory blocks to process the messages
        await Promise.all(promises);
    }
    /**
   * Update the memory cursor after successful processing
   */ updateMemoryCursor(processedCount) {
        this.memoryCursor += processedCount;
        // Ensure cursor doesn't exceed message count
        this.memoryCursor = Math.min(this.memoryCursor, this.messages.length);
    }
    /**
   * Clear all the messages in the memory
   */ async clear() {
        this.messages = [];
        this.memoryCursor = 0; // Reset cursor when clearing messages
    }
    /**
   * Creates a snapshot of the current memory state
   * Note: Memory blocks are not included in snapshots as they may contain non-serializable content.
   * Memory blocks should be recreated when loading from snapshot.
   * @returns A JSON-serializable object containing the memory state
   */ snapshot() {
        return JSON.stringify({
            messages: this.messages,
            memoryCursor: this.memoryCursor
        });
    }
    countMemoryMessagesToken(messages) {
        return this.countMessagesToken(messages.map((m)=>this.adapters.llamaindex.fromMemory(m)));
    }
    countMessagesToken(messages) {
        if (messages.length === 0) {
            return 0;
        }
        const tokenizer = index_cjs.Settings.tokenizer;
        const str = messages.map((m)=>index_cjs$1.extractText(m.content)).join(" ");
        return tokenizer.encode(str).length;
    }
}

/**
 * Create a Memory instance
 * @param messagesOrOptions - Either initial messages or options
 * @param options - Memory configuration options (when first param is messages)
 * @returns A new Memory instance
 */ function createMemory(messagesOrOptions = [], options = {}) {
    let messages = [];
    if (Array.isArray(messagesOrOptions)) {
        const firstMessage = messagesOrOptions[0];
        if (firstMessage) {
            if ("id" in firstMessage) {
                messages = messagesOrOptions;
            } else {
                const adapter = new ChatMessageAdapter();
                messages = messagesOrOptions.map((chatMessage)=>adapter.toMemory(chatMessage));
            }
        }
    }
    return new Memory(messages, options);
}
/**
 * create a StaticMemoryBlock
 * @param options - Configuration options for the static memory block
 * @returns A new StaticMemoryBlock instance
 */ function staticBlock(options) {
    return new StaticMemoryBlock(options);
}
/**
 * create a FactExtractionMemoryBlock
 * @param options - Configuration options for the fact extraction memory block
 * @returns A new FactExtractionMemoryBlock instance
 */ function factExtractionBlock(options) {
    return new FactExtractionMemoryBlock(options);
}
/**
 * Creates a new Memory instance from a snapshot
 * @param snapshot The snapshot to load from
 * @param options Optional MemoryOptions to apply when loading (including memory blocks)
 * @returns A new Memory instance with the snapshot data and provided options
 */ function loadMemory(snapshot, options) {
    const { messages, tokenLimit, memoryCursor } = JSON.parse(snapshot);
    // Merge snapshot data with provided options
    const mergedOptions = {
        tokenLimit: options?.tokenLimit ?? tokenLimit ?? DEFAULT_TOKEN_LIMIT,
        ...options?.shortTermTokenLimitRatio && {
            shortTermTokenLimitRatio: options.shortTermTokenLimitRatio
        },
        ...options?.customAdapters && {
            customAdapters: options.customAdapters
        },
        memoryBlocks: options?.memoryBlocks ?? [],
        memoryCursor: memoryCursor ?? 0
    };
    return new Memory(messages, mergedOptions);
}

exports.BaseMemory = BaseMemory;
exports.BaseMemoryBlock = BaseMemoryBlock;
exports.ChatMemoryBuffer = ChatMemoryBuffer;
exports.ChatMessageAdapter = ChatMessageAdapter;
exports.ChatSummaryMemoryBuffer = ChatSummaryMemoryBuffer;
exports.FactExtractionMemoryBlock = FactExtractionMemoryBlock;
exports.Memory = Memory;
exports.StaticMemoryBlock = StaticMemoryBlock;
exports.VercelMessageAdapter = VercelMessageAdapter;
exports.createMemory = createMemory;
exports.factExtractionBlock = factExtractionBlock;
exports.loadMemory = loadMemory;
exports.staticBlock = staticBlock;
