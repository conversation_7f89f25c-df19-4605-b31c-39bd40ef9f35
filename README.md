# ChatDoc - Chat with Documents AI Application

A modern web application that allows users to upload documents and chat with them using AI-powered natural language processing, powered by **DeepSeek's advanced reasoning models**.

## Features

- **Document Upload**: Drag-and-drop interface for .txt and .md files
- **AI-Powered Chat**: Ask questions about uploaded documents using DeepSeek's reasoning models
- **Source Attribution**: All responses include references to source documents and relevant text chunks
- **Real-time Processing**: Documents are automatically indexed and made searchable upon upload
- **Modern UI**: Built with React, Next.js, and Tailwind CSS
- **DeepSeek Integration**: Advanced reasoning capabilities for better document understanding

## Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Next.js API routes
- **AI/ML**: LlamaIndex for document processing and RAG
- **LLM**: DeepSeek reasoning models for enhanced document analysis
- **Embeddings**: OpenAI embeddings for document vectorization
- **File Storage**: Local file system with metadata tracking

## Setup Instructions

### Prerequisites

- Node.js 18+
- npm or yarn
- DeepSeek API key (primary)
- OpenAI API key (for embeddings)

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
Edit `.env.local` and add your API keys:
```bash
# DeepSeek API Configuration (Primary)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_CHAT_MODEL=deepseek-chat
DEEPSEEK_REASONING_MODEL=deepseek-reasoner

# OpenAI API Key (for embeddings only)
OPENAI_API_KEY=your_openai_api_key_here
EMBEDDING_MODEL=text-embedding-3-small
```

3. Get your API keys:
   - **DeepSeek**: Visit [https://platform.deepseek.com/api_keys](https://platform.deepseek.com/api_keys)
   - **OpenAI**: Visit [https://platform.openai.com/api-keys](https://platform.openai.com/api-keys)

4. Start the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

## Usage

1. **Upload Documents**:
   - Click "Select Files" or drag and drop .txt or .md files
   - Wait for documents to be processed (status will show "Ready" when complete)

2. **Ask Questions**:
   - Type questions about your uploaded documents in the chat interface
   - DeepSeek's reasoning models will analyze your documents and provide intelligent responses
   - The AI maintains conversation context for follow-up questions

3. **View Sources**:
   - Each response includes source attribution showing which documents were referenced
   - Relevant text chunks are displayed for transparency
   - DeepSeek's advanced reasoning provides more accurate source identification
