# ChatDoc - Chat with Documents AI Application

A modern web application that allows users to upload documents and chat with them using AI-powered natural language processing.

## Features

- **Document Upload**: Drag-and-drop interface for .txt and .md files
- **AI-Powered Chat**: Ask questions about uploaded documents using natural language
- **Source Attribution**: All responses include references to source documents and relevant text chunks
- **Real-time Processing**: Documents are automatically indexed and made searchable upon upload
- **Modern UI**: Built with React, Next.js, and Tailwind CSS

## Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Next.js API routes
- **AI/ML**: LlamaIndex for document processing and RAG
- **File Storage**: Local file system with metadata tracking

## Setup Instructions

### Prerequisites

- Node.js 18+
- npm or yarn
- OpenAI API key

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
Edit `.env.local` and add your OpenAI API key:
```
OPENAI_API_KEY=your_openai_api_key_here
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Usage

1. **Upload Documents**:
   - Click "Select Files" or drag and drop .txt or .md files
   - Wait for documents to be processed (status will show "Ready" when complete)

2. **Ask Questions**:
   - Type questions about your uploaded documents in the chat interface
   - The AI will respond with relevant information and source references

3. **View Sources**:
   - Each response includes source attribution showing which documents were referenced
   - Relevant text chunks are displayed for transparency
