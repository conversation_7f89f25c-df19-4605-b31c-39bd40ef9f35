import { Observable } from "rxjs";
import { WorkflowStream } from "@llamaindex/workflow-core";

//#region src/observable.ts
const toObservable = (stream) => {
	return new Observable((subscriber) => {
		const reader = stream.getReader();
		const read = async () => {
			try {
				const { done, value } = await reader.read();
				if (done) subscriber.complete();
				else {
					subscriber.next(value);
					read();
				}
			} catch (error) {
				subscriber.error(error);
			}
		};
		read().catch(subscriber.error);
		return () => {
			reader.cancel().catch(subscriber.error);
		};
	});
};

//#endregion
export { toObservable };
//# sourceMappingURL=observable.js.map