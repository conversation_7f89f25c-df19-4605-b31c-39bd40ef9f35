
//#region src/core/utils.ts
const isPromiseLike = (value) => value != null && typeof value.then === "function";
const __internal__subscribesSourcemap = /* @__PURE__ */ new WeakMap();
/**
* @internal
*/
function createSubscribable() {
	const subscribers = /* @__PURE__ */ new Set();
	const obj = {
		subscribe: (callback) => {
			subscribers.add(callback);
			return () => {
				subscribers.delete(callback);
			};
		},
		publish: (...args) => {
			const results = [];
			for (const callback of subscribers) results.push(callback(...args));
			return results;
		}
	};
	__internal__subscribesSourcemap.set(obj, subscribers);
	return obj;
}

//#endregion
Object.defineProperty(exports, 'createSubscribable', {
  enumerable: true,
  get: function () {
    return createSubscribable;
  }
});
Object.defineProperty(exports, 'isPromiseLike', {
  enumerable: true,
  get: function () {
    return isPromiseLike;
  }
});
//# sourceMappingURL=utils-7Ahw9gTU.cjs.map