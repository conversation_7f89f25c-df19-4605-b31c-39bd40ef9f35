{"name": "@llama-flow/core", "version": "0.4.4", "description": "event-based workflow engine", "type": "module", "main": "dist/index.cjs", "types": "dist/index.d.ts", "module": "dist/index.js", "browser": "dist/browser/index.js", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "./async-context": {"browser": {"types": "./async-context/index.d.ts", "default": "./async-context/index.browser.js"}, "import": {"types": "./async-context/index.d.ts", "default": "./async-context/index.js"}, "require": {"types": "./async-context/index.d.cts", "default": "./async-context/index.cjs"}, "default": {"types": "./async-context/index.d.ts", "default": "./async-context/index.js"}}, "./hono": {"types": "./dist/hono.d.ts", "default": "./dist/hono.js"}, "./mcp": {"types": "./dist/mcp.d.ts", "default": "./dist/mcp.js"}, "./next": {"types": "./dist/next.d.ts", "default": "./dist/next.js"}, "./observable": {"types": "./dist/observable.d.ts", "default": "./dist/observable.js"}, "./middleware/state": {"import": {"types": "./middleware/state.d.ts", "default": "./middleware/state.js"}, "require": {"types": "./middleware/state.d.cts", "default": "./middleware/state.cjs"}, "default": {"types": "./middleware/state.d.ts", "default": "./middleware/state.js"}}, "./middleware/trace-events": {"import": {"types": "./middleware/trace-events.d.ts", "default": "./middleware/trace-events.js"}, "require": {"types": "./middleware/trace-events.d.cts", "default": "./middleware/trace-events.cjs"}, "default": {"types": "./middleware/trace-events.d.ts", "default": "./middleware/trace-events.js"}}, "./middleware/validation": {"import": {"types": "./middleware/validation.d.ts", "default": "./middleware/validation.js"}, "require": {"types": "./middleware/validation.d.cts", "default": "./middleware/validation.cjs"}, "default": {"types": "./middleware/validation.d.ts", "default": "./middleware/validation.js"}}, "./middleware/snapshot": {"import": {"types": "./middleware/snapshot.d.ts", "default": "./middleware/snapshot.js"}, "require": {"types": "./middleware/snapshot.d.cts", "default": "./middleware/snapshot.cjs"}, "default": {"types": "./middleware/snapshot.d.ts", "default": "./middleware/snapshot.js"}}, "./util/p-retry": {"types": "./util/p-retry.d.ts", "default": "./util/p-retry.js"}, "./util/zod": {"types": "./util/zod.d.ts", "default": "./util/zod.js"}, "./stream/consumer": {"types": "./stream/consumer.d.ts", "default": "./stream/consumer.js"}, "./stream/filter": {"types": "./stream/filter.d.ts", "default": "./stream/filter.js"}, "./stream/find": {"types": "./stream/find.d.ts", "default": "./stream/find.js"}, "./stream/until": {"types": "./stream/until.d.ts", "default": "./stream/until.js"}, "./stream/run": {"types": "./stream/run.d.ts", "default": "./stream/run.js"}}, "files": ["async-context", "dist", "interrupter", "util", "middleware", "stream"], "devDependencies": {"@modelcontextprotocol/sdk": "^1.10.1", "@types/node": "^22.14.1", "hono": "^4.7.7", "next": "^15.3.1", "p-retry": "^6.2.1", "rimraf": "^6.0.1", "rxjs": "^7.8.2", "stream-chain": "^3.4.0", "tsdown": "^0.11.12", "typescript": "^5.8.3", "zod": "^3.24.3"}, "peerDependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "hono": "^4.7.4", "next": "^15.2.2", "p-retry": "^6.2.1", "rxjs": "^7.8.2", "zod": "^3.24.2"}, "license": "MIT", "peerDependenciesMeta": {"@modelcontextprotocol/sdk": {"optional": true}, "hono": {"optional": true}, "next": {"optional": true}, "p-retry": {"optional": true}, "rxjs": {"optional": true}, "zod": {"optional": true}}, "repository": {"type": "git", "url": "https://github.com/run-llama/llama-flow.git"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf dist interrupter middleware stream util async-context && tsdown", "dev": "tsdown --watch", "test": "vitest run", "test:ui": "vitest --ui"}}