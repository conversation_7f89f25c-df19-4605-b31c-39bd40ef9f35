import { NextRequest, NextResponse } from 'next/server';
import llamaIndexService from '@/lib/llamaindex-service';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  sources?: Source[];
  timestamp: Date;
}

interface Source {
  document: string;
  chunk: string;
  relevance: number;
}

export async function POST(request: NextRequest) {
  try {
    const { message, history } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Convert chat history to LlamaIndex format
    const chatHistory = (history || []).map((msg: ChatMessage) => ({
      role: msg.type === 'user' ? 'user' : 'assistant',
      content: msg.content,
    }));

    try {
      // Query using LlamaIndex
      const result = await llamaIndexService.query(message, chatHistory);

      return NextResponse.json({
        response: result.response,
        sources: result.sources,
        timestamp: new Date().toISOString()
      });
    } catch (queryError) {
      console.error('LlamaIndex query error:', queryError);

      // Fallback response if LlamaIndex fails
      return NextResponse.json({
        response: `I apologize, but I encountered an error while processing your question: "${message}". This might be because no documents have been uploaded yet, or there was an issue with the AI service. Please make sure you have uploaded some documents and try again.`,
        sources: [],
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('Chat error:', error);
    return NextResponse.json(
      { error: 'Failed to process chat message' },
      { status: 500 }
    );
  }
}
