import { NextRequest, NextResponse } from 'next/server';
// import llamaIndexService from '@/lib/llamaindex-service';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  sources?: Source[];
  timestamp: Date;
}

interface Source {
  document: string;
  chunk: string;
  relevance: number;
}

export async function POST(request: NextRequest) {
  try {
    const { message, history } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {
      return NextResponse.json({
        response: "⚠️ OpenAI API key is not configured. Please add your OpenAI API key to the .env.local file to enable AI-powered responses.\n\nTo get started:\n1. Get an API key from https://platform.openai.com/api-keys\n2. Add it to your .env.local file as OPENAI_API_KEY=your_key_here\n3. Restart the development server",
        sources: [],
        timestamp: new Date().toISOString()
      });
    }

    // For now, provide a demo response until LlamaIndex is properly configured
    const demoResponse = `Thank you for your question: "${message}"

🚧 **Demo Mode** - This is a demonstration of the ChatDoc interface.

To enable full AI-powered document chat:
1. Configure your OpenAI API key in .env.local
2. Upload some documents (.txt or .md files)
3. Ask questions about your uploaded content

**Current Status:**
- ✅ Frontend interface working
- ✅ File upload system working
- ✅ Document management working
- ⚠️ AI integration pending API key configuration

Once configured, I'll be able to:
- Analyze your uploaded documents
- Answer questions based on document content
- Provide source attribution and relevant text chunks
- Maintain conversation context`;

    const demoSources: Source[] = [
      {
        document: "demo-document.txt",
        chunk: "This is a demonstration of how source attribution will work once documents are uploaded and processed...",
        relevance: 0.95
      }
    ];

    return NextResponse.json({
      response: demoResponse,
      sources: demoSources,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Chat error:', error);
    return NextResponse.json(
      { error: 'Failed to process chat message' },
      { status: 500 }
    );
  }
}
