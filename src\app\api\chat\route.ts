import { NextRequest, NextResponse } from 'next/server';
import llamaIndexService from '@/lib/llamaindex-service';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  sources?: Source[];
  timestamp: Date;
}

interface Source {
  document: string;
  chunk: string;
  relevance: number;
}

export async function POST(request: NextRequest) {
  try {
    const { message, history } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Check if DeepSeek API key is configured
    if (!process.env.DEEPSEEK_API_KEY || process.env.DEEPSEEK_API_KEY === 'your_deepseek_api_key_here') {
      return NextResponse.json({
        response: "⚠️ DeepSeek API key is not configured. Please add your DeepSeek API key to the .env.local file to enable AI-powered responses.\n\nTo get started:\n1. Get an API key from https://platform.deepseek.com/api_keys\n2. Add it to your .env.local file as DEEPSEEK_API_KEY=your_key_here\n3. Restart the development server\n\n🔧 **DeepSeek Integration Active** - This application now uses DeepSeek's reasoning models for enhanced document analysis!",
        sources: [],
        timestamp: new Date().toISOString()
      });
    }

    // Convert chat history to LlamaIndex format
    const chatHistory = (history || []).map((msg: ChatMessage) => ({
      role: msg.type === 'user' ? 'user' : 'assistant',
      content: msg.content,
    }));

    try {
      // Query using LlamaIndex with DeepSeek
      const result = await llamaIndexService.query(message, chatHistory);

      return NextResponse.json({
        response: result.response,
        sources: result.sources,
        timestamp: new Date().toISOString()
      });
    } catch (queryError) {
      console.error('DeepSeek LlamaIndex query error:', queryError);

      // Fallback response if DeepSeek/LlamaIndex fails
      return NextResponse.json({
        response: `I apologize, but I encountered an error while processing your question: "${message}".

**Possible issues:**
- DeepSeek API key might be invalid or expired
- No documents have been uploaded yet
- Network connectivity issues

**🔧 DeepSeek Integration Status:**
- ✅ DeepSeek API configured
- ⚠️ Error occurred during query processing

Please check your API key and ensure you have uploaded some documents, then try again.

**Error details:** ${queryError instanceof Error ? queryError.message : 'Unknown error'}`,
        sources: [],
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('Chat error:', error);
    return NextResponse.json(
      { error: 'Failed to process chat message' },
      { status: 500 }
    );
  }
}
