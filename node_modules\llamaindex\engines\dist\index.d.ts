import { BaseChatEngine, NonStreamingChatEngineParams, StreamingChatEngineParams } from '@llamaindex/core/chat-engine';
export * from '@llamaindex/core/chat-engine';
import { LLM, ChatMessage, ToolMetadata, BaseTool } from '@llamaindex/core/llms';
import { Memory } from '@llamaindex/core/memory';
import { CondenseQuestionPrompt, ModuleRecord, PromptsRecord } from '@llamaindex/core/prompts';
import { BaseQueryEngine, QueryType } from '@llamaindex/core/query-engine';
export { RetrieverQueryEngine } from '@llamaindex/core/query-engine';
import * as _llamaindex_core_schema from '@llamaindex/core/schema';
import { EngineResponse } from '@llamaindex/core/schema';
import { BaseSynthesizer } from '@llamaindex/core/response-synthesizers';
import { BaseSelector } from '../../selectors/dist/index.js';

/**
 * CondenseQuestionChatEngine is used in conjunction with a Index (for example VectorStoreIndex).
 * It does two steps on taking a user's chat message: first, it condenses the chat message
 * with the previous chat history into a question with more context.
 * Then, it queries the underlying Index using the new question with context and returns
 * the response.
 * CondenseQuestionChatEngine performs well when the input is primarily questions about the
 * underlying data. It performs less well when the chat messages are not questions about the
 * data, or are very referential to previous context.
 */
declare class CondenseQuestionChatEngine extends BaseChatEngine {
    queryEngine: BaseQueryEngine;
    memory: Memory;
    llm: LLM;
    condenseMessagePrompt: CondenseQuestionPrompt;
    get chatHistory(): Promise<ChatMessage[]>;
    constructor(init: {
        queryEngine: BaseQueryEngine;
        chatHistory: ChatMessage[];
        condenseMessagePrompt?: CondenseQuestionPrompt;
    });
    protected _getPromptModules(): ModuleRecord;
    protected _getPrompts(): {
        condenseMessagePrompt: CondenseQuestionPrompt;
    };
    protected _updatePrompts(promptsDict: {
        condenseMessagePrompt: CondenseQuestionPrompt;
    }): void;
    private condenseQuestion;
    chat(params: NonStreamingChatEngineParams): Promise<EngineResponse>;
    chat(params: StreamingChatEngineParams): Promise<AsyncIterable<EngineResponse>>;
    reset(): void;
}

type RouterQueryEngineTool = {
    queryEngine: BaseQueryEngine;
    description: string;
};
/**
 * A query engine that uses multiple query engines and selects the best one.
 */
declare class RouterQueryEngine extends BaseQueryEngine {
    private selector;
    private queryEngines;
    private metadatas;
    private summarizer;
    private verbose;
    constructor(init: {
        selector: BaseSelector;
        queryEngineTools: RouterQueryEngineTool[];
        summarizer?: BaseSynthesizer | undefined;
        verbose?: boolean | undefined;
    });
    _query(strOrQueryBundle: QueryType, stream?: boolean): Promise<EngineResponse>;
    protected _getPrompts(): {};
    protected _updatePrompts(): void;
    protected _getPromptModules(): {
        selector: BaseSelector;
        summarizer: BaseSynthesizer;
    };
    static fromDefaults(init: {
        queryEngineTools: RouterQueryEngineTool[];
        selector?: BaseSelector;
        summarizer?: BaseSynthesizer;
        verbose?: boolean;
    }): RouterQueryEngine;
    private queryRoute;
}

/**
 * QuestionGenerators generate new questions for the LLM using tools and a user query.
 */
interface BaseQuestionGenerator {
    generate(tools: ToolMetadata[], query: QueryType): Promise<SubQuestion[]>;
}
interface SubQuestion {
    subQuestion: string;
    toolName: string;
}

/**
 * SubQuestionQueryEngine decomposes a question into subquestions and then
 */
declare class SubQuestionQueryEngine extends BaseQueryEngine {
    responseSynthesizer: BaseSynthesizer;
    questionGen: BaseQuestionGenerator;
    queryEngines: BaseTool[];
    metadatas: ToolMetadata[];
    constructor(init: {
        questionGen: BaseQuestionGenerator;
        responseSynthesizer: BaseSynthesizer;
        queryEngineTools: BaseTool[];
    });
    _query(strOrQueryBundle: QueryType, stream?: boolean): Promise<_llamaindex_core_schema.EngineResponse | AsyncIterable<_llamaindex_core_schema.EngineResponse>>;
    protected _getPrompts(): PromptsRecord;
    protected _updatePrompts(): void;
    protected _getPromptModules(): Record<string, any>;
    static fromDefaults(init: {
        queryEngineTools: BaseTool[];
        questionGen?: BaseQuestionGenerator;
        responseSynthesizer?: BaseSynthesizer;
    }): SubQuestionQueryEngine;
    private querySubQ;
}

export { CondenseQuestionChatEngine, RouterQueryEngine, SubQuestionQueryEngine };
