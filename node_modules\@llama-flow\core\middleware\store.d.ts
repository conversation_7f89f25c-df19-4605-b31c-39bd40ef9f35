import { Workflow as Workflow$1, WorkflowContext } from "@llama-flow/core";

//#region src/middleware/store.d.ts
type WithState<State, Input> = Input extends void | undefined ? {
  <Workflow extends Workflow$1>(workflow: Workflow): Omit<Workflow, "createContext"> & {
    createContext(): ReturnType<Workflow["createContext"]> & {
      get state(): State;
    };
  };
} : {
  <Workflow extends Workflow$1>(workflow: Workflow): Omit<Workflow, "createContext"> & {
    createContext(input: Input): ReturnType<Workflow["createContext"]> & {
      get state(): State;
    };
  };
};
type CreateState<State, Input, Context extends WorkflowContext> = {
  getContext(): Context & {
    get state(): State;
  };
  withState: WithState<State, Input>;
};
declare function createStateMiddleware<State, Input = void, Context extends WorkflowContext = WorkflowContext>(init: (input: Input) => State): CreateState<State, Input, Context>;

//#endregion
export { createStateMiddleware };
//# sourceMappingURL=store.d.ts.map