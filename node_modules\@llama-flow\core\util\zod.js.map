{"version": 3, "file": "zod.js", "names": ["schema: z.<PERSON><T>", "config?: WorkflowEventConfig<DebugLabel>", "data: T"], "sources": ["../src/util/zod.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport {\n  workflowEvent,\n  type WorkflowEvent,\n  type WorkflowEventConfig,\n} from \"@llama-flow/core\";\n\nexport const zodEvent = <T, DebugLabel extends string>(\n  schema: z.ZodType<T>,\n  config?: WorkflowEventConfig<DebugLabel>,\n): WorkflowEvent<T, DebugLabel> & { readonly schema: z.ZodType<T> } => {\n  const event = workflowEvent<T, DebugLabel>(config);\n  const originalWith = event.with;\n\n  return Object.assign(event, {\n    with: (data: T) => {\n      schema.parse(data);\n      return originalWith(data);\n    },\n    get schema() {\n      return schema;\n    },\n  });\n};\n"], "mappings": ";;;;AAOA,MAAa,WAAW,CACtBA,QACAC,WACqE;CACrE,MAAM,QAAQ,cAA6B,OAAO;CAClD,MAAM,eAAe,MAAM;AAE3B,QAAO,OAAO,OAAO,OAAO;EAC1B,MAAM,CAACC,SAAY;AACjB,UAAO,MAAM,KAAK;AAClB,UAAO,aAAa,KAAK;EAC1B;EACD,IAAI,SAAS;AACX,UAAO;EACR;CACF,EAAC;AACH"}