{"name": "@llamaindex/node-parser", "version": "2.0.13", "description": "Node parser for LlamaIndex", "type": "module", "exports": {"./html": {"require": {"types": "./html/dist/index.d.cts", "default": "./html/dist/index.cjs"}, "import": {"types": "./html/dist/index.d.ts", "default": "./html/dist/index.js"}}, "./code": {"require": {"types": "./code/dist/index.d.cts", "default": "./code/dist/index.cjs"}, "import": {"types": "./code/dist/index.d.ts", "default": "./code/dist/index.js"}}}, "files": ["html", "code"], "repository": {"type": "git", "url": "git+https://github.com/run-llama/LlamaIndexTS.git", "directory": "packages/node-parser"}, "devDependencies": {"@types/html-to-text": "^9.0.4", "@types/node": "^22.9.0", "tree-sitter": "^0.22.1", "web-tree-sitter": "^0.24.4", "@llamaindex/core": "0.6.13", "@llamaindex/env": "0.1.30"}, "peerDependencies": {"tree-sitter": "^0.22.0", "web-tree-sitter": "^0.24.3", "@llamaindex/core": "0.6.13", "@llamaindex/env": "0.1.30"}, "dependencies": {"html-to-text": "^9.0.5"}, "scripts": {"build": "bunchee", "dev": "bunchee --watch"}}