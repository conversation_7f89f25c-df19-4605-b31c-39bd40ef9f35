"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/is-network-error";
exports.ids = ["vendor-chunks/is-network-error"];
exports.modules = {

/***/ "(rsc)/./node_modules/is-network-error/index.js":
/*!************************************************!*\
  !*** ./node_modules/is-network-error/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ isNetworkError)\n/* harmony export */ });\nconst objectToString = Object.prototype.toString;\n\nconst isError = value => objectToString.call(value) === '[object Error]';\n\nconst errorMessages = new Set([\n\t'network error', // Chrome\n\t'Failed to fetch', // Chrome\n\t'NetworkError when attempting to fetch resource.', // Firefox\n\t'The Internet connection appears to be offline.', // Safari 16\n\t'Load failed', // Safari 17+\n\t'Network request failed', // `cross-fetch`\n\t'fetch failed', // Undici (Node.js)\n\t'terminated', // Undici (Node.js)\n]);\n\nfunction isNetworkError(error) {\n\tconst isValid = error\n\t\t&& isError(error)\n\t\t&& error.name === 'TypeError'\n\t\t&& typeof error.message === 'string';\n\n\tif (!isValid) {\n\t\treturn false;\n\t}\n\n\t// We do an extra check for Safari 17+ as it has a very generic error message.\n\t// Network errors in Safari have no stack.\n\tif (error.message === 'Load failed') {\n\t\treturn error.stack === undefined;\n\t}\n\n\treturn errorMessages.has(error.message);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaXMtbmV0d29yay1lcnJvci9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJEOlxcY29kZVxcY2hhdGRvYy12MVxcbm9kZV9tb2R1bGVzXFxpcy1uZXR3b3JrLWVycm9yXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBvYmplY3RUb1N0cmluZyA9IE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmc7XG5cbmNvbnN0IGlzRXJyb3IgPSB2YWx1ZSA9PiBvYmplY3RUb1N0cmluZy5jYWxsKHZhbHVlKSA9PT0gJ1tvYmplY3QgRXJyb3JdJztcblxuY29uc3QgZXJyb3JNZXNzYWdlcyA9IG5ldyBTZXQoW1xuXHQnbmV0d29yayBlcnJvcicsIC8vIENocm9tZVxuXHQnRmFpbGVkIHRvIGZldGNoJywgLy8gQ2hyb21lXG5cdCdOZXR3b3JrRXJyb3Igd2hlbiBhdHRlbXB0aW5nIHRvIGZldGNoIHJlc291cmNlLicsIC8vIEZpcmVmb3hcblx0J1RoZSBJbnRlcm5ldCBjb25uZWN0aW9uIGFwcGVhcnMgdG8gYmUgb2ZmbGluZS4nLCAvLyBTYWZhcmkgMTZcblx0J0xvYWQgZmFpbGVkJywgLy8gU2FmYXJpIDE3K1xuXHQnTmV0d29yayByZXF1ZXN0IGZhaWxlZCcsIC8vIGBjcm9zcy1mZXRjaGBcblx0J2ZldGNoIGZhaWxlZCcsIC8vIFVuZGljaSAoTm9kZS5qcylcblx0J3Rlcm1pbmF0ZWQnLCAvLyBVbmRpY2kgKE5vZGUuanMpXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gaXNOZXR3b3JrRXJyb3IoZXJyb3IpIHtcblx0Y29uc3QgaXNWYWxpZCA9IGVycm9yXG5cdFx0JiYgaXNFcnJvcihlcnJvcilcblx0XHQmJiBlcnJvci5uYW1lID09PSAnVHlwZUVycm9yJ1xuXHRcdCYmIHR5cGVvZiBlcnJvci5tZXNzYWdlID09PSAnc3RyaW5nJztcblxuXHRpZiAoIWlzVmFsaWQpIHtcblx0XHRyZXR1cm4gZmFsc2U7XG5cdH1cblxuXHQvLyBXZSBkbyBhbiBleHRyYSBjaGVjayBmb3IgU2FmYXJpIDE3KyBhcyBpdCBoYXMgYSB2ZXJ5IGdlbmVyaWMgZXJyb3IgbWVzc2FnZS5cblx0Ly8gTmV0d29yayBlcnJvcnMgaW4gU2FmYXJpIGhhdmUgbm8gc3RhY2suXG5cdGlmIChlcnJvci5tZXNzYWdlID09PSAnTG9hZCBmYWlsZWQnKSB7XG5cdFx0cmV0dXJuIGVycm9yLnN0YWNrID09PSB1bmRlZmluZWQ7XG5cdH1cblxuXHRyZXR1cm4gZXJyb3JNZXNzYWdlcy5oYXMoZXJyb3IubWVzc2FnZSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/is-network-error/index.js\n");

/***/ })

};
;