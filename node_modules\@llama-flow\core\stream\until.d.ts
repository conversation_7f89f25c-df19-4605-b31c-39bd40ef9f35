import { WorkflowEvent, WorkflowEventData, WorkflowStream } from "@llama-flow/core";

//#region src/stream/until.d.ts
/**
* @deprecated use `stream.until` instead. This will be removed in the next minor version.
*/
/**
 * @deprecated use `stream.until` instead. This will be removed in the next minor version.
 */
declare function until(stream: WorkflowStream | ReadableStream<WorkflowEventData<any>>, cond: (event: WorkflowEventData<any>) => boolean | Promise<boolean>): WorkflowStream;
declare function until<Stop>(stream: WorkflowStream | ReadableStream<WorkflowEventData<any>>, cond: WorkflowEvent<Stop>): WorkflowStream;

//#endregion
export { until };
//# sourceMappingURL=until.d.ts.map