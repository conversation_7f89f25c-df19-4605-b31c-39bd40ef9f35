import { Handler, WorkflowContext, WorkflowEvent, WorkflowEventData, WorkflowStream } from "@llama-flow/core";

//#region src/core/event.d.ts
declare const opaqueSymbol: unique symbol;
type WorkflowEventData$1<Data, DebugLabel extends string = string> = {
  get data(): Data;
} & {
  readonly [opaqueSymbol]: DebugLabel;
};
type WorkflowEvent$1<Data, DebugLabel extends string = string> = {
  /**
   * This is the label used for debugging purposes.
   */
  debugLabel?: DebugLabel;
  /**
   * This is the unique identifier for the event, which is used for sharing cross the network boundaries.
   */
  readonly uniqueId: string;
  with(data: Data): WorkflowEventData$1<Data, DebugLabel>;
  include(event: unknown): event is WorkflowEventData$1<Data, DebugLabel>;
} & {
  readonly [opaqueSymbol]: DebugLabel;
};

//#endregion
//#region src/core/context.d.ts
type Handler$1<AcceptEvents extends WorkflowEvent<any>[], Result extends WorkflowEventData<any> | void> = (...event: { [K in keyof AcceptEvents]: ReturnType<AcceptEvents[K]["with"]> }) => Result | Promise<Result>;
type BaseHandlerContext = {
  abortController: AbortController;
  handler: Handler$1<WorkflowEvent<any>[], any>;
  inputEvents: WorkflowEvent<any>[];
  inputs: WorkflowEventData<any>[];
  outputs: WorkflowEventData<any>[];
  prev: HandlerContext;
  next: Set<HandlerContext>;
  root: HandlerContext;
};
type SyncHandlerContext = BaseHandlerContext & {
  async: false;
  pending: null;
};
type AsyncHandlerContext = BaseHandlerContext & {
  async: true;
  pending: Promise<WorkflowEventData<any> | void> | null;
};
type HandlerContext = AsyncHandlerContext | SyncHandlerContext;

//#endregion
//#region src/middleware/trace-events/create-handler-decorator.d.ts
declare function createHandlerDecorator<Metadata>(config: {
  debugLabel?: string;
  getInitialValue: () => Metadata;
  onBeforeHandler: (handler: Handler<WorkflowEvent<any>[], WorkflowEventData<any> | void>, handlerContext: HandlerContext, metadata: Metadata) => Handler<WorkflowEvent<any>[], WorkflowEventData<any> | void>;
  onAfterHandler: (metadata: Metadata) => Metadata;
}): <const AcceptEvents extends WorkflowEvent<any>[], Result extends ReturnType<WorkflowEvent<any>["with"]> | void, Fn extends Handler<AcceptEvents, Result>>(handler: Fn) => Fn;

//#endregion
//#region src/middleware/trace-events/run-once.d.ts
declare const runOnce: <const AcceptEvents extends WorkflowEvent$1<any>[], Result extends ReturnType<WorkflowEvent$1<any>["with"]> | void, Fn extends Handler$1<AcceptEvents, Result>>(handler: Fn) => Fn;

//#endregion
//#region src/middleware/trace-events.d.ts
declare function getEventOrigins(eventData: WorkflowEventData<any>, context?: WorkflowContext): [WorkflowEventData<any>, ...WorkflowEventData<any>[]];
type HandlerRef<AcceptEvents extends WorkflowEvent<any>[], Result extends ReturnType<WorkflowEvent<any>["with"]> | void, Fn extends Handler<AcceptEvents, Result>> = {
  get handler(): Fn;
};
declare function withTraceEvents<WorkflowLike extends {
  handle<const AcceptEvents extends WorkflowEvent<any>[], Result extends ReturnType<WorkflowEvent<any>["with"]> | void>(accept: AcceptEvents, handler: Handler<AcceptEvents, Result>): void;
  createContext(): WorkflowContext;
}>(workflow: WorkflowLike): Omit<WorkflowLike, "handle"> & {
  handle<const AcceptEvents extends WorkflowEvent<any>[], Result extends ReturnType<WorkflowEvent<any>["with"]> | void, Fn extends Handler<AcceptEvents, Result>>(accept: AcceptEvents, handler: Fn): HandlerRef<AcceptEvents, Result, Fn>;
  substream<T extends WorkflowEventData<any>>(eventData: WorkflowEventData<any>, stream: WorkflowStream<T>): WorkflowStream<T>;
};

//#endregion
export { HandlerRef, createHandlerDecorator, getEventOrigins, runOnce, withTraceEvents };
//# sourceMappingURL=trace-events.d.ts.map