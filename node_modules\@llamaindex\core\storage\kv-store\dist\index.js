import { path, fs } from '@llamaindex/env';
import { DEFAULT_COLLECTION } from '../../../global/dist/index.js';

async function exists(path) {
    try {
        await fs.access(path);
        return true;
    } catch  {
        return false;
    }
}
class BaseKVStore {
}
class BaseInMemoryKVStore extends BaseKVStore {
    static fromPersistPath(persistPath) {
        throw new Error("Method not implemented.");
    }
}
class SimpleKVStore extends BaseKVStore {
    constructor(data = {}){
        super(), this.data = data;
    }
    async put(key, val, collection = DEFAULT_COLLECTION) {
        if (!(collection in this.data)) {
            this.data[collection] = {};
        }
        this.data[collection][key] = structuredClone(val); // Creating a shallow copy of the object
        if (this.persistPath) {
            await this.persist(this.persistPath);
        }
    }
    async get(key, collection = DEFAULT_COLLECTION) {
        const collectionData = this.data[collection];
        if (collectionData == null) {
            return null;
        }
        if (!(key in collectionData)) {
            return null;
        }
        return structuredClone(collectionData[key]); // Creating a shallow copy of the object
    }
    async getAll(collection = DEFAULT_COLLECTION) {
        if (this.data[collection]) {
            return structuredClone(this.data[collection]);
        }
        return {};
    }
    async delete(key, collection = DEFAULT_COLLECTION) {
        if (key in this.data[collection]) {
            delete this.data[collection][key];
            if (this.persistPath) {
                await this.persist(this.persistPath);
            }
            return true;
        }
        return false;
    }
    async persist(persistPath) {
        // TODO: decide on a way to polyfill path
        const dirPath = path.dirname(persistPath);
        if (!await exists(dirPath)) {
            await fs.mkdir(dirPath);
        }
        await fs.writeFile(persistPath, JSON.stringify(this.data));
    }
    static async fromPersistPath(persistPath) {
        const dirPath = path.dirname(persistPath);
        if (!await exists(dirPath)) {
            await fs.mkdir(dirPath, {
                recursive: true
            });
        }
        let data = {};
        if (!await exists(persistPath)) {
            console.info(`Starting new store from path: ${persistPath}`);
        } else {
            try {
                const fileData = await fs.readFile(persistPath);
                data = JSON.parse(fileData.toString());
            } catch (e) {
                throw new Error(`Failed to load data from path: ${persistPath}`, {
                    cause: e
                });
            }
        }
        const store = new SimpleKVStore(data);
        store.persistPath = persistPath;
        return store;
    }
    toDict() {
        return this.data;
    }
    static fromDict(saveDict) {
        return new SimpleKVStore(saveDict);
    }
}

export { BaseInMemoryKVStore, BaseKVStore, SimpleKVStore };
