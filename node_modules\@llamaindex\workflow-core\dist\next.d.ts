import { NextRequest } from "next/server";
import { Workflow, WorkflowEvent, WorkflowEventData } from "@llamaindex/workflow-core";

//#region src/next.d.ts
type WorkflowAPI = {
  GET: (request: NextRequest) => Promise<Response>;
};
declare const createNextHandler: <Start, Stop>(workflow: Workflow, getStart: (request: NextRequest) => WorkflowEventData<Start> | Promise<WorkflowEventData<Start>>, stop: WorkflowEvent<Stop>) => WorkflowAPI;
//#endregion
export { createNextHandler };
//# sourceMappingURL=next.d.ts.map