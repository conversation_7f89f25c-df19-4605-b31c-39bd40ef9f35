{"version": 3, "file": "hono.js", "names": ["workflow: Workflow", "getStart: (\n    c: Context,\n  ) => WorkflowEventData<Start> | Promise<WorkflowEventData<Start>>", "stopEvent: WorkflowEvent<Stop>", "wrapStopEvent?: (c: Context, stop: Stop) => Response"], "sources": ["../src/hono.ts"], "sourcesContent": ["import type { Context, <PERSON><PERSON> } from \"hono\";\nimport type {\n  Workflow,\n  WorkflowEvent,\n  WorkflowEventData,\n} from \"@llamaindex/workflow-core\";\nimport { run } from \"./stream/run\";\n\nexport const createHonoHandler = <Start, Stop>(\n  workflow: Workflow,\n  getStart: (\n    c: Context,\n  ) => WorkflowEventData<Start> | Promise<WorkflowEventData<Start>>,\n  stopEvent: WorkflowEvent<Stop>,\n  wrapStopEvent?: (c: Context, stop: Stop) => Response,\n): Handler => {\n  if (!wrapStopEvent) {\n    wrapStopEvent = (c, stop) => {\n      return c.json(stop as any);\n    };\n  }\n  return async (c) => {\n    const results = await run(workflow, await getStart(c))\n      .until(stopEvent)\n      .toArray();\n    return wrapStopEvent(c, results.at(-1)!.data);\n  };\n};\n"], "mappings": ";;;AAQA,MAAa,oBAAoB,CAC/BA,UACAC,UAGAC,WACAC,kBACY;AACZ,MAAK,cACH,iBAAgB,CAAC,GAAG,SAAS;AAC3B,SAAO,EAAE,KAAK,KAAY;CAC3B;AAEH,QAAO,OAAO,MAAM;EAClB,MAAM,UAAU,MAAM,IAAI,UAAU,MAAM,SAAS,EAAE,CAAC,CACnD,MAAM,UAAU,CAChB,SAAS;AACZ,SAAO,cAAc,GAAG,QAAQ,GAAG,GAAG,CAAE,KAAK;CAC9C;AACF"}