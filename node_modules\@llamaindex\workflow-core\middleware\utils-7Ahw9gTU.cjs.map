{"version": 3, "file": "utils-7Ahw9gTU.cjs", "names": ["value: unknown", "callback: (...args: any) => any", "results: unknown[]"], "sources": ["../src/core/utils.ts"], "sourcesContent": ["import type { WorkflowEvent, WorkflowEventData } from \"./event\";\n\nexport const isEventData = (data: unknown): data is WorkflowEventData<any> =>\n  data != null && typeof data === \"object\" && \"data\" in data;\n\nexport const isPromiseLike = (value: unknown): value is PromiseLike<unknown> =>\n  value != null && typeof (value as PromiseLike<unknown>).then === \"function\";\n\nexport function flattenEvents(\n  acceptEventTypes: WorkflowEvent<any>[],\n  inputEventData: WorkflowEventData<any>[],\n): WorkflowEventData<any>[] {\n  const acceptance: WorkflowEventData<any>[] = new Array(\n    acceptEventTypes.length,\n  );\n  for (const eventData of inputEventData) {\n    for (let i = 0; i < acceptEventTypes.length; i++) {\n      if (acceptance[i]) {\n        continue;\n      }\n      if (acceptEventTypes[i]!.include(eventData)) {\n        acceptance[i] = eventData;\n        break;\n      }\n    }\n  }\n  return acceptance.filter(Boolean);\n}\n\nexport type Subscribable<Args extends any[], R> = {\n  subscribe: (callback: (...args: Args) => R) => () => void;\n  publish: (...args: Args) => unknown[];\n};\n\nconst __internal__subscribesSourcemap = new WeakMap<\n  Subscribable<any, any>,\n  Set<(...args: any[]) => any>\n>();\n\n/**\n * @internal\n */\nexport function getSubscribers<Args extends any[], R>(\n  subscribable: Subscribable<Args, R>,\n): Set<(...args: Args) => R> {\n  return __internal__subscribesSourcemap.get(subscribable)!;\n}\n\n/**\n * @internal\n */\nexport function createSubscribable<\n  FnOrArgs extends ((...args: any[]) => any) | any[],\n  R = unknown,\n>(): FnOrArgs extends (...args: any[]) => any\n  ? Subscribable<Parameters<FnOrArgs>, ReturnType<FnOrArgs>>\n  : FnOrArgs extends any[]\n    ? Subscribable<FnOrArgs, R>\n    : never {\n  const subscribers = new Set<(...args: any) => any>();\n  const obj = {\n    subscribe: (callback: (...args: any) => any) => {\n      subscribers.add(callback);\n      return () => {\n        subscribers.delete(callback);\n      };\n    },\n    publish: (...args: any) => {\n      const results: unknown[] = [];\n      for (const callback of subscribers) {\n        results.push(callback(...args));\n      }\n      return results;\n    },\n  };\n  __internal__subscribesSourcemap.set(obj, subscribers);\n  return obj as any;\n}\n"], "mappings": ";;AAKA,MAAa,gBAAgB,CAACA,UAC5B,SAAS,eAAgB,MAA+B,SAAS;AA4BnE,MAAM,kDAAkC,IAAI;;;;AAiB5C,SAAgB,qBAOJ;CACV,MAAM,8BAAc,IAAI;CACxB,MAAM,MAAM;EACV,WAAW,CAACC,aAAoC;AAC9C,eAAY,IAAI,SAAS;AACzB,UAAO,MAAM;AACX,gBAAY,OAAO,SAAS;GAC7B;EACF;EACD,SAAS,CAAC,GAAG,SAAc;GACzB,MAAMC,UAAqB,CAAE;AAC7B,QAAK,MAAM,YAAY,YACrB,SAAQ,KAAK,SAAS,GAAG,KAAK,CAAC;AAEjC,UAAO;EACR;CACF;AACD,iCAAgC,IAAI,KAAK,YAAY;AACrD,QAAO;AACR"}