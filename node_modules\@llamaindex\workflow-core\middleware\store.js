import { getContext } from "@llamaindex/workflow-core";

//#region src/middleware/store.ts
function createStateMiddleware(init) {
	return {
		getContext,
		withState: (workflow) => {
			return {
				...workflow,
				createContext: (input) => {
					const state = init(input);
					const context = workflow.createContext();
					context.__internal__call_context.subscribe((_, next) => {
						const context$1 = getContext();
						if (!Reflect.has(context$1, "state")) Object.defineProperty(context$1, "state", { get: () => state });
						next(_);
					});
					if (!Reflect.has(context, "state")) Object.defineProperty(context, "state", { get: () => state });
					return context;
				}
			};
		}
	};
}

//#endregion
export { createStateMiddleware };
//# sourceMappingURL=store.js.map