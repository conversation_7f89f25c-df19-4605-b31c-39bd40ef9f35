import { WorkflowEventData, WorkflowStream } from "@llama-flow/core";

//#region src/stream/filter.d.ts
/**
* @deprecated use `stream.filter` instead. This will be removed in the next minor version.
*/
/**
 * @deprecated use `stream.filter` instead. This will be removed in the next minor version.
 */
declare function filter<Event extends WorkflowEventData<any>, Final extends Event>(stream: ReadableStream<Event> | WorkflowStream<Event>, cond: (event: Event) => event is Final): ReadableStream<Final> | WorkflowStream<Final>;

//#endregion
export { filter };
//# sourceMappingURL=filter.d.ts.map