{"name": "@llamaindex/core", "type": "module", "version": "0.6.13", "description": "LlamaIndex Core Module", "exports": {"./agent": {"require": {"types": "./agent/dist/index.d.cts", "default": "./agent/dist/index.cjs"}, "import": {"types": "./agent/dist/index.d.ts", "default": "./agent/dist/index.js"}, "default": "./agent/dist/index.js"}, "./objects": {"require": {"types": "./objects/dist/index.d.cts", "default": "./objects/dist/index.cjs"}, "import": {"types": "./objects/dist/index.d.ts", "default": "./objects/dist/index.js"}, "default": "./objects/dist/index.js"}, "./node-parser": {"require": {"types": "./node-parser/dist/index.d.cts", "default": "./node-parser/dist/index.cjs"}, "import": {"types": "./node-parser/dist/index.d.ts", "default": "./node-parser/dist/index.js"}, "default": "./node-parser/dist/index.js"}, "./query-engine": {"require": {"types": "./query-engine/dist/index.d.cts", "default": "./query-engine/dist/index.cjs"}, "import": {"types": "./query-engine/dist/index.d.ts", "default": "./query-engine/dist/index.js"}, "default": "./query-engine/dist/index.js"}, "./llms": {"require": {"types": "./llms/dist/index.d.cts", "default": "./llms/dist/index.cjs"}, "import": {"types": "./llms/dist/index.d.ts", "default": "./llms/dist/index.js"}, "default": "./llms/dist/index.js"}, "./decorator": {"require": {"types": "./decorator/dist/index.d.cts", "default": "./decorator/dist/index.cjs"}, "import": {"types": "./decorator/dist/index.d.ts", "default": "./decorator/dist/index.js"}, "default": "./decorator/dist/index.js"}, "./embeddings": {"require": {"types": "./embeddings/dist/index.d.cts", "default": "./embeddings/dist/index.cjs"}, "import": {"types": "./embeddings/dist/index.d.ts", "default": "./embeddings/dist/index.js"}, "default": "./embeddings/dist/index.js"}, "./global": {"require": {"types": "./global/dist/index.d.cts", "default": "./global/dist/index.cjs"}, "import": {"types": "./global/dist/index.d.ts", "default": "./global/dist/index.js"}, "default": "./global/dist/index.js"}, "./schema": {"require": {"types": "./schema/dist/index.d.cts", "default": "./schema/dist/index.cjs"}, "import": {"types": "./schema/dist/index.d.ts", "default": "./schema/dist/index.js"}, "default": "./schema/dist/index.js"}, "./utils": {"require": {"types": "./utils/dist/index.d.cts", "default": "./utils/dist/index.cjs"}, "import": {"types": "./utils/dist/index.d.ts", "default": "./utils/dist/index.js"}, "default": "./utils/dist/index.js"}, "./prompts": {"require": {"types": "./prompts/dist/index.d.cts", "default": "./prompts/dist/index.cjs"}, "import": {"types": "./prompts/dist/index.d.ts", "default": "./prompts/dist/index.js"}, "default": "./prompts/dist/index.js"}, "./indices": {"require": {"types": "./indices/dist/index.d.cts", "default": "./indices/dist/index.cjs"}, "import": {"types": "./indices/dist/index.d.ts", "default": "./indices/dist/index.js"}, "default": "./indices/dist/index.js"}, "./memory": {"require": {"types": "./memory/dist/index.d.cts", "default": "./memory/dist/index.cjs"}, "import": {"types": "./memory/dist/index.d.ts", "default": "./memory/dist/index.js"}, "default": "./memory/dist/index.js"}, "./storage/chat-store": {"require": {"types": "./storage/chat-store/dist/index.d.cts", "default": "./storage/chat-store/dist/index.cjs"}, "import": {"types": "./storage/chat-store/dist/index.d.ts", "default": "./storage/chat-store/dist/index.js"}, "default": "./storage/chat-store/dist/index.js"}, "./storage/doc-store": {"require": {"types": "./storage/doc-store/dist/index.d.cts", "default": "./storage/doc-store/dist/index.cjs"}, "import": {"types": "./storage/doc-store/dist/index.d.ts", "default": "./storage/doc-store/dist/index.js"}, "default": "./storage/doc-store/dist/index.js"}, "./storage/index-store": {"require": {"types": "./storage/index-store/dist/index.d.cts", "default": "./storage/index-store/dist/index.cjs"}, "import": {"types": "./storage/index-store/dist/index.d.ts", "default": "./storage/index-store/dist/index.js"}, "default": "./storage/index-store/dist/index.js"}, "./storage/kv-store": {"require": {"types": "./storage/kv-store/dist/index.d.cts", "default": "./storage/kv-store/dist/index.cjs"}, "import": {"types": "./storage/kv-store/dist/index.d.ts", "default": "./storage/kv-store/dist/index.js"}, "default": "./storage/kv-store/dist/index.js"}, "./response-synthesizers": {"require": {"types": "./response-synthesizers/dist/index.d.cts", "default": "./response-synthesizers/dist/index.cjs"}, "import": {"types": "./response-synthesizers/dist/index.d.ts", "default": "./response-synthesizers/dist/index.js"}, "default": "./response-synthesizers/dist/index.js"}, "./chat-engine": {"require": {"types": "./chat-engine/dist/index.d.cts", "default": "./chat-engine/dist/index.cjs"}, "import": {"types": "./chat-engine/dist/index.d.ts", "default": "./chat-engine/dist/index.js"}, "default": "./chat-engine/dist/index.js"}, "./retriever": {"require": {"types": "./retriever/dist/index.d.cts", "default": "./retriever/dist/index.cjs"}, "import": {"types": "./retriever/dist/index.d.ts", "default": "./retriever/dist/index.js"}, "default": "./retriever/dist/index.js"}, "./vector-store": {"require": {"types": "./vector-store/dist/index.d.cts", "default": "./vector-store/dist/index.cjs"}, "import": {"types": "./vector-store/dist/index.d.ts", "default": "./vector-store/dist/index.js"}, "default": "./vector-store/dist/index.js"}, "./tools": {"require": {"types": "./tools/dist/index.d.cts", "default": "./tools/dist/index.cjs"}, "import": {"types": "./tools/dist/index.d.ts", "default": "./tools/dist/index.js"}, "default": "./tools/dist/index.js"}, "./data-structs": {"require": {"types": "./data-structs/dist/index.d.cts", "default": "./data-structs/dist/index.cjs"}, "import": {"types": "./data-structs/dist/index.d.ts", "default": "./data-structs/dist/index.js"}, "default": "./data-structs/dist/index.js"}, "./postprocessor": {"require": {"types": "./postprocessor/dist/index.d.cts", "default": "./postprocessor/dist/index.cjs"}, "import": {"types": "./postprocessor/dist/index.d.ts", "default": "./postprocessor/dist/index.js"}, "default": "./postprocessor/dist/index.js"}}, "files": ["./agent", "./objects", "./node-parser", "./query-engine", "./llms", "./decorator", "./embeddings", "./global", "./schema", "./utils", "./prompts", "./indices", "./workflow", "./memory", "./storage", "./response-synthesizers", "./chat-engine", "./retriever", "./vector-store", "./tools", "./data-structs", "./postprocessor"], "repository": {"type": "git", "directory": "packages/core", "url": "git+https://github.com/run-llama/LlamaIndexTS.git"}, "devDependencies": {"@edge-runtime/vm": "^4.0.4", "ajv": "^8.17.1", "happy-dom": "^15.11.6", "natural": "^8.0.1"}, "dependencies": {"@types/node": "^22.9.0", "magic-bytes.js": "^1.10.0", "zod": "^3.25.67", "zod-to-json-schema": "^3.24.6", "@llamaindex/env": "0.1.30"}, "scripts": {"dev": "bunchee --watch", "build": "bunchee"}}