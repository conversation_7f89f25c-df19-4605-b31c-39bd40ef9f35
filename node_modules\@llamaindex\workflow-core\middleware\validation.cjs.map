{"version": 3, "file": "validation.cjs", "names": ["workflow: WorkflowLike", "validation: Validation", "outputs", "wrappedHandler: Handler<WorkflowEvent<any>[], any>", "context"], "sources": ["../src/middleware/validation.ts"], "sourcesContent": ["import {\n  getContext,\n  type Handler,\n  type Workflow,\n  type WorkflowEvent,\n  type WorkflowEventData,\n} from \"@llamaindex/workflow-core\";\n\nexport type ValidationHandler<\n  Validation extends [\n    inputs: WorkflowEvent<any>[],\n    output: WorkflowEvent<any>[],\n  ][],\n  AcceptEvents extends WorkflowEvent<any>[],\n  Result extends WorkflowEventData<any> | void,\n> = (\n  sendEvent: (\n    ...inputs: Array<\n      Validation[number] extends infer Tuple\n        ? <PERSON><PERSON> extends [AcceptEvents, infer Outputs]\n          ? Outputs extends WorkflowEvent<any>[]\n            ? ReturnType<Outputs[number][\"with\"]>\n            : never\n          : never\n        : never\n    >\n  ) => void,\n  ...events: {\n    [K in keyof AcceptEvents]: ReturnType<AcceptEvents[K][\"with\"]>;\n  }\n) => Result | Promise<Result>;\n\nexport type WithValidationWorkflow<\n  Validation extends [\n    inputs: WorkflowEvent<any>[],\n    output: WorkflowEvent<any>[],\n  ][],\n> = {\n  strictHandle<\n    const AcceptEvents extends WorkflowEvent<any>[],\n    Result extends ReturnType<WorkflowEvent<any>[\"with\"]> | void,\n  >(\n    accept: AcceptEvents,\n    handler: ValidationHand<PERSON><Validation, AcceptEvents, Result>,\n  ): void;\n};\n\nexport function withValidation<\n  const Validation extends [\n    inputs: WorkflowEvent<any>[],\n    outputs: WorkflowEvent<any>[],\n  ][],\n  WorkflowLike extends Workflow,\n>(\n  workflow: WorkflowLike,\n  validation: Validation,\n): WithValidationWorkflow<Validation> & WorkflowLike {\n  const createSafeSendEvent = (...events: WorkflowEventData<any>[]) => {\n    const outputs = validation\n      .filter(([inputs]) =>\n        inputs.every((input, idx) => input.include(events[idx])),\n      )\n      .map(([_, outputs]) => outputs);\n    const store = getContext();\n    const originalSendEvent = store.sendEvent;\n    return (...inputs: WorkflowEventData<any>[]) => {\n      for (let i = 0; i < outputs.length; i++) {\n        const output = outputs[i]!;\n        if (output.length === inputs.length) {\n          if (output.every((e, idx) => e.include(inputs[idx]))) {\n            return originalSendEvent(...inputs);\n          }\n        }\n      }\n      console.warn(\n        \"Invalid input detected [%s]\",\n        inputs.map((i) => i.data).join(\", \"),\n      );\n      return originalSendEvent(...inputs);\n    };\n  };\n  return {\n    ...workflow,\n    strictHandle: (accept, handler) => {\n      const wrappedHandler: Handler<WorkflowEvent<any>[], any> = (\n        ...events\n      ) => {\n        const context = getContext();\n        return handler(\n          (context as any).safeSendEvent,\n          // @ts-expect-error\n          ...events,\n        );\n      };\n      return workflow.handle(accept, wrappedHandler);\n    },\n    createContext() {\n      const context = workflow.createContext();\n      context.__internal__call_context.subscribe((context, next) => {\n        (getContext() as any).safeSendEvent = createSafeSendEvent(\n          ...context.inputs,\n        );\n        next(context);\n      });\n      return context;\n    },\n  };\n}\n"], "mappings": ";;;;AA+CA,SAAgB,eAOdA,UACAC,YACmD;CACnD,MAAM,sBAAsB,CAAC,GAAG,WAAqC;EACnE,MAAM,UAAU,WACb,OAAO,CAAC,CAAC,OAAO,KACf,OAAO,MAAM,CAAC,OAAO,QAAQ,MAAM,QAAQ,OAAO,KAAK,CAAC,CACzD,CACA,IAAI,CAAC,CAAC,GAAGC,UAAQ,KAAKA,UAAQ;EACjC,MAAM,QAAQ,4CAAY;EAC1B,MAAM,oBAAoB,MAAM;AAChC,SAAO,CAAC,GAAG,WAAqC;AAC9C,QAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;IACvC,MAAM,SAAS,QAAQ;AACvB,QAAI,OAAO,WAAW,OAAO,QAC3B;SAAI,OAAO,MAAM,CAAC,GAAG,QAAQ,EAAE,QAAQ,OAAO,KAAK,CAAC,CAClD,QAAO,kBAAkB,GAAG,OAAO;IACpC;GAEJ;AACD,WAAQ,KACN,+BACA,OAAO,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,KAAK,CACrC;AACD,UAAO,kBAAkB,GAAG,OAAO;EACpC;CACF;AACD,QAAO;EACL,GAAG;EACH,cAAc,CAAC,QAAQ,YAAY;GACjC,MAAMC,iBAAqD,CACzD,GAAG,WACA;IACH,MAAM,UAAU,4CAAY;AAC5B,WAAO,QACJ,QAAgB,eAEjB,GAAG,OACJ;GACF;AACD,UAAO,SAAS,OAAO,QAAQ,eAAe;EAC/C;EACD,gBAAgB;GACd,MAAM,UAAU,SAAS,eAAe;AACxC,WAAQ,yBAAyB,UAAU,CAACC,WAAS,SAAS;AAC5D,IAAC,4CAAY,CAAS,gBAAgB,oBACpC,GAAGA,UAAQ,OACZ;AACD,SAAKA,UAAQ;GACd,EAAC;AACF,UAAO;EACR;CACF;AACF"}