//#region src/stream/run.ts
/**
* Runs a workflow with the provided events and returns the resulting stream.
*
* ```ts
* const events = await run(workflow, startEvent.with("42")).toArray();
* ```
*
*/
function run(workflow, events) {
	const { stream, sendEvent } = workflow.createContext();
	sendEvent(...Array.isArray(events) ? events : [events]);
	return stream;
}
/**
* Runs a workflow with a specified input event and returns the first matching event of the specified output type.
*
* @deprecated Use `stream.until().toArray()` for a more idiomatic approach.
* @example
* ```ts
* const result = await runWorkflow(workflow, startEvent.with("42"), stopEvent);
* console.log(`Result: ${result.data === 1 ? 'positive' : 'negative'}`);
* ```
*/
async function runWorkflow(workflow, inputEvent, outputEvent) {
	const { stream, sendEvent } = workflow.createContext();
	sendEvent(inputEvent);
	const result = (await stream.until(outputEvent).toArray()).at(-1);
	if (!result) throw new Error("No output event received");
	return result;
}

//#endregion
export { run, runWorkflow };
//# sourceMappingURL=run-25vavBMH.js.map