{"version": 3, "file": "trace-events.cjs", "names": ["config: {\n  debugLabel?: string;\n  getInitialValue: () => Metadata;\n  onBeforeHandler: (\n    handler: <PERSON>ler<WorkflowEvent<any>[], WorkflowEventData<any> | void>,\n    handlerContext: HandlerContext,\n    metadata: Metadata,\n  ) => Handler<WorkflowEvent<any>[], WorkflowEventData<any> | void>;\n  onAfterHandler: (metadata: Metadata) => Metadata;\n}", "handler: Fn", "noop: (...args: any[]) => void", "noop", "eventData: WorkflowEventData<any>", "workflow: WorkflowLike", "stream: WorkflowStream<T>", "eventData", "accept: AcceptEvents", "handler: Fn", "handlerMiddleware: <PERSON><PERSON><\n          WorkflowEvent<any>[],\n          WorkflowEventData<any> | void\n        >", "tracingContext: TracingContext", "context", "next", "result"], "sources": ["../src/middleware/trace-events/create-handler-decorator.ts", "../src/middleware/trace-events/run-once.ts", "../src/middleware/trace-events.ts"], "sourcesContent": ["import type {\n  Handler,\n  WorkflowEvent,\n  WorkflowEventData,\n} from \"@llamaindex/workflow-core\";\nimport type { HandlerContext } from \"../../core/context\";\n\nconst namespace = \"decorator\" as const;\n\nlet counter = 0;\n\nexport const decoratorRegistry = new Map<\n  string,\n  {\n    handlers: WeakSet<\n      Handler<WorkflowEvent<any>[], WorkflowEventData<any> | void>\n    >;\n    debugLabel: string;\n    getInitialValue: () => any;\n    onBeforeHandler: (\n      handler: Handler<WorkflowEvent<any>[], WorkflowEventData<any> | void>,\n      handlerContext: Readonly<HandlerContext>,\n      metadata: any,\n    ) => Handler<WorkflowEvent<any>[], WorkflowEventData<any> | void>;\n    onAfterHandler: (metadata: any) => any;\n  }\n>();\n\nexport function createHandlerDecorator<Metadata>(config: {\n  debugLabel?: string;\n  getInitialValue: () => Metadata;\n  onBeforeHandler: (\n    handler: <PERSON>ler<WorkflowEvent<any>[], WorkflowEventData<any> | void>,\n    handlerContext: HandlerContext,\n    metadata: Metadata,\n  ) => Handler<WorkflowEvent<any>[], WorkflowEventData<any> | void>;\n  onAfterHandler: (metadata: Metadata) => Metadata;\n}) {\n  const uid = `${namespace}:${counter++}`;\n  decoratorRegistry.set(uid, {\n    handlers: new WeakSet(),\n    debugLabel: config.debugLabel ?? uid,\n    getInitialValue: config.getInitialValue,\n    onAfterHandler: config.onAfterHandler,\n    onBeforeHandler: config.onBeforeHandler,\n  });\n  return function <\n    const AcceptEvents extends WorkflowEvent<any>[],\n    Result extends ReturnType<WorkflowEvent<any>[\"with\"]> | void,\n    Fn extends Handler<AcceptEvents, Result>,\n  >(handler: Fn) {\n    decoratorRegistry\n      .get(uid)!\n      .handlers.add(\n        handler as Handler<WorkflowEvent<any>[], WorkflowEventData<any> | void>,\n      );\n    return handler;\n  };\n}\n", "import { createHandlerDecorator } from \"./create-handler-decorator\";\n\nconst noop: (...args: any[]) => void = function noop() {};\n\nexport const runOnce = createHandlerDecorator({\n  debugLabel: \"onceHook\",\n  getInitialValue: () => false,\n  onBeforeHandler: (handler, _, tracked) => (tracked ? noop : handler),\n  onAfterHandler: () => true,\n});\n", "import {\n  getContext,\n  type Handler,\n  type WorkflowContext,\n  type WorkflowEvent,\n  type WorkflowEventData,\n  WorkflowStream,\n} from \"@llamaindex/workflow-core\";\nimport { isPromiseLike } from \"../core/utils\";\nimport {\n  createHandlerDecorator,\n  decoratorRegistry,\n} from \"./trace-events/create-handler-decorator\";\nimport { runOnce } from \"./trace-events/run-once\";\nimport type { HandlerContext } from \"../core/context\";\n\ntype TracingContext = Record<string, unknown>;\n\nconst tracingWeakMap = new WeakMap<\n  WorkflowContext,\n  WeakMap<\n    WorkflowEvent<any>[],\n    WeakMap<\n      Handler<WorkflowEvent<any>[], WorkflowEventData<any> | void>,\n      TracingContext\n    >\n  >\n>();\n\nconst contextTraceWeakMap = new WeakMap<HandlerContext, WorkflowContext>();\n\nconst eventToHandlerContextWeakMap = new WeakMap<\n  WorkflowEventData<any>,\n  HandlerContext\n>();\n\nexport function getEventOrigins(\n  eventData: WorkflowEventData<any>,\n  context = getContext(),\n): [WorkflowEventData<any>, ...WorkflowEventData<any>[]] {\n  let currentContext = eventToHandlerContextWeakMap.get(eventData);\n  if (!currentContext) {\n    throw new Error(\n      \"Event context not found, this should not happen. Please report this issue with a reproducible example.\",\n    );\n  }\n  do {\n    const workflowContext = contextTraceWeakMap.get(currentContext.prev)!;\n    if (workflowContext === context) {\n      return currentContext.inputs as [\n        WorkflowEventData<any>,\n        ...WorkflowEventData<any>[],\n      ];\n    }\n\n    currentContext = currentContext.prev;\n  } while (currentContext.prev);\n  throw new Error(\n    \"Event context not found, this should not happen. Please report this issue with a reproducible example.\",\n  );\n}\n\nexport type HandlerRef<\n  AcceptEvents extends WorkflowEvent<any>[],\n  Result extends ReturnType<WorkflowEvent<any>[\"with\"]> | void,\n  Fn extends Handler<AcceptEvents, Result>,\n> = {\n  get handler(): Fn;\n};\n\nexport function withTraceEvents<\n  WorkflowLike extends {\n    handle<\n      const AcceptEvents extends WorkflowEvent<any>[],\n      Result extends ReturnType<WorkflowEvent<any>[\"with\"]> | void,\n    >(\n      accept: AcceptEvents,\n      handler: Handler<AcceptEvents, Result>,\n    ): void;\n    createContext(): WorkflowContext;\n  },\n>(\n  workflow: WorkflowLike,\n): Omit<WorkflowLike, \"handle\"> & {\n  handle<\n    const AcceptEvents extends WorkflowEvent<any>[],\n    Result extends ReturnType<WorkflowEvent<any>[\"with\"]> | void,\n    Fn extends Handler<AcceptEvents, Result>,\n  >(\n    accept: AcceptEvents,\n    handler: Fn,\n  ): HandlerRef<AcceptEvents, Result, Fn>;\n  substream<T extends WorkflowEventData<any>>(\n    eventData: WorkflowEventData<any>,\n    stream: WorkflowStream<T>,\n  ): WorkflowStream<T>;\n} {\n  return {\n    ...workflow,\n    substream: <T extends WorkflowEventData<any>>(\n      eventData: WorkflowEventData<any>,\n      stream: WorkflowStream<T>,\n    ): WorkflowStream<T> => {\n      const rootContext = eventToHandlerContextWeakMap.get(eventData);\n      return stream.pipeThrough(\n        new TransformStream({\n          transform(eventData, controller) {\n            let isInSameContext = false;\n            let currentEventContext =\n              eventToHandlerContextWeakMap.get(eventData);\n            while (currentEventContext) {\n              if (currentEventContext === rootContext) {\n                isInSameContext = true;\n                break;\n              }\n              currentEventContext = currentEventContext.prev;\n            }\n            if (isInSameContext) {\n              controller.enqueue(eventData);\n            }\n          },\n        }),\n      );\n    },\n    handle: <\n      const AcceptEvents extends WorkflowEvent<any>[],\n      Result extends ReturnType<WorkflowEvent<any>[\"with\"]> | void,\n      Fn extends Handler<AcceptEvents, Result>,\n    >(\n      accept: AcceptEvents,\n      handler: Fn,\n    ): HandlerRef<AcceptEvents, Result, Fn> => {\n      workflow.handle(accept, handler);\n      return {\n        get handler(): Fn {\n          return handler;\n        },\n      };\n    },\n    createContext(): WorkflowContext {\n      const context = workflow.createContext();\n      tracingWeakMap.set(context, new WeakMap());\n      context.__internal__call_send_event.subscribe((event, handlerContext) => {\n        eventToHandlerContextWeakMap.set(event, handlerContext);\n      });\n      context.__internal__call_context.subscribe((handlerContext, next) => {\n        handlerContext.inputs.forEach((input) => {\n          if (!eventToHandlerContextWeakMap.has(input)) {\n            console.warn(\"unregistered event detected\");\n          }\n          eventToHandlerContextWeakMap.set(input, handlerContext);\n        });\n        const inputEvents = handlerContext.inputEvents;\n        const handlersWeakMap = tracingWeakMap.get(context)!;\n        if (!handlersWeakMap.has(inputEvents)) {\n          handlersWeakMap.set(inputEvents, new WeakMap());\n        }\n        const handlerWeakMap = handlersWeakMap.get(inputEvents)!;\n\n        const originalHandler = handlerContext.handler;\n        let finalHandler = originalHandler;\n        let handlerMiddleware: Handler<\n          WorkflowEvent<any>[],\n          WorkflowEventData<any> | void\n        >;\n        if (!handlerWeakMap) {\n          throw new Error(\n            \"Handler context is not defined, this should not happen. Please report this issue with a reproducible example.\",\n          );\n        }\n        const tracingContext: TracingContext =\n          handlerWeakMap.get(originalHandler) ?? {};\n        if (!handlerWeakMap.has(originalHandler)) {\n          handlerWeakMap.set(originalHandler, tracingContext);\n        }\n\n        const onAfterHandlers = [] as (() => void)[];\n        const onBeforeHandlers = [] as ((\n          nextHandler: Handler<\n            WorkflowEvent<any>[],\n            WorkflowEventData<any> | void\n          >,\n        ) => Handler<WorkflowEvent<any>[], WorkflowEventData<any> | void>)[];\n        handlerMiddleware = (...args) => {\n          const context = getContext();\n          contextTraceWeakMap.set(handlerContext, context);\n          const result = onBeforeHandlers.reduce((next, cb) => {\n            return cb(next);\n          }, finalHandler)(...args);\n          if (isPromiseLike(result)) {\n            return result.then((result) => {\n              onAfterHandlers.forEach((cb) => {\n                cb();\n              });\n              return result;\n            });\n          } else {\n            onAfterHandlers.forEach((cb) => {\n              cb();\n            });\n            return result;\n          }\n        };\n        [...decoratorRegistry]\n          .filter(([, { handlers }]) =>\n            handlers.has(\n              handlerContext.handler as Handler<\n                WorkflowEvent<any>[],\n                WorkflowEventData<any> | void\n              >,\n            ),\n          )\n          .forEach(\n            ([name, { getInitialValue, onAfterHandler, onBeforeHandler }]) => {\n              if (!tracingContext[name]) {\n                tracingContext[name] = getInitialValue();\n              }\n              onBeforeHandlers.push((next) =>\n                onBeforeHandler(next, handlerContext, tracingContext[name]),\n              );\n              onAfterHandlers.push(() => {\n                tracingContext[name] = onAfterHandler(tracingContext[name]);\n              });\n            },\n          );\n        next({\n          ...handlerContext,\n          handler: handlerMiddleware,\n        });\n      });\n      return context;\n    },\n  };\n}\n\nexport { createHandlerDecorator, runOnce };\n"], "mappings": ";;;;;AAOA,MAAM,YAAY;AAElB,IAAI,UAAU;AAEd,MAAa,oCAAoB,IAAI;AAiBrC,SAAgB,uBAAiCA,QAS9C;CACD,MAAM,SAAS,UAAU,GAAG;AAC5B,mBAAkB,IAAI,KAAK;EACzB,0BAAU,IAAI;EACd,YAAY,OAAO,cAAc;EACjC,iBAAiB,OAAO;EACxB,gBAAgB,OAAO;EACvB,iBAAiB,OAAO;CACzB,EAAC;AACF,QAAO,SAILC,SAAa;AACb,oBACG,IAAI,IAAI,CACR,SAAS,IACR,QACD;AACH,SAAO;CACR;AACF;;;;ACxDD,MAAMC,OAAiC,SAASC,SAAO,CAAE;AAEzD,MAAa,UAAU,uBAAuB;CAC5C,YAAY;CACZ,iBAAiB,MAAM;CACvB,iBAAiB,CAAC,SAAS,GAAG,YAAa,UAAU,OAAO;CAC5D,gBAAgB,MAAM;AACvB,EAAC;;;;ACSF,MAAM,iCAAiB,IAAI;AAW3B,MAAM,sCAAsB,IAAI;AAEhC,MAAM,+CAA+B,IAAI;AAKzC,SAAgB,gBACdC,WACA,UAAU,4CAAY,EACiC;CACvD,IAAI,iBAAiB,6BAA6B,IAAI,UAAU;AAChE,MAAK,eACH,OAAM,IAAI,MACR;AAGJ,IAAG;EACD,MAAM,kBAAkB,oBAAoB,IAAI,eAAe,KAAK;AACpE,MAAI,oBAAoB,QACtB,QAAO,eAAe;AAMxB,mBAAiB,eAAe;CACjC,SAAQ,eAAe;AACxB,OAAM,IAAI,MACR;AAEH;AAUD,SAAgB,gBAYdC,UAcA;AACA,QAAO;EACL,GAAG;EACH,WAAW,CACTD,WACAE,WACsB;GACtB,MAAM,cAAc,6BAA6B,IAAI,UAAU;AAC/D,UAAO,OAAO,YACZ,IAAI,gBAAgB,EAClB,UAAUC,aAAW,YAAY;IAC/B,IAAI,kBAAkB;IACtB,IAAI,sBACF,6BAA6B,IAAIA,YAAU;AAC7C,WAAO,qBAAqB;AAC1B,SAAI,wBAAwB,aAAa;AACvC,wBAAkB;AAClB;KACD;AACD,2BAAsB,oBAAoB;IAC3C;AACD,QAAI,gBACF,YAAW,QAAQA,YAAU;GAEhC,EACF,GACF;EACF;EACD,QAAQ,CAKNC,QACAC,YACyC;AACzC,YAAS,OAAO,QAAQ,QAAQ;AAChC,UAAO,EACL,IAAI,UAAc;AAChB,WAAO;GACR,EACF;EACF;EACD,gBAAiC;GAC/B,MAAM,UAAU,SAAS,eAAe;AACxC,kBAAe,IAAI,yBAAS,IAAI,UAAU;AAC1C,WAAQ,4BAA4B,UAAU,CAAC,OAAO,mBAAmB;AACvE,iCAA6B,IAAI,OAAO,eAAe;GACxD,EAAC;AACF,WAAQ,yBAAyB,UAAU,CAAC,gBAAgB,SAAS;AACnE,mBAAe,OAAO,QAAQ,CAAC,UAAU;AACvC,UAAK,6BAA6B,IAAI,MAAM,CAC1C,SAAQ,KAAK,8BAA8B;AAE7C,kCAA6B,IAAI,OAAO,eAAe;IACxD,EAAC;IACF,MAAM,cAAc,eAAe;IACnC,MAAM,kBAAkB,eAAe,IAAI,QAAQ;AACnD,SAAK,gBAAgB,IAAI,YAAY,CACnC,iBAAgB,IAAI,6BAAa,IAAI,UAAU;IAEjD,MAAM,iBAAiB,gBAAgB,IAAI,YAAY;IAEvD,MAAM,kBAAkB,eAAe;IACvC,IAAI,eAAe;IACnB,IAAIC;AAIJ,SAAK,eACH,OAAM,IAAI,MACR;IAGJ,MAAMC,iBACJ,eAAe,IAAI,gBAAgB,IAAI,CAAE;AAC3C,SAAK,eAAe,IAAI,gBAAgB,CACtC,gBAAe,IAAI,iBAAiB,eAAe;IAGrD,MAAM,kBAAkB,CAAE;IAC1B,MAAM,mBAAmB,CAAE;AAM3B,wBAAoB,CAAC,GAAG,SAAS;KAC/B,MAAMC,YAAU,4CAAY;AAC5B,yBAAoB,IAAI,gBAAgBA,UAAQ;KAChD,MAAM,SAAS,iBAAiB,OAAO,CAACC,QAAM,OAAO;AACnD,aAAO,GAAGA,OAAK;KAChB,GAAE,aAAa,CAAC,GAAG,KAAK;AACzB,SAAI,4BAAc,OAAO,CACvB,QAAO,OAAO,KAAK,CAACC,aAAW;AAC7B,sBAAgB,QAAQ,CAAC,OAAO;AAC9B,WAAI;MACL,EAAC;AACF,aAAOA;KACR,EAAC;UACG;AACL,sBAAgB,QAAQ,CAAC,OAAO;AAC9B,WAAI;MACL,EAAC;AACF,aAAO;KACR;IACF;AACD,KAAC,GAAG,iBAAkB,EACnB,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,KACvB,SAAS,IACP,eAAe,QAIhB,CACF,CACA,QACC,CAAC,CAAC,MAAM,EAAE,iBAAiB,gBAAgB,iBAAiB,CAAC,KAAK;AAChE,UAAK,eAAe,MAClB,gBAAe,QAAQ,iBAAiB;AAE1C,sBAAiB,KAAK,CAACD,WACrB,gBAAgBA,QAAM,gBAAgB,eAAe,MAAM,CAC5D;AACD,qBAAgB,KAAK,MAAM;AACzB,qBAAe,QAAQ,eAAe,eAAe,MAAM;KAC5D,EAAC;IACH,EACF;AACH,SAAK;KACH,GAAG;KACH,SAAS;IACV,EAAC;GACH,EAAC;AACF,UAAO;EACR;CACF;AACF"}