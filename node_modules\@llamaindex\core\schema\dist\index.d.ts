import { ChatResponse, ChatResponseChunk, ChatMessage } from '../../llms/dist/index.js';
import { z } from 'zod';

declare enum NodeRelationship {
    SOURCE = "SOURCE",
    PREVIOUS = "PREVIOUS",
    NEXT = "NEXT",
    PARENT = "PARENT",
    CHILD = "CHILD"
}
declare enum ObjectType {
    TEXT = "TEXT",
    IMAGE = "IMAGE",
    INDEX = "INDEX",
    DOCUMENT = "DOCUMENT",
    IMAGE_DOCUMENT = "IMAGE_DOCUMENT"
}
declare enum MetadataMode {
    ALL = "ALL",
    EMBED = "EMBED",
    LLM = "LLM",
    NONE = "NONE"
}
type Metadata = Record<string, any>;
interface RelatedNodeInfo<T extends Metadata = Metadata> {
    nodeId: string;
    nodeType?: ObjectType;
    metadata: T;
    hash?: string;
}
type RelatedNodeType<T extends Metadata = Metadata> = RelatedNodeInfo<T> | RelatedNodeInfo<T>[];
type BaseNodeParams<T extends Metadata = Metadata> = {
    id_?: string | undefined;
    metadata?: T | undefined;
    excludedEmbedMetadataKeys?: string[] | undefined;
    excludedLlmMetadataKeys?: string[] | undefined;
    relationships?: Partial<Record<NodeRelationship, RelatedNodeType<T>>> | undefined;
    hash?: string | undefined;
    embedding?: number[] | undefined;
};
/**
 * Generic abstract class for retrievable nodes
 */
declare abstract class BaseNode<T extends Metadata = Metadata> {
    /**
     * The unique ID of the Node/Document. The trailing underscore is here
     * to avoid collisions with the id keyword in Python.
     *
     * Set to a UUID by default.
     */
    id_: string;
    embedding: number[] | undefined;
    metadata: T;
    excludedEmbedMetadataKeys: string[];
    excludedLlmMetadataKeys: string[];
    relationships: Partial<Record<NodeRelationship, RelatedNodeType<T>>>;
    accessor hash: string;
    protected constructor(init?: BaseNodeParams<T>);
    abstract get type(): ObjectType;
    abstract getContent(metadataMode: MetadataMode): string;
    abstract getMetadataStr(metadataMode: MetadataMode): string;
    abstract setContent(value: unknown): void;
    get sourceNode(): RelatedNodeInfo<T> | undefined;
    get prevNode(): RelatedNodeInfo<T> | undefined;
    get nextNode(): RelatedNodeInfo<T> | undefined;
    get parentNode(): RelatedNodeInfo<T> | undefined;
    get childNodes(): RelatedNodeInfo<T>[] | undefined;
    abstract generateHash(): string;
    getEmbedding(): number[];
    asRelatedNodeInfo(): RelatedNodeInfo<T>;
    /**
     * Called by built in JSON.stringify (see https://javascript.info/json)
     * Properties are read-only as they are not deep-cloned (not necessary for stringification).
     * @see toMutableJSON - use to return a mutable JSON instead
     */
    toJSON(): Record<string, any>;
    clone(): BaseNode;
    /**
     * Converts the object to a JSON representation.
     * Properties can be safely modified as a deep clone of the properties are created.
     * @return {Record<string, any>} - The JSON representation of the object.
     */
    toMutableJSON(): Record<string, any>;
}
type TextNodeParams<T extends Metadata = Metadata> = BaseNodeParams<T> & {
    text?: string | undefined;
    textTemplate?: string | undefined;
    startCharIdx?: number | undefined;
    endCharIdx?: number | undefined;
    metadataSeparator?: string | undefined;
};
/**
 * TextNode is the default node type for text. Most common node type in LlamaIndex.TS
 */
declare class TextNode<T extends Metadata = Metadata> extends BaseNode<T> {
    text: string;
    textTemplate: string;
    startCharIdx?: number;
    endCharIdx?: number;
    metadataSeparator: string;
    constructor(init?: TextNodeParams<T>);
    /**
     * Generate a hash of the text node.
     * The ID is not part of the hash as it can change independent of content.
     * @returns
     */
    generateHash(): string;
    get type(): ObjectType;
    getContent(metadataMode?: MetadataMode): string;
    getMetadataStr(metadataMode: MetadataMode): string;
    setContent(value: string): void;
    getNodeInfo(): {
        start: number | undefined;
        end: number | undefined;
    };
    getText(): string;
}
type IndexNodeParams<T extends Metadata = Metadata> = TextNodeParams<T> & {
    indexId: string;
};
declare class IndexNode<T extends Metadata = Metadata> extends TextNode<T> {
    indexId: string;
    constructor(init?: IndexNodeParams<T>);
    get type(): ObjectType;
}
/**
 * A document is just a special text node with a docId.
 */
declare class Document<T extends Metadata = Metadata> extends TextNode<T> {
    constructor(init?: TextNodeParams<T>);
    get type(): ObjectType;
}
declare function jsonToNode(json: any, type?: ObjectType): TextNode<Metadata>;
type ImageType = string | Blob | URL;
type ImageNodeParams<T extends Metadata = Metadata> = TextNodeParams<T> & {
    image: ImageType;
};
declare class ImageNode<T extends Metadata = Metadata> extends TextNode<T> {
    image: ImageType;
    constructor(init: ImageNodeParams<T>);
    get type(): ObjectType;
    getUrl(): URL;
    private generateImageHash;
    generateHash(): string;
}
declare class ImageDocument<T extends Metadata = Metadata> extends ImageNode<T> {
    constructor(init: ImageNodeParams<T>);
    get type(): ObjectType;
}
/**
 * A node with a similarity score
 */
interface NodeWithScore<T extends Metadata = Metadata> {
    node: BaseNode<T>;
    score?: number | undefined;
}
declare enum ModalityType {
    TEXT = "TEXT",
    IMAGE = "IMAGE",
    AUDIO = "AUDIO"
}
type NodesByType = {
    [P in ModalityType]?: BaseNode[];
};
declare function splitNodesByType(nodes: BaseNode[]): NodesByType;
declare function buildNodeFromSplits(textSplits: string[], doc: BaseNode, refDoc?: BaseNode, idGenerator?: (idx: number, refDoc: BaseNode) => string): TextNode[];

type StoredValue = Record<string, any> | null;
interface TransformComponentSignature<Result extends BaseNode[] | Promise<BaseNode[]>> {
    <Options extends Record<string, unknown>>(nodes: BaseNode[], options?: Options): Result;
}
interface TransformComponent<Result extends BaseNode[] | Promise<BaseNode[]> = BaseNode[] | Promise<BaseNode[]>> extends TransformComponentSignature<Result> {
    id: string;
}
declare class TransformComponent<Result extends BaseNode[] | Promise<BaseNode[]> = BaseNode[] | Promise<BaseNode[]>> {
    constructor(transformFn: TransformComponentSignature<Result>);
}
/**
 * A reader takes imports data into Document objects.
 */
interface BaseReader<T extends BaseNode = Document> {
    loadData(...args: unknown[]): Promise<T[]>;
}
/**
 * A FileReader takes file paths and imports data into Document objects.
 */
declare abstract class FileReader<T extends BaseNode = Document> implements BaseReader<T> {
    abstract loadDataAsContent(fileContent: Uint8Array, filename?: string): Promise<T[]>;
    loadData(filePath: string): Promise<T[]>;
    static addMetaData(filePath: string): (doc: BaseNode, index: number) => void;
}

/**
 * An OutputParser is used to extract structured data from the raw output of the LLM.
 */
interface BaseOutputParser<T = any> {
    parse(output: string): T;
    format(output: string): string;
}

declare class EngineResponse implements ChatResponse, ChatResponseChunk {
    sourceNodes: NodeWithScore[] | undefined;
    metadata: Metadata;
    message: ChatMessage;
    raw: object | null;
    readonly stream: boolean;
    private constructor();
    static fromResponse(response: string, stream: boolean, sourceNodes: NodeWithScore[]): EngineResponse;
    private static toChatResponse;
    static fromChatResponse(chatResponse: ChatResponse, sourceNodes?: NodeWithScore[]): EngineResponse;
    static fromChatResponseChunk(chunk: ChatResponseChunk, sourceNodes?: NodeWithScore[]): EngineResponse;
    /**
     * @deprecated Use `message` instead.
     */
    get response(): string;
    get delta(): string;
    toString(): string;
}

declare const anyFunctionSchema: z.ZodFunction<z.ZodTuple<[], z.ZodAny>, z.ZodAny>;
declare const toolMetadataSchema: z.ZodObject<{
    description: z.ZodString;
    name: z.ZodString;
    parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
}, "strip", z.ZodTypeAny, {
    description: string;
    name: string;
    parameters: Record<string, any>;
}, {
    description: string;
    name: string;
    parameters: Record<string, any>;
}>;
declare const baseToolSchema: z.ZodObject<{
    call: z.ZodOptional<z.ZodFunction<z.ZodTuple<[], z.ZodAny>, z.ZodAny>>;
    metadata: z.ZodObject<{
        description: z.ZodString;
        name: z.ZodString;
        parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
    }, "strip", z.ZodTypeAny, {
        description: string;
        name: string;
        parameters: Record<string, any>;
    }, {
        description: string;
        name: string;
        parameters: Record<string, any>;
    }>;
}, "strip", z.ZodTypeAny, {
    metadata: {
        description: string;
        name: string;
        parameters: Record<string, any>;
    };
    call?: ((...args: any[]) => any) | undefined;
}, {
    metadata: {
        description: string;
        name: string;
        parameters: Record<string, any>;
    };
    call?: ((...args: any[]) => any) | undefined;
}>;
declare const baseToolWithCallSchema: z.ZodObject<{
    metadata: z.ZodObject<{
        description: z.ZodString;
        name: z.ZodString;
        parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
    }, "strip", z.ZodTypeAny, {
        description: string;
        name: string;
        parameters: Record<string, any>;
    }, {
        description: string;
        name: string;
        parameters: Record<string, any>;
    }>;
} & {
    call: z.ZodFunction<z.ZodTuple<[], z.ZodUnknown>, z.ZodUnknown>;
}, "strip", z.ZodTypeAny, {
    metadata: {
        description: string;
        name: string;
        parameters: Record<string, any>;
    };
    call: (...args: unknown[]) => unknown;
}, {
    metadata: {
        description: string;
        name: string;
        parameters: Record<string, any>;
    };
    call: (...args: unknown[]) => unknown;
}>;
declare const sentenceSplitterSchema: z.ZodEffects<z.ZodObject<{
    chunkSize: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    chunkOverlap: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    separator: z.ZodDefault<z.ZodString>;
    paragraphSeparator: z.ZodDefault<z.ZodOptional<z.ZodString>>;
    secondaryChunkingRegex: z.ZodDefault<z.ZodOptional<z.ZodString>>;
    extraAbbreviations: z.ZodDefault<z.ZodOptional<z.ZodArray<z.ZodString, "many">>>;
}, "strip", z.ZodTypeAny, {
    chunkSize: number;
    chunkOverlap: number;
    separator: string;
    paragraphSeparator: string;
    secondaryChunkingRegex: string;
    extraAbbreviations: string[];
}, {
    chunkSize?: number | undefined;
    chunkOverlap?: number | undefined;
    separator?: string | undefined;
    paragraphSeparator?: string | undefined;
    secondaryChunkingRegex?: string | undefined;
    extraAbbreviations?: string[] | undefined;
}>, {
    chunkSize: number;
    chunkOverlap: number;
    separator: string;
    paragraphSeparator: string;
    secondaryChunkingRegex: string;
    extraAbbreviations: string[];
}, {
    chunkSize?: number | undefined;
    chunkOverlap?: number | undefined;
    separator?: string | undefined;
    paragraphSeparator?: string | undefined;
    secondaryChunkingRegex?: string | undefined;
    extraAbbreviations?: string[] | undefined;
}>;
declare const sentenceWindowNodeParserSchema: z.ZodObject<{
    windowSize: z.ZodDefault<z.ZodNumber>;
    windowMetadataKey: z.ZodDefault<z.ZodString>;
    originalTextMetadataKey: z.ZodDefault<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    windowSize: number;
    windowMetadataKey: string;
    originalTextMetadataKey: string;
}, {
    windowSize?: number | undefined;
    windowMetadataKey?: string | undefined;
    originalTextMetadataKey?: string | undefined;
}>;

export { BaseNode, type BaseNodeParams, type BaseOutputParser, type BaseReader, Document, EngineResponse, FileReader, ImageDocument, ImageNode, type ImageNodeParams, type ImageType, IndexNode, type IndexNodeParams, type Metadata, MetadataMode, ModalityType, NodeRelationship, type NodeWithScore, ObjectType, type RelatedNodeInfo, type RelatedNodeType, type StoredValue, TextNode, type TextNodeParams, TransformComponent, anyFunctionSchema, baseToolSchema, baseToolWithCallSchema, buildNodeFromSplits, jsonToNode, sentenceSplitterSchema, sentenceWindowNodeParserSchema, splitNodesByType, toolMetadataSchema };
