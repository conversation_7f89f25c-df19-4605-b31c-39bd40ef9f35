import { WorkflowStream } from "@llamaindex/workflow-core";

//#region src/stream/find.ts
/**
* Consume a stream of events with a given event and time.
*/
async function find(stream, event) {
	const reader = stream.getReader();
	let result = await reader.read();
	while (!result.done) {
		const ev = result.value;
		if (event.include(ev)) {
			reader.releaseLock();
			return ev;
		}
		result = await reader.read();
	}
	throw new Error(`Event ${event.toString()} not found`);
}

//#endregion
export { find };
//# sourceMappingURL=find.js.map