{"version": 3, "file": "run.js", "names": ["workflow: Workflow", "events: WorkflowEventData<any> | WorkflowEventData<any>[]", "inputEvent: WorkflowEventData<Input>", "outputEvent: WorkflowEvent<Output>"], "sources": ["../src/stream/run.ts"], "sourcesContent": ["import type {\n  Workflow,\n  WorkflowEvent,\n  WorkflowEventData,\n} from \"@llama-flow/core\";\n\n/**\n * Runs a workflow with the provided events and returns the resulting stream.\n *\n * ```ts\n * const events = await run(workflow, startEvent.with(\"42\")).toArray();\n * ```\n *\n */\nexport function run(\n  workflow: Workflow,\n  events: WorkflowEventData<any> | WorkflowEventData<any>[],\n) {\n  const { stream, sendEvent } = workflow.createContext();\n  sendEvent(...(Array.isArray(events) ? events : [events]));\n  return stream;\n}\n\n/**\n * Runs a workflow with a specified input event and returns the first matching event of the specified output type.\n *\n * @deprecated Use `stream.until().toArray()` for a more idiomatic approach.\n * @example\n * ```ts\n * const result = await runWorkflow(workflow, startEvent.with(\"42\"), stopEvent);\n * console.log(`Result: ${result.data === 1 ? 'positive' : 'negative'}`);\n * ```\n */\nexport async function runWorkflow<Input, Output>(\n  workflow: Workflow,\n  inputEvent: WorkflowEventData<Input>,\n  outputEvent: WorkflowEvent<Output>,\n): Promise<WorkflowEventData<Output>> {\n  const { stream, sendEvent } = workflow.createContext();\n\n  // Send the initial event\n  sendEvent(inputEvent);\n\n  // Create a stream until we get the output event\n  const result = (await stream.until(outputEvent).toArray()).at(-1);\n  if (!result) {\n    throw new Error(\"No output event received\");\n  }\n  return result as WorkflowEventData<Output>;\n}\n\n/**\n * Runs a workflow with a specified input event and collects all events until a specified output event is encountered.\n * Returns an array containing all events including the final output event.\n *\n * @deprecated Use `stream.until().toArray()` for a more idiomatic approach.\n * @example\n * ```ts\n * const allEvents = await runAndCollect(workflow, startEvent.with(\"42\"), stopEvent);\n * const finalEvent = allEvents[allEvents.length - 1];\n * console.log(`Result: ${finalEvent.data === 1 ? 'positive' : 'negative'}`);\n * ```\n */\nexport async function runAndCollect<Input, Output>(\n  workflow: Workflow,\n  inputEvent: WorkflowEventData<Input>,\n  outputEvent: WorkflowEvent<Output>,\n): Promise<WorkflowEventData<any>[]> {\n  const { stream, sendEvent } = workflow.createContext();\n\n  // Send the initial event\n  sendEvent(inputEvent);\n\n  // Collect all events until the output event\n  return await stream.until(outputEvent).toArray();\n}\n\n/**\n * Runs a workflow with a specified input event and returns an async iterable stream of all events\n * until a specified output event is encountered.\n *\n * This allows processing events one by one without collecting them all upfront.\n *\n * @deprecated Use `stream.until().toArray()` for a more idiomatic approach.\n * @example\n * ```ts\n * const eventStream = runStream(workflow, startEvent.with(\"42\"), stopEvent);\n * for await (const event of eventStream) {\n *   console.log(`Processing event: ${event}`);\n *   // Do something with each event as it arrives\n * }\n * ```\n */\nexport function runStream<Input, Output>(\n  workflow: Workflow,\n  inputEvent: WorkflowEventData<Input>,\n  outputEvent: WorkflowEvent<Output>,\n): AsyncIterable<WorkflowEventData<any>> {\n  const { stream, sendEvent } = workflow.createContext();\n\n  // Send the initial event\n  sendEvent(inputEvent);\n\n  // Return the stream that runs until the output event is encountered\n  return stream.until(outputEvent).values();\n}\n"], "mappings": ";;;;;;;;;AAcA,SAAgB,IACdA,UACAC,QACA;CACA,MAAM,EAAE,QAAQ,WAAW,GAAG,SAAS,eAAe;AACtD,WAAU,GAAI,MAAM,QAAQ,OAAO,GAAG,SAAS,CAAC,MAAO,EAAE;AACzD,QAAO;AACR;;;;;;;;;;;AAYD,eAAsB,YACpBD,UACAE,YACAC,aACoC;CACpC,MAAM,EAAE,QAAQ,WAAW,GAAG,SAAS,eAAe;AAGtD,WAAU,WAAW;CAGrB,MAAM,SAAS,CAAC,MAAM,OAAO,MAAM,YAAY,CAAC,SAAS,EAAE,GAAG,GAAG;AACjE,MAAK,OACH,OAAM,IAAI,MAAM;AAElB,QAAO;AACR;;;;;;;;;;;;;AAcD,eAAsB,cACpBH,UACAE,YACAC,aACmC;CACnC,MAAM,EAAE,QAAQ,WAAW,GAAG,SAAS,eAAe;AAGtD,WAAU,WAAW;AAGrB,QAAO,MAAM,OAAO,MAAM,YAAY,CAAC,SAAS;AACjD;;;;;;;;;;;;;;;;;AAkBD,SAAgB,UACdH,UACAE,YACAC,aACuC;CACvC,MAAM,EAAE,QAAQ,WAAW,GAAG,SAAS,eAAe;AAGtD,WAAU,WAAW;AAGrB,QAAO,OAAO,MAAM,YAAY,CAAC,QAAQ;AAC1C"}