{"version": 3, "file": "next.js", "names": ["workflow: Workflow", "getStart: (\n    request: NextRequest,\n  ) => WorkflowEventData<Start> | Promise<WorkflowEventData<Start>>", "stop: WorkflowEvent<Stop>"], "sources": ["../src/next.ts"], "sourcesContent": ["import { NextRequest } from \"next/server\";\nimport type {\n  Workflow,\n  WorkflowEventData,\n  WorkflowEvent,\n} from \"@llamaindex/workflow-core\";\nimport { run } from \"./stream/run\";\n\ntype WorkflowAPI = {\n  GET: (request: NextRequest) => Promise<Response>;\n};\n\nexport const createNextHandler = <Start, Stop>(\n  workflow: Workflow,\n  getStart: (\n    request: NextRequest,\n  ) => WorkflowEventData<Start> | Promise<WorkflowEventData<Start>>,\n  stop: WorkflowEvent<Stop>,\n): WorkflowAPI => {\n  return {\n    GET: async (request) => {\n      const result = await run(workflow, await getStart(request))\n        .until(stop)\n        .toArray();\n      return Response.json(result.at(-1)!.data);\n    },\n  };\n};\n"], "mappings": ";;;;AAYA,MAAa,oBAAoB,CAC/BA,UACAC,UAGAC,SACgB;AAChB,QAAO,EACL,KAAK,OAAO,YAAY;EACtB,MAAM,SAAS,MAAM,IAAI,UAAU,MAAM,SAAS,QAAQ,CAAC,CACxD,MAAM,KAAK,CACX,SAAS;AACZ,SAAO,SAAS,KAAK,OAAO,GAAG,GAAG,CAAE,KAAK;CAC1C,EACF;AACF"}