//#region src/stream/run.ts
/**
* Runs a workflow with the provided events and returns the resulting stream.
*
* ```ts
* const events = await run(workflow, startEvent.with("42")).toArray();
* ```
*
*/
function run(workflow, events) {
	const { stream, sendEvent } = workflow.createContext();
	sendEvent(...Array.isArray(events) ? events : [events]);
	return stream;
}
/**
* Runs a workflow with a specified input event and returns the first matching event of the specified output type.
*
* @deprecated Use `stream.until().toArray()` for a more idiomatic approach.
* @example
* ```ts
* const result = await runWorkflow(workflow, startEvent.with("42"), stopEvent);
* console.log(`Result: ${result.data === 1 ? 'positive' : 'negative'}`);
* ```
*/
async function runWorkflow(workflow, inputEvent, outputEvent) {
	const { stream, sendEvent } = workflow.createContext();
	sendEvent(inputEvent);
	const result = (await stream.until(outputEvent).toArray()).at(-1);
	if (!result) throw new Error("No output event received");
	return result;
}
/**
* Runs a workflow with a specified input event and collects all events until a specified output event is encountered.
* Returns an array containing all events including the final output event.
*
* @deprecated Use `stream.until().toArray()` for a more idiomatic approach.
* @example
* ```ts
* const allEvents = await runAndCollect(workflow, startEvent.with("42"), stopEvent);
* const finalEvent = allEvents[allEvents.length - 1];
* console.log(`Result: ${finalEvent.data === 1 ? 'positive' : 'negative'}`);
* ```
*/
async function runAndCollect(workflow, inputEvent, outputEvent) {
	const { stream, sendEvent } = workflow.createContext();
	sendEvent(inputEvent);
	return await stream.until(outputEvent).toArray();
}
/**
* Runs a workflow with a specified input event and returns an async iterable stream of all events
* until a specified output event is encountered.
*
* This allows processing events one by one without collecting them all upfront.
*
* @deprecated Use `stream.until().toArray()` for a more idiomatic approach.
* @example
* ```ts
* const eventStream = runStream(workflow, startEvent.with("42"), stopEvent);
* for await (const event of eventStream) {
*   console.log(`Processing event: ${event}`);
*   // Do something with each event as it arrives
* }
* ```
*/
function runStream(workflow, inputEvent, outputEvent) {
	const { stream, sendEvent } = workflow.createContext();
	sendEvent(inputEvent);
	return stream.until(outputEvent).values();
}

//#endregion
export { run, runAndCollect, runStream, runWorkflow };
//# sourceMappingURL=run.js.map