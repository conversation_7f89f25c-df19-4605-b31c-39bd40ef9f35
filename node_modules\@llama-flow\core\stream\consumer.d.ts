import { WorkflowEventData, WorkflowStream } from "@llama-flow/core";

//#region src/stream/consumer.d.ts
/**
* A no-op function that consumes a stream of events and does nothing with them.
*
* Do not collect the raw stream from `workflow.createContext()`
* or `getContext()`, it's infinite and will never finish
*
* @deprecated uss `await stream.toArray()` instead
*/
/**
 * A no-op function that consumes a stream of events and does nothing with them.
 *
 * Do not collect the raw stream from `workflow.createContext()`
 * or `getContext()`, it's infinite and will never finish
 *
 * @deprecated uss `await stream.toArray()` instead
 */
declare const nothing: (stream: ReadableStream | WorkflowStream) => Promise<void>;
/**
 * Collects all events from a stream and returns them as an array.
 *
 * Do not collect the raw stream from `workflow.createContext()`
 * or getContext()`, it's infinite and will never finish.
 *
 * @deprecated uss `await stream.toArray()` instead
 */
declare const collect: <T extends WorkflowEventData<any>>(stream: ReadableStream<T> | WorkflowStream) => Promise<WorkflowEventData<any>[]>;

//#endregion
export { collect, nothing };
//# sourceMappingURL=consumer.d.ts.map