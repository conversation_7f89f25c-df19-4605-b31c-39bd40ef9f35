import { run } from "./run-25vavBMH.js";

//#region src/hono.ts
const createHonoHandler = (workflow, getStart, stopEvent, wrapStopEvent) => {
	if (!wrapStopEvent) wrapStopEvent = (c, stop) => {
		return c.json(stop);
	};
	return async (c) => {
		const results = await run(workflow, await getStart(c)).until(stopEvent).toArray();
		return wrapStopEvent(c, results.at(-1).data);
	};
};

//#endregion
export { createHonoHandler };
//# sourceMappingURL=hono.js.map