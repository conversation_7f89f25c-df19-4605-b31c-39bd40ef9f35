{"version": 3, "file": "observable.js", "names": ["stream: ReadableStream<WorkflowEventData<any>> | WorkflowStream"], "sources": ["../src/observable.ts"], "sourcesContent": ["import { Observable } from \"rxjs\";\nimport {\n  type WorkflowEventData,\n  WorkflowStream,\n} from \"@llamaindex/workflow-core\";\n\nexport const toObservable = (\n  stream: ReadableStream<WorkflowEventData<any>> | WorkflowStream,\n): Observable<WorkflowEventData<any>> => {\n  return new Observable((subscriber) => {\n    const reader = stream.getReader();\n\n    const read = async () => {\n      try {\n        const { done, value } = await reader.read();\n        if (done) {\n          subscriber.complete();\n        } else {\n          subscriber.next(value);\n          read();\n        }\n      } catch (error) {\n        subscriber.error(error);\n      }\n    };\n\n    read().catch(subscriber.error);\n\n    return () => {\n      reader.cancel().catch(subscriber.error);\n    };\n  });\n};\n"], "mappings": ";;;;AAMA,MAAa,eAAe,CAC1BA,WACuC;AACvC,QAAO,IAAI,WAAW,CAAC,eAAe;EACpC,MAAM,SAAS,OAAO,WAAW;EAEjC,MAAM,OAAO,YAAY;AACvB,OAAI;IACF,MAAM,EAAE,MAAM,OAAO,GAAG,MAAM,OAAO,MAAM;AAC3C,QAAI,KACF,YAAW,UAAU;SAChB;AACL,gBAAW,KAAK,MAAM;AACtB,WAAM;IACP;GACF,SAAQ,OAAO;AACd,eAAW,MAAM,MAAM;GACxB;EACF;AAED,QAAM,CAAC,MAAM,WAAW,MAAM;AAE9B,SAAO,MAAM;AACX,UAAO,QAAQ,CAAC,MAAM,WAAW,MAAM;EACxC;CACF;AACF"}