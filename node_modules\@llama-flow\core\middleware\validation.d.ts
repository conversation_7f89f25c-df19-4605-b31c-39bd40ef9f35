import { Workflow, WorkflowEvent, WorkflowEventData } from "@llama-flow/core";

//#region src/middleware/validation.d.ts
type ValidationHandler<Validation extends [inputs: WorkflowEvent<any>[], output: WorkflowEvent<any>[]][], AcceptEvents extends WorkflowEvent<any>[], Result extends WorkflowEventData<any> | void> = (sendEvent: (...inputs: Array<Validation[number] extends infer Tuple ? <PERSON><PERSON> extends [AcceptEvents, infer Outputs] ? Outputs extends WorkflowEvent<any>[] ? ReturnType<Outputs[number]["with"]> : never : never : never>) => void, ...events: { [K in keyof AcceptEvents]: ReturnType<AcceptEvents[K]["with"]> }) => Result | Promise<Result>;
type WithValidationWorkflow<Validation extends [inputs: WorkflowEvent<any>[], output: WorkflowEvent<any>[]][]> = {
  strictHandle<const AcceptEvents extends WorkflowEvent<any>[], Result extends ReturnType<WorkflowEvent<any>["with"]> | void>(accept: AcceptEvents, handler: ValidationHandler<Validation, AcceptEvents, Result>): void;
};
declare function withValidation<const Validation extends [inputs: WorkflowEvent<any>[], outputs: WorkflowEvent<any>[]][], WorkflowLike extends Workflow>(workflow: WorkflowLike, validation: Validation): WithValidationWorkflow<Validation> & WorkflowLike;

//#endregion
export { ValidationHandler, WithValidationWorkflow, withValidation };
//# sourceMappingURL=validation.d.ts.map