import '@llamaindex/env';
import 'magic-bytes.js';

/**
 * Extracts just the text whether from
 *  a multi-modal message
 *  a single text message
 *  or a query
 *
 * @param message The message to extract text from.
 * @returns The extracted text
 */ function extractText(message) {
    if (typeof message === "object" && "query" in message) {
        return extractText(message.query);
    }
    if (typeof message !== "string" && !Array.isArray(message)) {
        console.warn("extractText called with non-MessageContent message, this is likely a bug.");
        return `${message}`;
    } else if (typeof message !== "string" && Array.isArray(message)) {
        // message is of type MessageContentDetail[] - retrieve just the text parts and concatenate them
        // so we can pass them to the context generator
        return message.filter((c)=>c.type === "text").map((c)=>c.text).join("\n\n");
    } else {
        return message;
    }
}

async function* streamConverter(stream, converter) {
    for await (const data of stream){
        const newData = converter(data);
        if (newData === null) {
            return;
        }
        yield newData;
    }
}

class BaseLLM {
    async complete(params) {
        const { prompt, stream, responseFormat } = params;
        if (stream) {
            const stream = await this.chat({
                messages: [
                    {
                        content: prompt,
                        role: "user"
                    }
                ],
                stream: true,
                ...responseFormat ? {
                    responseFormat
                } : {}
            });
            return streamConverter(stream, (chunk)=>{
                return {
                    raw: null,
                    text: chunk.delta
                };
            });
        }
        const chatResponse = await this.chat({
            messages: [
                {
                    content: prompt,
                    role: "user"
                }
            ],
            ...responseFormat ? {
                responseFormat
            } : {}
        });
        return {
            text: extractText(chatResponse.message.content),
            raw: chatResponse.raw
        };
    }
}
class ToolCallLLM extends BaseLLM {
}

var LiveLLMCapability = /*#__PURE__*/ function(LiveLLMCapability) {
    LiveLLMCapability["EPHEMERAL_KEY"] = "ephemeral_key";
    LiveLLMCapability["AUDIO_CONFIG"] = "audio_config";
    return LiveLLMCapability;
}({});
class LiveLLMSession {
    isTextMessage(content) {
        return content.type === "text";
    }
    isAudioMessage(content) {
        return content.type === "audio";
    }
    isImageMessage(content) {
        return content.type === "image";
    }
    isVideoMessage(content) {
        return content.type === "video";
    }
    sendMessage(message) {
        const { content, role } = message;
        if (!Array.isArray(content)) {
            this.messageSender.sendTextMessage(content, role);
        } else {
            for (const item of content){
                this.processMessage(item, role);
            }
        }
    }
    processMessage(message, role) {
        if (this.isTextMessage(message)) {
            this.messageSender.sendTextMessage(message.text, role);
        } else if (this.isAudioMessage(message) && this.messageSender.sendAudioMessage) {
            this.messageSender.sendAudioMessage(message, role);
        } else if (this.isImageMessage(message) && this.messageSender.sendImageMessage) {
            this.messageSender.sendImageMessage(message, role);
        } else if (this.isVideoMessage(message) && this.messageSender.sendVideoMessage) {
            this.messageSender.sendVideoMessage(message, role);
        }
    }
    async *streamEvents() {
        while(true){
            const event = await this.nextEvent();
            if (event === undefined) {
                break;
            }
            yield event;
        }
    }
    async nextEvent() {
        if (this.eventQueue.length) {
            return Promise.resolve(this.eventQueue.shift());
        }
        return new Promise((resolve)=>{
            this.eventResolvers.push(resolve);
        });
    }
    //Uses an async queue to send events to the client
    // if the consumer is waiting for an event, it will be resolved immediately
    // otherwise, the event will be queued up and sent when the consumer is ready
    pushEventToQueue(event) {
        if (this.eventResolvers.length) {
            //resolving the promise with the event
            this.eventResolvers.shift()(event);
        } else {
            this.eventQueue.push(event);
        }
    }
    constructor(){
        this.eventQueue = [];
        this.eventResolvers = [];
        this.closed = false;
    }
}
class LiveLLM {
    hasCapability(capability) {
        return this.capabilities.has(capability);
    }
    constructor(){
        /**
   * Set of capabilities supported by this implementation.
   * Override in subclasses as needed.
   */ this.capabilities = new Set();
    }
}

const liveEvents = {
    open: {
        include: (e)=>e.type === "open"
    },
    audio: {
        include: (e)=>e.type === "audio"
    },
    text: {
        include: (e)=>e.type === "text"
    },
    error: {
        include: (e)=>e.type === "error"
    },
    close: {
        include: (e)=>e.type === "close"
    },
    setupComplete: {
        include: (e)=>e.type === "setupComplete"
    },
    interrupted: {
        include: (e)=>e.type === "interrupted"
    },
    generationComplete: {
        include: (e)=>e.type === "generationComplete"
    },
    turnComplete: {
        include: (e)=>e.type === "turnComplete"
    }
};

function addContentPart(message, part) {
    if (part.type === "text") {
        if (typeof message.content === "string") {
            message.content += part.text;
        } else {
            message.content.push(part);
        }
    } else {
        if (typeof message.content === "string") {
            if (message.content === "") {
                message.content = [
                    part
                ];
            } else {
                message.content = [
                    {
                        type: "text",
                        text: message.content
                    },
                    part
                ];
            }
        } else {
            message.content.push(part);
        }
    }
}

export { BaseLLM, LiveLLM, LiveLLMCapability, LiveLLMSession, ToolCallLLM, addContentPart, liveEvents };
