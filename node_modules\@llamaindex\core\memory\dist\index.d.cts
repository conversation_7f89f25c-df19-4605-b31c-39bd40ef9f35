import { ChatMessage, LLM, MessageContent, MessageType } from '../../llms/dist/index.cjs';
import { BaseChatStore } from '../../storage/chat-store/dist/index.cjs';
import { Tokenizer } from '@llamaindex/env/tokenizers';
import { SummaryPrompt } from '../../prompts/dist/index.cjs';

/**
 * A ChatMemory is used to keep the state of back and forth chat messages
 * @deprecated Use Memory instead.
 */
declare abstract class BaseMemory<AdditionalMessageOptions extends object = object> {
    /**
     * Retrieves messages from the memory, optionally including transient messages.
     * Compared to getAllMessages, this method a) allows for transient messages to be included in the retrieval and b) may return a subset of the total messages by applying a token limit.
     * @param transientMessages Optional array of temporary messages to be included in the retrieval.
     * These messages are not stored in the memory but are considered for the current interaction.
     * @returns An array of chat messages, either synchronously or as a Promise.
     */
    abstract getMessages(transientMessages?: ChatMessage<AdditionalMessageOptions>[] | undefined): ChatMessage<AdditionalMessageOptions>[] | Promise<ChatMessage<AdditionalMessageOptions>[]>;
    /**
     * Retrieves all messages stored in the memory.
     * @returns An array of all chat messages, either synchronously or as a Promise.
     */
    abstract getAllMessages(): ChatMessage<AdditionalMessageOptions>[] | Promise<ChatMessage<AdditionalMessageOptions>[]>;
    /**
     * Adds a new message to the memory.
     * @param messages The chat message to be added to the memory.
     */
    abstract put(messages: ChatMessage<AdditionalMessageOptions>): void;
    /**
     * Clears all messages from the memory.
     */
    abstract reset(): void;
    protected _tokenCountForMessages(messages: ChatMessage[]): number;
}
/**
 * @deprecated Use Memory with snapshot feature with your own storage instead.
 */
declare abstract class BaseChatStoreMemory<AdditionalMessageOptions extends object = object> extends BaseMemory<AdditionalMessageOptions> {
    chatStore: BaseChatStore<AdditionalMessageOptions>;
    chatStoreKey: string;
    protected constructor(chatStore?: BaseChatStore<AdditionalMessageOptions>, chatStoreKey?: string);
    getAllMessages(): ChatMessage<AdditionalMessageOptions>[] | Promise<ChatMessage<AdditionalMessageOptions>[]>;
    put(messages: ChatMessage<AdditionalMessageOptions>): void | Promise<void>;
    set(messages: ChatMessage<AdditionalMessageOptions>[]): void | Promise<void>;
    reset(): void | Promise<void>;
}

type ChatMemoryBufferOptions<AdditionalMessageOptions extends object = object> = {
    tokenLimit?: number | undefined;
    chatStore?: BaseChatStore<AdditionalMessageOptions> | undefined;
    chatStoreKey?: string | undefined;
    chatHistory?: ChatMessage<AdditionalMessageOptions>[] | undefined;
    llm?: LLM<object, AdditionalMessageOptions> | undefined;
};
/**
 * @deprecated Use Memory instead.
 */
declare class ChatMemoryBuffer<AdditionalMessageOptions extends object = object> extends BaseChatStoreMemory<AdditionalMessageOptions> {
    tokenLimit: number;
    constructor(options?: Partial<ChatMemoryBufferOptions<AdditionalMessageOptions>>);
    getMessages(transientMessages?: ChatMessage<AdditionalMessageOptions>[] | undefined, initialTokenCount?: number): Promise<ChatMessage<AdditionalMessageOptions>[]>;
}

/**
 * @deprecated Use Memory instead.
 */
declare class ChatSummaryMemoryBuffer extends BaseMemory {
    /**
     * Tokenizer function that converts text to tokens,
     *  this is used to calculate the number of tokens in a message.
     */
    tokenizer: Tokenizer;
    tokensToSummarize: number;
    messages: ChatMessage[];
    summaryPrompt: SummaryPrompt;
    llm: LLM;
    constructor(options?: Partial<ChatSummaryMemoryBuffer>);
    private summarize;
    private get lastSummaryIndex();
    getLastSummary(): ChatMessage | null;
    private get systemMessages();
    private get nonSystemMessages();
    /**
     * Calculates the messages that describe the conversation so far.
     * If there's no memory, all non-system messages are used.
     * If there's a memory, uses all messages after the last summary message.
     */
    private calcConversationMessages;
    private calcCurrentRequestMessages;
    reset(): void;
    getMessages(transientMessages?: ChatMessage[]): Promise<ChatMessage[]>;
    getAllMessages(): Promise<ChatMessage[]>;
    put(message: ChatMessage): void;
}

/**
 * Additional properties for storing additional data to memory messages
 * using the same properties as vercel/ai for simplicity
 */
type MemoryMessageExtension = {
    id: string;
    createdAt?: Date | undefined;
    annotations?: Array<unknown> | undefined;
};
type MemoryMessage<AdditionalMessageOptions extends object = object> = ChatMessage<AdditionalMessageOptions> & MemoryMessageExtension;
type MemorySnapshot = {
    messages: MemoryMessage[];
    tokenLimit: number;
};

interface MessageAdapter<T, TMessageOptions extends object = object> {
    fromMemory(message: MemoryMessage<TMessageOptions>): T;
    toMemory(message: T): MemoryMessage<TMessageOptions>;
    isCompatible(message: unknown): message is T;
}

declare class ChatMessageAdapter<AdditionalMessageOptions extends object = object> implements MessageAdapter<ChatMessage<AdditionalMessageOptions>, AdditionalMessageOptions> {
    fromMemory(message: MemoryMessage<AdditionalMessageOptions>): ChatMessage<AdditionalMessageOptions>;
    toMemory(message: ChatMessage<AdditionalMessageOptions>): MemoryMessage<AdditionalMessageOptions>;
    isCompatible(message: unknown): message is ChatMessage<AdditionalMessageOptions>;
}

type VercelMessage = {
    id: string;
    role: "system" | "user" | "assistant" | "data";
    content: string;
    createdAt?: Date | undefined;
    annotations?: Array<unknown> | undefined;
    parts: Array<{
        type: string;
        [key: string]: unknown;
    }>;
};
/**
 * Utility class for converting between LlamaIndex ChatMessage and Vercel UI Message formats
 */
declare class VercelMessageAdapter<AdditionalMessageOptions extends object = object> implements MessageAdapter<VercelMessage, AdditionalMessageOptions> {
    /**
     * Convert LlamaIndex ChatMessage to Vercel UI Message format
     */
    fromMemory(memoryMessage: MemoryMessage<object>): VercelMessage;
    /**
     * Convert Vercel UI Message to LlamaIndex ChatMessage format
     */
    toMemory(uiMessage: VercelMessage): MemoryMessage<AdditionalMessageOptions>;
    /**
     * Validate if object matches VercelMessage structure
     */
    isCompatible(message: unknown): message is VercelMessage;
    /**
     * Convert UI parts to MessageContent
     */
    private convertVercelPartsToMessageContent;
    /**
     * Convert MessageContent to UI parts
     */
    private convertMessageContentToVercelParts;
}

type MemoryBlockOptions = {
    /**
     * The id of the memory block.
     */
    id?: string;
    /**
     * The priority of the memory block.
     * Note: if priority is 0, the block content is always included in the memory context.
     */
    priority: number;
    /**
     * Whether the memory block is long term.
     * Default is true.
     */
    isLongTerm?: boolean;
};
/**
 * A base class for memory blocks.
 */
declare abstract class BaseMemoryBlock<TAdditionalMessageOptions extends object = object> {
    readonly id: string;
    readonly priority: number;
    readonly isLongTerm: boolean;
    constructor(options: MemoryBlockOptions);
    /**
     * Pull the memory block content (async).
     *
     * @returns The memory block content as an array of ChatMessage.
     */
    abstract get(): Promise<MemoryMessage<TAdditionalMessageOptions>[]>;
    /**
     * Store the messages in the memory block.
     */
    abstract put(messages: MemoryMessage<TAdditionalMessageOptions>[]): Promise<void>;
}

/**
 * The options for the fact extraction memory block.
 */
type FactExtractionMemoryBlockOptions = {
    /**
     * The fact extraction model to use.
     */
    llm: LLM;
    /**
     * The maximum number of facts to extract.
     */
    maxFacts: number;
    /**
     * The prompt to use for fact extraction.
     */
    extractionPrompt?: string;
    /**
     * The prompt to use for fact summary.
     */
    summaryPrompt?: string;
} & MemoryBlockOptions & {
    isLongTerm?: true;
};
/**
 * A memory block that stores facts extracted from conversations.
 */
declare class FactExtractionMemoryBlock<TAdditionalMessageOptions extends object = object> extends BaseMemoryBlock<TAdditionalMessageOptions> {
    private readonly llm;
    private facts;
    private readonly maxFacts;
    private readonly extractionPrompt;
    private readonly summaryPrompt;
    constructor(options: FactExtractionMemoryBlockOptions);
    get(): Promise<MemoryMessage<TAdditionalMessageOptions>[]>;
    put(messages: MemoryMessage<TAdditionalMessageOptions>[]): Promise<void>;
}

type StaticMemoryBlockOptions = {
    /**
     * The static content to store.
     */
    content: MessageContent;
    /**
     * The role of the message.
     */
    messageRole?: MessageType;
} & Omit<MemoryBlockOptions, "priority" | "isLongTerm">;
/**
 * A memory block that stores static content that doesn't change.
 * Static content is always included in the memory context.
 */
declare class StaticMemoryBlock<TAdditionalMessageOptions extends object = object> extends BaseMemoryBlock<TAdditionalMessageOptions> {
    private readonly content;
    private readonly messageRole;
    constructor(options: StaticMemoryBlockOptions);
    /**
     * Returns the static content.
     * The messages parameter is ignored since this block contains static content.
     */
    get(): Promise<MemoryMessage<TAdditionalMessageOptions>[]>;
    put(_messages: MemoryMessage<TAdditionalMessageOptions>[]): Promise<void>;
}

type BuiltinAdapters<TMessageOptions extends object = object> = {
    vercel: VercelMessageAdapter;
    llamaindex: ChatMessageAdapter<TMessageOptions>;
};
type MemoryOptions<TMessageOptions extends object = object> = {
    tokenLimit?: number;
    /**
     * How much of the token limit is used for short term memory.
     * The remaining token limit is used for long term memory.
     * Default is 0.5.
     */
    shortTermTokenLimitRatio?: number;
    customAdapters?: Record<string, MessageAdapter<unknown, object>>;
    memoryBlocks?: BaseMemoryBlock<TMessageOptions>[];
    /**
     * The cursor position for tracking processed messages into long-term memory.
     * Used internally for memory restoration from snapshots.
     */
    memoryCursor?: number;
};
declare class Memory<TAdapters extends Record<string, MessageAdapter<unknown, TMessageOptions>> = Record<string, never>, TMessageOptions extends object = object> {
    /**
     * Hold all messages put into the memory.
     */
    private messages;
    /**
     * The token limit for memory retrieval results.
     */
    private tokenLimit;
    /**
     * The ratio of the token limit for short term memory.
     */
    private shortTermTokenLimitRatio;
    /**
     * The adapters for the memory.
     */
    private adapters;
    /**
     * The memory blocks for the memory.
     */
    private memoryBlocks;
    /**
     * The cursor for the messages that have been processed into long-term memory.
     */
    private memoryCursor;
    constructor(messages?: MemoryMessage<TMessageOptions>[], options?: MemoryOptions<TMessageOptions>);
    /**
     * Add a message to the memory
     * @param message - The message to add to the memory
     */
    add(message: unknown): Promise<void>;
    /**
     * Get the messages of specific type from the memory
     * @param options - The options for the get method
     * @returns The messages of specific type
     */
    get<K extends keyof (TAdapters & BuiltinAdapters<TMessageOptions>) = "llamaindex">(options?: {
        type?: K;
        transientMessages?: ChatMessage<TMessageOptions>[];
    }): Promise<K extends keyof (TAdapters & BuiltinAdapters<TMessageOptions>) ? ReturnType<(TAdapters & BuiltinAdapters<TMessageOptions>)[K]["fromMemory"]>[] : never>;
    /**
     * Get the messages from the memory, optionally including transient messages.
     * only return messages that are within context window of the LLM
     * @param llm - To fit the result messages to the context window of the LLM. If not provided, the default token limit will be used.
     * @param transientMessages - Optional transient messages to include.
     * @returns The messages from the memory, optionally including transient messages.
     */
    getLLM(llm?: LLM, transientMessages?: ChatMessage<TMessageOptions>[]): Promise<ChatMessage[]>;
    /**
     * Get the content from the memory blocks
     * also convert the content to chat messages
     * @param blocks - The blocks to get the content from
     * @param tokenLimit - The token limit for the memory blocks, if not provided, all the memory blocks will be included
     */
    private getMemoryBlockMessages;
    /**
     * Manage the memory blocks
     * This method processes new messages into memory blocks when short-term memory exceeds its token limit.
     * It uses a cursor system to track which messages have already been processed into long-term memory.
     */
    manageMemoryBlocks(): Promise<void>;
    /**
     * Get messages that haven't been processed into long-term memory yet
     */
    private getUnprocessedMessages;
    /**
     * Process new messages into all memory blocks
     */
    private processMessagesIntoMemoryBlocks;
    /**
     * Update the memory cursor after successful processing
     */
    private updateMemoryCursor;
    /**
     * Clear all the messages in the memory
     */
    clear(): Promise<void>;
    /**
     * Creates a snapshot of the current memory state
     * Note: Memory blocks are not included in snapshots as they may contain non-serializable content.
     * Memory blocks should be recreated when loading from snapshot.
     * @returns A JSON-serializable object containing the memory state
     */
    snapshot(): string;
    private countMemoryMessagesToken;
    private countMessagesToken;
}

/**
 * Create a Memory instance with default options
 * @returns A new Memory instance with default configuration
 */
declare function createMemory<TMessageOptions extends object = object>(): Memory<Record<string, never>, TMessageOptions>;
/**
 * Create a Memory instance with options only
 * @param options - Memory configuration options
 * @returns A new Memory instance
 */
declare function createMemory<TMessageOptions extends object = object>(options: MemoryOptions<TMessageOptions>): Memory<Record<string, never>, TMessageOptions>;
/**
 * Create a Memory instance with ChatMessage array (IDs will be generated)
 * @param messages - Initial ChatMessage array for the memory
 * @param options - Memory configuration options
 * @returns A new Memory instance
 */
declare function createMemory<TMessageOptions extends object = object>(messages: ChatMessage<TMessageOptions>[], options?: MemoryOptions<TMessageOptions>): Memory<Record<string, never>, TMessageOptions>;
/**
 * Create a Memory instance with MemoryMessage array and options
 * @param messages - Initial MemoryMessage array for the memory
 * @param options - Memory configuration options
 * @returns A new Memory instance
 */
declare function createMemory<TMessageOptions extends object = object>(messages: MemoryMessage<TMessageOptions>[], options: MemoryOptions<TMessageOptions>): Memory<Record<string, never>, TMessageOptions>;
/**
 * create a StaticMemoryBlock
 * @param options - Configuration options for the static memory block
 * @returns A new StaticMemoryBlock instance
 */
declare function staticBlock<TMessageOptions extends object = object>(options: StaticMemoryBlockOptions): StaticMemoryBlock<TMessageOptions>;
/**
 * create a FactExtractionMemoryBlock
 * @param options - Configuration options for the fact extraction memory block
 * @returns A new FactExtractionMemoryBlock instance
 */
declare function factExtractionBlock<TMessageOptions extends object = object>(options: FactExtractionMemoryBlockOptions): FactExtractionMemoryBlock<TMessageOptions>;
/**
 * Creates a new Memory instance from a snapshot
 * @param snapshot The snapshot to load from
 * @param options Optional MemoryOptions to apply when loading (including memory blocks)
 * @returns A new Memory instance with the snapshot data and provided options
 */
declare function loadMemory<TMessageOptions extends object = object>(snapshot: string, options?: MemoryOptions<TMessageOptions>): Memory<Record<string, never>, TMessageOptions>;

export { BaseMemory, BaseMemoryBlock, ChatMemoryBuffer, ChatMessageAdapter, ChatSummaryMemoryBuffer, FactExtractionMemoryBlock, Memory, type MemoryMessage, type MemoryMessageExtension, type MemorySnapshot, type MessageAdapter, StaticMemoryBlock, type VercelMessage, VercelMessageAdapter, createMemory, factExtractionBlock, loadMemory, staticBlock };
