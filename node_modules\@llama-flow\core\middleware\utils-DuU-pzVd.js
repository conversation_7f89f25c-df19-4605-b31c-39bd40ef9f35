//#region src/core/utils.ts
const isPromiseLike = (value) => value != null && typeof value.then === "function";
const __internal__subscribesSourcemap = new WeakMap();
/**
* @internal
*/
function createSubscribable() {
	const subscribers = new Set();
	const obj = {
		subscribe: (callback) => {
			subscribers.add(callback);
			return () => {
				subscribers.delete(callback);
			};
		},
		publish: (...args) => {
			const results = [];
			for (const callback of subscribers) results.push(callback(...args));
			return results;
		}
	};
	__internal__subscribesSourcemap.set(obj, subscribers);
	return obj;
}

//#endregion
export { createSubscribable, isPromiseLike };
//# sourceMappingURL=utils-DuU-pzVd.js.map