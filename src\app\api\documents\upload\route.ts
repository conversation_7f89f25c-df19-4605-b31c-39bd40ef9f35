import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import llamaIndexService from '@/lib/llamaindex-service';
import documentManager from '@/lib/document-manager';

const UPLOAD_DIR = path.join(process.cwd(), 'uploads');

// Ensure upload directory exists
async function ensureUploadDir() {
  if (!existsSync(UPLOAD_DIR)) {
    await mkdir(UPLOAD_DIR, { recursive: true });
  }
}

export async function POST(request: NextRequest) {
  try {
    await ensureUploadDir();

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ['.txt', '.md'];
    const fileExtension = path.extname(file.name).toLowerCase();
    
    if (!allowedTypes.includes(fileExtension)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only .txt and .md files are allowed.' },
        { status: 400 }
      );
    }

    // Generate unique filename
    const fileId = uuidv4();
    const fileName = `${fileId}${fileExtension}`;
    const filePath = path.join(UPLOAD_DIR, fileName);

    // Save file
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Add to document manager
    await documentManager.addDocument({
      id: fileId,
      filename: fileName,
      originalName: file.name,
      size: file.size,
      type: fileExtension,
    });

    // Process file with LlamaIndex
    try {
      await llamaIndexService.addDocument(filePath, fileId, file.name, file.size);
    } catch (indexError) {
      console.error('Failed to index document:', indexError);
      // Continue even if indexing fails - file is still uploaded
    }

    return NextResponse.json({
      id: fileId,
      name: file.name,
      size: file.size,
      path: filePath,
      message: 'File uploaded and indexed successfully'
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}
