import { readFile, writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

interface DocumentMetadata {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  uploadDate: string;
  lastModified: string;
  type: string;
  status: 'uploading' | 'processing' | 'indexed' | 'error';
  errorMessage?: string;
}

class DocumentManager {
  private readonly metadataFile = path.join(process.cwd(), 'documents_metadata.json');
  private documents: Map<string, DocumentMetadata> = new Map();

  async initialize() {
    try {
      if (existsSync(this.metadataFile)) {
        const data = await readFile(this.metadataFile, 'utf-8');
        const documentsArray = JSON.parse(data);
        this.documents = new Map(documentsArray.map((doc: DocumentMetadata) => [doc.id, doc]));
        console.log(`Loaded ${this.documents.size} document metadata entries`);
      }
    } catch (error) {
      console.error('Failed to load document metadata:', error);
    }
  }

  async addDocument(metadata: Omit<DocumentMetadata, 'uploadDate' | 'lastModified' | 'status'>) {
    const fullMetadata: DocumentMetadata = {
      ...metadata,
      uploadDate: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      status: 'uploading',
    };

    this.documents.set(metadata.id, fullMetadata);
    await this.saveMetadata();
    return fullMetadata;
  }

  async updateDocumentStatus(id: string, status: DocumentMetadata['status'], errorMessage?: string) {
    const doc = this.documents.get(id);
    if (doc) {
      doc.status = status;
      doc.lastModified = new Date().toISOString();
      if (errorMessage) {
        doc.errorMessage = errorMessage;
      }
      this.documents.set(id, doc);
      await this.saveMetadata();
    }
  }

  async removeDocument(id: string) {
    this.documents.delete(id);
    await this.saveMetadata();
  }

  getDocument(id: string): DocumentMetadata | undefined {
    return this.documents.get(id);
  }

  getAllDocuments(): DocumentMetadata[] {
    return Array.from(this.documents.values());
  }

  getDocumentsByStatus(status: DocumentMetadata['status']): DocumentMetadata[] {
    return Array.from(this.documents.values()).filter(doc => doc.status === status);
  }

  private async saveMetadata() {
    try {
      const documentsArray = Array.from(this.documents.values());
      await writeFile(this.metadataFile, JSON.stringify(documentsArray, null, 2));
    } catch (error) {
      console.error('Failed to save document metadata:', error);
    }
  }

  async getStats() {
    const docs = Array.from(this.documents.values());
    return {
      total: docs.length,
      indexed: docs.filter(d => d.status === 'indexed').length,
      processing: docs.filter(d => d.status === 'processing').length,
      errors: docs.filter(d => d.status === 'error').length,
      totalSize: docs.reduce((sum, doc) => sum + doc.size, 0),
    };
  }
}

// Singleton instance
const documentManager = new DocumentManager();

export default documentManager;
export type { DocumentMetadata };
