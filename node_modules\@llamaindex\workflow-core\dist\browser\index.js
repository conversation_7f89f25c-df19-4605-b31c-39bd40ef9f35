//#region src/core/event.ts
const eventMap = /* @__PURE__ */ new WeakMap();
const refMap = /* @__PURE__ */ new WeakMap();
let i = 0;
let j = 0;
const workflowEvent = (config) => {
	const l1 = `${i++}`;
	const event = {
		debugLabel: config?.debugLabel ?? l1,
		include: (instance) => s.has(instance),
		with: (data) => {
			const l2 = `${j++}`;
			const ref = {
				[Symbol.toStringTag]: config?.debugLabel ?? `WorkflowEvent(${l1}.${l2})`,
				toString: () => config?.debugLabel ? config.debugLabel : `WorkflowEvent(${l1}.${l2})`,
				toJSON: () => {
					return {
						type: config?.debugLabel ? config.debugLabel : l1,
						data
					};
				},
				get data() {
					return data;
				}
			};
			s.add(ref);
			refMap.set(ref, event);
			return ref;
		}
	};
	const s = /* @__PURE__ */ new WeakSet();
	eventMap.set(event, s);
	Object.defineProperty(event, Symbol.toStringTag, { get: () => event?.debugLabel ?? `WorkflowEvent<${l1}>` });
	Object.defineProperty(event, "displayName", { value: event?.debugLabel ?? `WorkflowEvent<${l1}>` });
	let uniqueId = config?.uniqueId;
	Object.defineProperty(event, "uniqueId", {
		get: () => {
			if (!uniqueId) uniqueId = l1;
			return uniqueId;
		},
		set: () => {
			throw new Error("uniqueId is readonly");
		}
	});
	event.toString = () => config?.debugLabel ?? `WorkflowEvent<${l1}>`;
	return event;
};
const isWorkflowEvent = (instance) => typeof instance === "object" && instance !== null ? eventMap.has(instance) : false;
const isWorkflowEventData = (instance) => typeof instance === "object" && instance !== null ? refMap.has(instance) : false;
const eventSource = (instance) => typeof instance === "object" && instance !== null ? refMap.get(instance) : void 0;

//#endregion
//#region src/core/utils.ts
const isEventData = (data) => data != null && typeof data === "object" && "data" in data;
const isPromiseLike = (value) => value != null && typeof value.then === "function";
function flattenEvents(acceptEventTypes, inputEventData) {
	const acceptance = new Array(acceptEventTypes.length);
	for (const eventData of inputEventData) for (let i$1 = 0; i$1 < acceptEventTypes.length; i$1++) {
		if (acceptance[i$1]) continue;
		if (acceptEventTypes[i$1].include(eventData)) {
			acceptance[i$1] = eventData;
			break;
		}
	}
	return acceptance.filter(Boolean);
}
const __internal__subscribesSourcemap = /* @__PURE__ */ new WeakMap();
/**
* @internal
*/
function getSubscribers(subscribable) {
	return __internal__subscribesSourcemap.get(subscribable);
}
/**
* @internal
*/
function createSubscribable() {
	const subscribers = /* @__PURE__ */ new Set();
	const obj = {
		subscribe: (callback) => {
			subscribers.add(callback);
			return () => {
				subscribers.delete(callback);
			};
		},
		publish: (...args) => {
			const results = [];
			for (const callback of subscribers) results.push(callback(...args));
			return results;
		}
	};
	__internal__subscribesSourcemap.set(obj, subscribers);
	return obj;
}

//#endregion
//#region src/async-context/index.browser.ts
const createAsyncContext = () => {
	let currentStore = null;
	return {
		getStore: () => {
			if (currentStore === null) console.warn("Woops! Looks like you are calling `getContext` after `await fn()`. Please move `getContext` to top level of handler.");
			return currentStore;
		},
		run(store, fn) {
			currentStore = store;
			try {
				return fn();
			} finally {
				currentStore = null;
			}
		}
	};
};

//#endregion
//#region src/core/stream.ts
var JsonEncodeTransform = class extends TransformStream {
	constructor() {
		super({ transform: (event, controller) => {
			if (eventSource(event)) controller.enqueue(JSON.stringify({
				data: event.data,
				uniqueId: eventSource(event).uniqueId
			}) + "\n");
		} });
	}
};
var JsonDecodeTransform = class extends TransformStream {
	#eventMap;
	constructor(eventMap$1) {
		super({ transform: (data, controller) => {
			const lines = data.split("\n").map((line) => line.trim()).filter((line) => line.length > 0);
			lines.forEach((line) => {
				const eventData = JSON.parse(line);
				const targetEvent = Object.values(this.#eventMap).find((e) => e.uniqueId === eventData.uniqueId);
				if (targetEvent) {
					const ev = targetEvent.with(eventData.data);
					controller.enqueue(ev);
				} else console.warn(`Unknown event: ${eventData.uniqueId}`);
			});
		} });
		this.#eventMap = eventMap$1;
	}
};
var WorkflowStream = class WorkflowStream extends ReadableStream {
	#stream;
	#subscribable;
	on(event, handler) {
		return this.#subscribable.subscribe((ev) => {
			if (event.include(ev)) handler(ev);
		});
	}
	constructor(subscribable, rootStream) {
		if (!subscribable && !rootStream) throw new TypeError("Either subscribable or root stream must be provided");
		super();
		if (!subscribable) {
			this.#subscribable = createSubscribable();
			this.#stream = rootStream.pipeThrough(new TransformStream({ transform: (ev, controller) => {
				this.#subscribable.publish(ev);
				controller.enqueue(ev);
			} }));
			return;
		} else {
			this.#subscribable = subscribable;
			let unsubscribe;
			this.#stream = rootStream ?? new ReadableStream({
				start: (controller) => {
					unsubscribe = subscribable.subscribe((event) => {
						controller.enqueue(event);
					});
				},
				cancel: () => {
					unsubscribe();
				}
			});
		}
	}
	static fromReadableStream(stream) {
		return new WorkflowStream(null, stream.pipeThrough(new TransformStream({ transform: (event, controller) => {
			controller.enqueue(event);
		} })));
	}
	static fromResponse(response, eventMap$1) {
		const body = response.body;
		if (!body) throw new Error("Response body is not readable");
		return new WorkflowStream(null, body.pipeThrough(new TextDecoderStream()).pipeThrough(new JsonDecodeTransform(eventMap$1)));
	}
	toResponse(init, transformer = new JsonEncodeTransform()) {
		return new Response(this.#stream.pipeThrough(transformer).pipeThrough(new TextEncoderStream()), init);
	}
	get locked() {
		return this.#stream.locked;
	}
	[Symbol.asyncIterator]() {
		return this.#stream[Symbol.asyncIterator]();
	}
	cancel(reason) {
		return this.#stream.cancel(reason);
	}
	getReader() {
		return this.#stream.getReader();
	}
	pipeThrough(transform, options) {
		const stream = this.#stream.pipeThrough(transform, options);
		return new WorkflowStream(null, stream);
	}
	pipeTo(destination, options) {
		return this.#stream.pipeTo(destination, options);
	}
	tee() {
		const [l, r] = this.#stream.tee();
		return [new WorkflowStream(this.#subscribable, l), new WorkflowStream(this.#subscribable, r)];
	}
	forEach(callback) {
		return this.#stream.pipeTo(new WritableStream({ write: (item) => {
			callback(item);
		} }));
	}
	map(callback) {
		return this.pipeThrough(new TransformStream({ transform: (item, controller) => {
			controller.enqueue(callback(item));
		} }));
	}
	values(options) {
		return this.#stream.values(options);
	}
	take(limit) {
		let count = 0;
		return this.pipeThrough(new TransformStream({ transform: (ev, controller) => {
			if (count < limit) {
				controller.enqueue(ev);
				count++;
			}
			if (count >= limit) controller.terminate();
		} }));
	}
	filter(predicate) {
		return this.pipeThrough(new TransformStream({ transform: (ev, controller) => {
			if (typeof predicate === "function" ? predicate(ev) : isWorkflowEvent(predicate) ? predicate.include(ev) : predicate === ev) controller.enqueue(ev);
		} }));
	}
	until(predicate) {
		return this.pipeThrough(new TransformStream({ transform: (ev, controller) => {
			controller.enqueue(ev);
			if (typeof predicate === "function" ? predicate(ev) : isWorkflowEvent(predicate) ? predicate.include(ev) : predicate === ev) controller.terminate();
		} }));
	}
	async toArray() {
		const events = [];
		await this.pipeTo(new WritableStream({ write: (event) => {
			events.push(event);
		} }));
		return events;
	}
};

//#endregion
//#region src/core/context.ts
const _executorAsyncLocalStorage = createAsyncContext();
function getContext() {
	const context = _executorAsyncLocalStorage.getStore();
	if (!context) throw new Error("No current context found");
	return context;
}
const handlerContextAsyncLocalStorage = createAsyncContext();
const eventContextWeakMap = /* @__PURE__ */ new WeakMap();
const createContext = ({ listeners }) => {
	const queue = [];
	const runHandler = (handler, inputEvents, inputs, parentContext) => {
		let handlerAbortController;
		const handlerContext = {
			get abortController() {
				if (!handlerAbortController) handlerAbortController = new AbortController();
				return handlerAbortController;
			},
			async: "constructor" in handler ? handler.constructor.name === "AsyncFunction" : false,
			pending: null,
			handler,
			inputEvents,
			inputs,
			outputs: [],
			prev: parentContext,
			next: /* @__PURE__ */ new Set(),
			get root() {
				return handlerRootContext;
			}
		};
		handlerContext.prev.next.add(handlerContext);
		const workflowContext = createWorkflowContext(handlerContext);
		handlerContextAsyncLocalStorage.run(handlerContext, () => {
			const cbs = [...new Set([...getSubscribers(rootWorkflowContext.__internal__call_context), ...getSubscribers(workflowContext.__internal__call_context)])];
			_executorAsyncLocalStorage.run(workflowContext, () => {
				let i$1 = 0;
				const next = (context) => {
					if (i$1 === cbs.length) {
						let result;
						try {
							result = context.handler(...context.inputs);
						} catch (error) {
							if (handlerAbortController ?? rootAbortController) (handlerAbortController ?? rootAbortController).abort(error);
							else {
								console.error("unhandled error in handler", error);
								throw error;
							}
						}
						if (isPromiseLike(result)) {
							handlerContext.async = true;
							handlerContext.pending = result.then((event) => {
								if (isEventData(event)) workflowContext.sendEvent(event);
								return event;
							});
						} else if (isEventData(result)) workflowContext.sendEvent(result);
					}
					const cb = cbs[i$1];
					if (cb) {
						i$1++;
						cb(context, next);
					}
				};
				next(handlerContext);
			});
		});
	};
	const queueUpdateCallback = (handlerContext) => {
		const queueSnapshot = [...queue];
		[...listeners].filter(([events]) => {
			const inputs = flattenEvents(events, queueSnapshot);
			return inputs.length === events.length;
		}).map(([events, handlers]) => {
			const inputs = flattenEvents(events, queueSnapshot);
			inputs.forEach((input) => {
				queue.splice(queue.indexOf(input), 1);
			});
			for (const handler of handlers) runHandler(handler, events, inputs, handlerContext);
		});
	};
	const createWorkflowContext = (handlerContext) => {
		let lazyLoadStream = null;
		return {
			get stream() {
				if (!lazyLoadStream) {
					const subscribable = createSubscribable();
					rootWorkflowContext.__internal__call_send_event.subscribe((newEvent) => {
						let currentEventContext = eventContextWeakMap.get(newEvent);
						while (currentEventContext) {
							if (currentEventContext === handlerContext) {
								subscribable.publish(newEvent);
								break;
							}
							currentEventContext = currentEventContext.prev;
						}
					});
					lazyLoadStream = new WorkflowStream(subscribable, null);
				}
				return lazyLoadStream;
			},
			get signal() {
				return handlerContext.abortController.signal;
			},
			sendEvent: (...events) => {
				events.forEach((event) => {
					eventContextWeakMap.set(event, handlerContext);
					handlerContext.outputs.push(event);
					queue.push(event);
					rootWorkflowContext.__internal__call_send_event.publish(event, handlerContext);
					queueUpdateCallback(handlerContext);
				});
			},
			__internal__call_context: createSubscribable(),
			__internal__call_send_event: createSubscribable()
		};
	};
	let rootAbortController = new AbortController();
	const handlerRootContext = {
		get abortController() {
			if (!rootAbortController) rootAbortController = new AbortController();
			return rootAbortController;
		},
		async: false,
		pending: null,
		inputEvents: [],
		inputs: [],
		outputs: [],
		handler: null,
		prev: null,
		next: /* @__PURE__ */ new Set(),
		get root() {
			return handlerRootContext;
		}
	};
	const rootWorkflowContext = createWorkflowContext(handlerRootContext);
	return rootWorkflowContext;
};

//#endregion
//#region src/core/workflow.ts
const createWorkflow = () => {
	const config = { steps: /* @__PURE__ */ new Map() };
	return {
		handle: (accept, handler) => {
			if (config.steps.has(accept)) {
				const set = config.steps.get(accept);
				set.add(handler);
			} else {
				const set = /* @__PURE__ */ new Set();
				set.add(handler);
				config.steps.set(accept, set);
			}
		},
		createContext() {
			return createContext({ listeners: config.steps });
		}
	};
};

//#endregion
export { WorkflowStream, createWorkflow, eventSource, getContext, isWorkflowEvent, isWorkflowEventData, workflowEvent };
//# sourceMappingURL=index.js.map