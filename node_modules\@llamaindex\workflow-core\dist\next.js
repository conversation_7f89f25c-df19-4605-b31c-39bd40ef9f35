import { run } from "./run-25vavBMH.js";
import { NextRequest } from "next/server";

//#region src/next.ts
const createNextHandler = (workflow, getStart, stop) => {
	return { GET: async (request) => {
		const result = await run(workflow, await getStart(request)).until(stop).toArray();
		return Response.json(result.at(-1).data);
	} };
};

//#endregion
export { createNextHandler };
//# sourceMappingURL=next.js.map