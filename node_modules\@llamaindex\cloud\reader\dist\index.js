import { FileReader, Document } from '@llamaindex/core/schema';
import { getEnv, fs, path } from '@llamaindex/env';
import { uploadFileApiV1ParsingUploadPost, getJobApiV1ParsingJobJobIdGet, getJobTextResultApiV1ParsingJobJobIdResultTextGet, getJobResultApiV1ParsingJobJobIdResultMarkdownGet, getJobJsonResultApiV1ParsingJobJobIdResultJsonGet, getJobImageResultApiV1ParsingJobJobIdResultImageNameGet } from '../../api/dist/index.js';

var A = async (t, r)=>{
    let e = typeof r == "function" ? await r(t) : r;
    if (e) return t.scheme === "bearer" ? `Bearer ${e}` : t.scheme === "basic" ? `Basic ${btoa(e)}` : e;
}, R = {
    bodySerializer: (t)=>JSON.stringify(t, (r, e)=>typeof e == "bigint" ? e.toString() : e)
}, U = (t)=>{
    switch(t){
        case "label":
            return ".";
        case "matrix":
            return ";";
        case "simple":
            return ",";
        default:
            return "&";
    }
}, _ = (t)=>{
    switch(t){
        case "form":
            return ",";
        case "pipeDelimited":
            return "|";
        case "spaceDelimited":
            return "%20";
        default:
            return ",";
    }
}, D = (t)=>{
    switch(t){
        case "label":
            return ".";
        case "matrix":
            return ";";
        case "simple":
            return ",";
        default:
            return "&";
    }
}, O = ({ allowReserved: t, explode: r, name: e, style: a, value: i })=>{
    if (!r) {
        let s = (t ? i : i.map((l)=>encodeURIComponent(l))).join(_(a));
        switch(a){
            case "label":
                return `.${s}`;
            case "matrix":
                return `;${e}=${s}`;
            case "simple":
                return s;
            default:
                return `${e}=${s}`;
        }
    }
    let o = U(a), n = i.map((s)=>a === "label" || a === "simple" ? t ? s : encodeURIComponent(s) : y({
            allowReserved: t,
            name: e,
            value: s
        })).join(o);
    return a === "label" || a === "matrix" ? o + n : n;
}, y = ({ allowReserved: t, name: r, value: e })=>{
    if (e == null) return "";
    if (typeof e == "object") throw new Error("Deeply-nested arrays/objects aren\u2019t supported. Provide your own `querySerializer()` to handle these.");
    return `${r}=${t ? e : encodeURIComponent(e)}`;
}, q = ({ allowReserved: t, explode: r, name: e, style: a, value: i })=>{
    if (i instanceof Date) return `${e}=${i.toISOString()}`;
    if (a !== "deepObject" && !r) {
        let s = [];
        Object.entries(i).forEach(([f, u])=>{
            s = [
                ...s,
                f,
                t ? u : encodeURIComponent(u)
            ];
        });
        let l = s.join(",");
        switch(a){
            case "form":
                return `${e}=${l}`;
            case "label":
                return `.${l}`;
            case "matrix":
                return `;${e}=${l}`;
            default:
                return l;
        }
    }
    let o = D(a), n = Object.entries(i).map(([s, l])=>y({
            allowReserved: t,
            name: a === "deepObject" ? `${e}[${s}]` : s,
            value: l
        })).join(o);
    return a === "label" || a === "matrix" ? o + n : n;
};
var H = /\{[^{}]+\}/g, B = ({ path: t, url: r })=>{
    let e = r, a = r.match(H);
    if (a) for (let i of a){
        let o = false, n = i.substring(1, i.length - 1), s = "simple";
        n.endsWith("*") && (o = true, n = n.substring(0, n.length - 1)), n.startsWith(".") ? (n = n.substring(1), s = "label") : n.startsWith(";") && (n = n.substring(1), s = "matrix");
        let l = t[n];
        if (l == null) continue;
        if (Array.isArray(l)) {
            e = e.replace(i, O({
                explode: o,
                name: n,
                style: s,
                value: l
            }));
            continue;
        }
        if (typeof l == "object") {
            e = e.replace(i, q({
                explode: o,
                name: n,
                style: s,
                value: l
            }));
            continue;
        }
        if (s === "matrix") {
            e = e.replace(i, `;${y({
                name: n,
                value: l
            })}`);
            continue;
        }
        let f = encodeURIComponent(s === "label" ? `.${l}` : l);
        e = e.replace(i, f);
    }
    return e;
}, P = ({ allowReserved: t, array: r, object: e } = {})=>(i)=>{
        let o = [];
        if (i && typeof i == "object") for(let n in i){
            let s = i[n];
            if (s != null) if (Array.isArray(s)) {
                let l = O({
                    allowReserved: t,
                    explode: true,
                    name: n,
                    style: "form",
                    value: s,
                    ...r
                });
                l && o.push(l);
            } else if (typeof s == "object") {
                let l = q({
                    allowReserved: t,
                    explode: true,
                    name: n,
                    style: "deepObject",
                    value: s,
                    ...e
                });
                l && o.push(l);
            } else {
                let l = y({
                    allowReserved: t,
                    name: n,
                    value: s
                });
                l && o.push(l);
            }
        }
        return o.join("&");
    }, E = (t)=>{
    if (!t) return "stream";
    let r = t.split(";")[0]?.trim();
    if (r) {
        if (r.startsWith("application/json") || r.endsWith("+json")) return "json";
        if (r === "multipart/form-data") return "formData";
        if ([
            "application/",
            "audio/",
            "image/",
            "video/"
        ].some((e)=>r.startsWith(e))) return "blob";
        if (r.startsWith("text/")) return "text";
    }
}, I = async ({ security: t, ...r })=>{
    for (let e of t){
        let a = await A(e, r.auth);
        if (!a) continue;
        let i = e.name ?? "Authorization";
        switch(e.in){
            case "query":
                r.query || (r.query = {}), r.query[i] = a;
                break;
            case "cookie":
                r.headers.append("Cookie", `${i}=${a}`);
                break;
            case "header":
            default:
                r.headers.set(i, a);
                break;
        }
        return;
    }
}, S = (t)=>W({
        baseUrl: t.baseUrl,
        path: t.path,
        query: t.query,
        querySerializer: typeof t.querySerializer == "function" ? t.querySerializer : P(t.querySerializer),
        url: t.url
    }), W = ({ baseUrl: t, path: r, query: e, querySerializer: a, url: i })=>{
    let o = i.startsWith("/") ? i : `/${i}`, n = (t ?? "") + o;
    r && (n = B({
        path: r,
        url: n
    }));
    let s = e ? a(e) : "";
    return s.startsWith("?") && (s = s.substring(1)), s && (n += `?${s}`), n;
}, C = (t, r)=>{
    let e = {
        ...t,
        ...r
    };
    return e.baseUrl?.endsWith("/") && (e.baseUrl = e.baseUrl.substring(0, e.baseUrl.length - 1)), e.headers = x(t.headers, r.headers), e;
}, x = (...t)=>{
    let r = new Headers;
    for (let e of t){
        if (!e || typeof e != "object") continue;
        let a = e instanceof Headers ? e.entries() : Object.entries(e);
        for (let [i, o] of a)if (o === null) r.delete(i);
        else if (Array.isArray(o)) for (let n of o)r.append(i, n);
        else o !== void 0 && r.set(i, typeof o == "object" ? JSON.stringify(o) : o);
    }
    return r;
}, h = class {
    constructor(){
        this._fns = [];
    }
    clear() {
        this._fns = [];
    }
    exists(r) {
        return this._fns.indexOf(r) !== -1;
    }
    eject(r) {
        let e = this._fns.indexOf(r);
        e !== -1 && (this._fns = [
            ...this._fns.slice(0, e),
            ...this._fns.slice(e + 1)
        ]);
    }
    use(r) {
        this._fns = [
            ...this._fns,
            r
        ];
    }
}, v = ()=>({
        error: new h,
        request: new h,
        response: new h
    }), N = P({
    allowReserved: false,
    array: {
        explode: true,
        style: "form"
    },
    object: {
        explode: true,
        style: "deepObject"
    }
}), Q = {
    "Content-Type": "application/json"
}, w = (t = {})=>({
        ...R,
        headers: Q,
        parseAs: "auto",
        querySerializer: N,
        ...t
    });
var J = (t = {})=>{
    let r = C(w(), t), e = ()=>({
            ...r
        }), a = (n)=>(r = C(r, n), e()), i = v(), o = async (n)=>{
        let s = {
            ...r,
            ...n,
            fetch: n.fetch ?? r.fetch ?? globalThis.fetch,
            headers: x(r.headers, n.headers)
        };
        s.security && await I({
            ...s,
            security: s.security
        }), s.body && s.bodySerializer && (s.body = s.bodySerializer(s.body)), (s.body === void 0 || s.body === "") && s.headers.delete("Content-Type");
        let l = S(s), f = {
            redirect: "follow",
            ...s
        }, u = new Request(l, f);
        for (let p of i.request._fns)u = await p(u, s);
        let T = s.fetch, c = await T(u);
        for (let p of i.response._fns)c = await p(c, u, s);
        let m = {
            request: u,
            response: c
        };
        if (c.ok) {
            if (c.status === 204 || c.headers.get("Content-Length") === "0") return {
                data: {},
                ...m
            };
            let p = (s.parseAs === "auto" ? E(c.headers.get("Content-Type")) : s.parseAs) ?? "json";
            if (p === "stream") return {
                data: c.body,
                ...m
            };
            let b = await c[p]();
            return p === "json" && (s.responseValidator && await s.responseValidator(b), s.responseTransformer && (b = await s.responseTransformer(b))), {
                data: b,
                ...m
            };
        }
        let g = await c.text();
        try {
            g = JSON.parse(g);
        } catch  {}
        let d = g;
        for (let p of i.error._fns)d = await p(g, c, u, s);
        if (d = d || {}, s.throwOnError) throw d;
        return {
            error: d,
            ...m
        };
    };
    return {
        buildUrl: S,
        connect: (n)=>o({
                ...n,
                method: "CONNECT"
            }),
        delete: (n)=>o({
                ...n,
                method: "DELETE"
            }),
        get: (n)=>o({
                ...n,
                method: "GET"
            }),
        getConfig: e,
        head: (n)=>o({
                ...n,
                method: "HEAD"
            }),
        interceptors: i,
        options: (n)=>o({
                ...n,
                method: "OPTIONS"
            }),
        patch: (n)=>o({
                ...n,
                method: "PATCH"
            }),
        post: (n)=>o({
                ...n,
                method: "POST"
            }),
        put: (n)=>o({
                ...n,
                method: "PUT"
            }),
        request: o,
        setConfig: a,
        trace: (n)=>o({
                ...n,
                method: "TRACE"
            })
    };
};

async function sleep(ms) {
    return new Promise((resolve)=>setTimeout(resolve, ms));
}

// Do not modify this variable or cause type errors
// eslint-disable-next-line no-var
var process;
/**
 * Represents a reader for parsing files using the LlamaParse API.
 * See https://github.com/run-llama/llama_parse
 */ class LlamaParseReader extends FileReader {
    #client;
    constructor(params = {}){
        super(), // The base URL of the Llama Cloud Platform.
        this.baseUrl = "https://api.cloud.llamaindex.ai", // The result type for the parser.
        this.resultType = "text", // The interval in seconds to check if the parsing is done.
        this.checkInterval = 1, // The maximum timeout in seconds to wait for the parsing to finish.
        this.maxTimeout = 2000, // Whether to print the progress of the parsing.
        this.verbose = true, // The language to parse the file in.
        this.language = [
            "en"
        ], // New polling options:
        // Controls the backoff mode: "constant", "linear", or "exponential".
        this.backoffPattern = "linear", // Maximum interval in seconds between polls.
        this.maxCheckInterval = 5, // Maximum number of retryable errors before giving up.
        this.maxErrorCount = 4, // Deprecated. Use vendorMultimodal params. Whether to use gpt-4o to extract text.
        this.gpt4oMode = false, // Whether to ignore errors during parsing.
        this.ignoreErrors = true, // Whether to split by page using the pageSeparator (or "\n---\n" as default).
        this.splitByPage = true, // Whether to use the vendor multimodal API.
        this.useVendorMultimodalModel = false, this.output_tables_as_HTML = false;
        Object.assign(this, params);
        this.language = Array.isArray(this.language) ? this.language : [
            this.language
        ];
        this.stdout = params.stdout ?? "undefined" !== "undefined" ? process.stdout : undefined;
        const apiKey = params.apiKey ?? getEnv("LLAMA_CLOUD_API_KEY");
        if (!apiKey) {
            throw new Error("API Key is required for LlamaParseReader. Please pass the apiKey parameter or set the LLAMA_CLOUD_API_KEY environment variable.");
        }
        this.apiKey = apiKey;
        if (this.baseUrl.endsWith("/")) {
            this.baseUrl = this.baseUrl.slice(0, -1);
        }
        if (this.baseUrl.endsWith("/api/parsing")) {
            this.baseUrl = this.baseUrl.slice(0, -"/api/parsing".length);
        }
        if (params.gpt4oMode) {
            params.gpt4oApiKey = params.gpt4oApiKey ?? getEnv("LLAMA_CLOUD_GPT4O_API_KEY");
            this.gpt4oApiKey = params.gpt4oApiKey;
        }
        if (params.useVendorMultimodalModel) {
            params.vendorMultimodalApiKey = params.vendorMultimodalApiKey ?? getEnv("LLAMA_CLOUD_VENDOR_MULTIMODAL_API_KEY");
            this.vendorMultimodalApiKey = params.vendorMultimodalApiKey;
        }
        this.#client = J(w({
            headers: {
                Authorization: `Bearer ${this.apiKey}`
            },
            baseUrl: this.baseUrl
        }));
    }
    /**
   * Creates a job for the LlamaParse API.
   *
   * @param data - The file data as a Uint8Array.
   * @param filename - Optional filename for the file.
   * @returns A Promise resolving to the job ID as a string.
   */ async #createJob(data, filename) {
        if (this.verbose) {
            console.log("Started uploading the file");
        }
        let file = null;
        let input_s3_path = this.inputS3Path;
        let input_url = this.input_url;
        if (typeof data !== "string") {
            // TODO: remove Blob usage when we drop Node.js 18 support
            file = globalThis.File && filename ? new File([
                data
            ], filename) : new Blob([
                data
            ]);
        } else if (data.startsWith("s3://")) {
            input_s3_path = data;
        } else if (data.startsWith("http://") || data.startsWith("https://")) {
            input_url = data;
        }
        const body = {
            file,
            input_s3_path,
            input_url,
            language: this.language,
            parsing_instruction: this.parsingInstruction,
            skip_diagonal_text: this.skipDiagonalText,
            invalidate_cache: this.invalidateCache,
            do_not_cache: this.doNotCache,
            fast_mode: this.fastMode,
            do_not_unroll_columns: this.doNotUnrollColumns,
            page_separator: this.pageSeparator,
            page_prefix: this.pagePrefix,
            page_suffix: this.pageSuffix,
            gpt4o_mode: this.gpt4oMode,
            gpt4o_api_key: this.gpt4oApiKey,
            bounding_box: this.boundingBox,
            target_pages: this.targetPages,
            use_vendor_multimodal_model: this.useVendorMultimodalModel,
            vendor_multimodal_model_name: this.vendorMultimodalModelName,
            vendor_multimodal_api_key: this.vendorMultimodalApiKey,
            premium_mode: this.premiumMode,
            webhook_url: this.webhookUrl,
            take_screenshot: this.takeScreenshot,
            disable_ocr: this.disableOcr,
            disable_reconstruction: this.disableReconstruction,
            output_s3_path_prefix: this.outputS3PathPrefix,
            continuous_mode: this.continuousMode,
            is_formatting_instruction: this.isFormattingInstruction,
            annotate_links: this.annotateLinks,
            azure_openai_deployment_name: this.azureOpenaiDeploymentName,
            azure_openai_endpoint: this.azureOpenaiEndpoint,
            azure_openai_api_version: this.azureOpenaiApiVersion,
            azure_openai_key: this.azureOpenaiKey,
            auto_mode: this.auto_mode,
            auto_mode_trigger_on_image_in_page: this.auto_mode_trigger_on_image_in_page,
            auto_mode_trigger_on_table_in_page: this.auto_mode_trigger_on_table_in_page,
            auto_mode_trigger_on_text_in_page: this.auto_mode_trigger_on_text_in_page,
            auto_mode_trigger_on_regexp_in_page: this.auto_mode_trigger_on_regexp_in_page,
            bbox_bottom: this.bbox_bottom,
            bbox_left: this.bbox_left,
            bbox_right: this.bbox_right,
            bbox_top: this.bbox_top,
            disable_image_extraction: this.disable_image_extraction,
            extract_charts: this.extract_charts,
            guess_xlsx_sheet_name: this.guess_xlsx_sheet_name,
            html_make_all_elements_visible: this.html_make_all_elements_visible,
            html_remove_fixed_elements: this.html_remove_fixed_elements,
            html_remove_navigation_elements: this.html_remove_navigation_elements,
            http_proxy: this.http_proxy,
            max_pages: this.max_pages,
            output_pdf_of_document: this.output_pdf_of_document,
            structured_output: this.structured_output,
            structured_output_json_schema: this.structured_output_json_schema,
            structured_output_json_schema_name: this.structured_output_json_schema_name,
            extract_layout: this.extract_layout,
            output_tables_as_HTML: this.output_tables_as_HTML,
            input_s3_region: this.input_s3_region,
            output_s3_region: this.output_s3_region,
            preserve_layout_alignment_across_pages: this.preserve_layout_alignment_across_pages,
            spreadsheet_extract_sub_tables: this.spreadsheet_extract_sub_tables,
            formatting_instruction: this.formatting_instruction,
            parse_mode: this.parse_mode,
            system_prompt: this.system_prompt,
            system_prompt_append: this.system_prompt_append,
            user_prompt: this.user_prompt,
            job_timeout_in_seconds: this.job_timeout_in_seconds,
            job_timeout_extra_time_per_page_in_seconds: this.job_timeout_extra_time_per_page_in_seconds,
            strict_mode_image_extraction: this.strict_mode_image_extraction,
            strict_mode_image_ocr: this.strict_mode_image_ocr,
            strict_mode_reconstruction: this.strict_mode_reconstruction,
            strict_mode_buggy_font: this.strict_mode_buggy_font,
            ignore_document_elements_for_layout_detection: this.ignore_document_elements_for_layout_detection,
            complemental_formatting_instruction: this.complemental_formatting_instruction,
            content_guideline_instruction: this.content_guideline_instruction,
            adaptive_long_table: this.adaptive_long_table,
            model: this.model,
            auto_mode_configuration_json: this.auto_mode_configuration_json,
            compact_markdown_table: this.compact_markdown_table,
            markdown_table_multiline_header_separator: this.markdown_table_multiline_header_separator,
            page_error_tolerance: this.page_error_tolerance,
            replace_failed_page_mode: this.replace_failed_page_mode,
            replace_failed_page_with_error_message_prefix: this.replace_failed_page_with_error_message_prefix,
            replace_failed_page_with_error_message_suffix: this.replace_failed_page_with_error_message_suffix,
            save_images: this.save_images,
            preset: this.preset,
            high_res_ocr: this.high_res_ocr,
            outlined_table_extraction: this.outlined_table_extraction,
            hide_headers: this.hide_headers,
            hide_footers: this.hide_footers,
            page_header_prefix: this.page_header_prefix,
            page_header_suffix: this.page_header_suffix,
            page_footer_prefix: this.page_footer_prefix,
            page_footer_suffix: this.page_footer_suffix
        };
        const response = await uploadFileApiV1ParsingUploadPost({
            client: this.#client,
            throwOnError: true,
            query: {
                project_id: this.project_id ?? null,
                organization_id: this.organization_id ?? null
            },
            signal: AbortSignal.timeout(this.maxTimeout * 1000),
            body
        });
        return response.data.id;
    }
    /**
   * Retrieves the result of a parsing job.
   *
   * Uses a polling loop with retry logic. Each API call is retried
   * up to maxErrorCount times if it fails with a 5XX or socket error.
   * The delay between polls increases according to the specified backoffPattern ("constant", "linear", or "exponential"),
   * capped by maxCheckInterval.
   *
   * @param jobId - The job ID.
   * @param resultType - The type of result to fetch ("text", "json", or "markdown").
   * @returns A Promise resolving to the job result.
   */ async getJobResult(jobId, resultType) {
        let tries = 0;
        let currentInterval = this.checkInterval;
        const { default: pRetry } = await import('p-retry');
        while(true){
            await sleep(currentInterval * 1000);
            // Wraps the API call in a retry
            let result;
            try {
                result = await pRetry(()=>getJobApiV1ParsingJobJobIdGet({
                        client: this.#client,
                        throwOnError: true,
                        path: {
                            job_id: jobId
                        },
                        signal: AbortSignal.timeout(this.maxTimeout * 1000)
                    }), {
                    retries: this.maxErrorCount,
                    onFailedAttempt: (error)=>{
                        // Retry only on 5XX or socket errors.
                        const status = error.cause?.response?.status;
                        if (!(status && status >= 500 && status < 600 || error.cause?.code && (error.cause.code === "ECONNRESET" || error.cause.code === "ETIMEDOUT" || error.cause.code === "ECONNREFUSED"))) {
                            throw error;
                        }
                        if (this.verbose) {
                            console.warn(`Attempting to get job ${jobId} result (attempt ${error.attemptNumber}) failed. Retrying...`);
                        }
                    }
                });
            } catch (e) {
                throw new Error(`Max error count reached for job ${jobId}: ${e.message}`);
            }
            const { data } = result;
            const status = data["status"];
            if (status === "SUCCESS") {
                let resultData;
                switch(resultType){
                    case "json":
                        {
                            resultData = await getJobJsonResultApiV1ParsingJobJobIdResultJsonGet({
                                client: this.#client,
                                throwOnError: true,
                                path: {
                                    job_id: jobId
                                },
                                query: {
                                    organization_id: this.organization_id ?? null
                                },
                                signal: AbortSignal.timeout(this.maxTimeout * 1000)
                            });
                            break;
                        }
                    case "markdown":
                        {
                            resultData = await getJobResultApiV1ParsingJobJobIdResultMarkdownGet({
                                client: this.#client,
                                throwOnError: true,
                                path: {
                                    job_id: jobId
                                },
                                query: {
                                    organization_id: this.organization_id ?? null
                                },
                                signal: AbortSignal.timeout(this.maxTimeout * 1000)
                            });
                            break;
                        }
                    case "text":
                        {
                            resultData = await getJobTextResultApiV1ParsingJobJobIdResultTextGet({
                                client: this.#client,
                                throwOnError: true,
                                path: {
                                    job_id: jobId
                                },
                                query: {
                                    organization_id: this.organization_id ?? null
                                },
                                signal: AbortSignal.timeout(this.maxTimeout * 1000)
                            });
                            break;
                        }
                }
                return resultData.data;
            } else if (status === "PENDING") {
                if (this.verbose && tries % 10 === 0) {
                    this.stdout?.write(".");
                }
                tries++;
            } else {
                if (this.verbose) {
                    console.error(`Received error response ${status} for job ${jobId}. Got Error Code: ${data.error_code} and Error Message: ${data.error_message}`);
                }
                throw new Error(`Failed to parse the file: ${jobId}, status: ${status}`);
            }
            // Adjust the polling interval based on the backoff pattern.
            if (this.backoffPattern === "exponential") {
                currentInterval = Math.min(currentInterval * 2, this.maxCheckInterval);
            } else if (this.backoffPattern === "linear") {
                currentInterval = Math.min(currentInterval + this.checkInterval, this.maxCheckInterval);
            } else if (this.backoffPattern === "constant") {
                currentInterval = this.checkInterval;
            }
        }
    }
    async loadData(filePath) {
        if (!filePath) {
            if (this.input_url) {
                return this.loadDataAsContent(this.input_url, this.input_url);
            } else if (this.inputS3Path) {
                return this.loadDataAsContent(this.inputS3Path, this.inputS3Path);
            } else {
                throw new TypeError("File path is required");
            }
        } else {
            const data = filePath.startsWith("s3://") || filePath.startsWith("http://") || filePath.startsWith("https://") ? filePath : await fs.readFile(filePath);
            return this.loadDataAsContent(data, filePath);
        }
    }
    /**
   * Loads data from a file and returns an array of Document objects.
   * To be used with resultType "text" or "markdown".
   *
   * @param fileContent - The content of the file as a Uint8Array.
   * @param filename - Optional filename for the file.
   * @returns A Promise that resolves to an array of Document objects.
   */ async loadDataAsContent(fileContent, filename) {
        return this.#createJob(fileContent, filename).then(async (jobId)=>{
            if (this.verbose) {
                console.log(`Started parsing the file under job id ${jobId}`);
            }
            // Return results as Document objects.
            const jobResults = await this.getJobResult(jobId, this.resultType);
            const resultText = jobResults[this.resultType];
            // Split the text by separator if splitByPage is true.
            if (this.splitByPage) {
                return this.splitTextBySeparator(resultText);
            }
            return [
                new Document({
                    text: resultText
                })
            ];
        }).catch((error)=>{
            console.warn(`Error while parsing the file with: ${error.message ?? error.detail}`);
            if (this.ignoreErrors) {
                return [];
            } else {
                throw error;
            }
        });
    }
    /**
   * Loads data from a file and returns an array of JSON objects.
   * To be used with resultType "json".
   *
   * @param filePathOrContent - The file path or the file content as a Uint8Array.
   * @returns A Promise that resolves to an array of JSON objects.
   */ async loadJson(filePathOrContent) {
        let jobId;
        const isFilePath = typeof filePathOrContent === "string" && !(filePathOrContent.startsWith("s3://") || filePathOrContent.startsWith("http://") || filePathOrContent.startsWith("https://"));
        try {
            const data = isFilePath ? await fs.readFile(filePathOrContent) : filePathOrContent;
            // Create a job for the file.
            jobId = await this.#createJob(data, isFilePath ? path.basename(filePathOrContent) : undefined);
            if (this.verbose) {
                console.log(`Started parsing the file under job id ${jobId}`);
            }
            // Return results as an array of JSON objects.
            const resultJson = await this.getJobResult(jobId, "json");
            resultJson.job_id = jobId;
            resultJson.file_path = isFilePath ? filePathOrContent : undefined;
            return [
                resultJson
            ];
        } catch (e) {
            console.error(`Error while parsing the file under job id ${jobId}`, e);
            if (this.ignoreErrors) {
                return [];
            } else {
                throw e;
            }
        }
    }
    /**
   * Downloads and saves images from a given JSON result to a specified download path.
   * Currently only supports resultType "json".
   *
   * @param jsonResult - The JSON result containing image information.
   * @param downloadPath - The path where the downloaded images will be saved.
   * @returns A Promise that resolves to an array of image objects.
   */ async getImages(jsonResult, downloadPath) {
        try {
            // Create download directory if it doesn't exist (checks for write access).
            try {
                await fs.access(downloadPath);
            } catch  {
                await fs.mkdir(downloadPath, {
                    recursive: true
                });
            }
            const images = [];
            for (const result of jsonResult){
                const jobId = result.job_id;
                for (const page of result.pages){
                    if (this.verbose) {
                        console.log(`> Image for page ${page.page}: ${page.images}`);
                    }
                    for (const image of page.images){
                        const imageName = image.name;
                        const imagePath = await this.getImagePath(downloadPath, jobId, imageName);
                        await this.fetchAndSaveImage(imageName, imagePath, jobId);
                        // Assign metadata to the image.
                        image.path = imagePath;
                        image.job_id = jobId;
                        image.original_pdf_path = result.file_path;
                        image.page_number = page.page;
                        images.push(image);
                    }
                }
            }
            return images;
        } catch (e) {
            console.error(`Error while downloading images from the parsed result`, e);
            if (this.ignoreErrors) {
                return [];
            } else {
                throw e;
            }
        }
    }
    /**
   * Constructs the file path for an image.
   *
   * @param downloadPath - The base download directory.
   * @param jobId - The job ID.
   * @param imageName - The image name.
   * @returns A Promise that resolves to the full image path.
   */ async getImagePath(downloadPath, jobId, imageName) {
        return path.join(downloadPath, `${jobId}-${imageName}`);
    }
    /**
   * Fetches an image from the API and saves it to the specified path.
   *
   * @param imageName - The name of the image.
   * @param imagePath - The local path to save the image.
   * @param jobId - The associated job ID.
   */ async fetchAndSaveImage(imageName, imagePath, jobId) {
        const response = await getJobImageResultApiV1ParsingJobJobIdResultImageNameGet({
            client: this.#client,
            path: {
                job_id: jobId,
                name: imageName
            }
        });
        if (response.error) {
            throw new Error(`Failed to download image: ${response.error.detail}`);
        }
        const blob = await response.data;
        // Write the image buffer to the specified imagePath.
        await fs.writeFile(imagePath, new Uint8Array(await blob.arrayBuffer()));
    }
    /**
   * Filters out invalid values (null, undefined, empty string) for specific parameters.
   *
   * @param params - The parameters object.
   * @param keysToCheck - The keys to check for valid values.
   * @returns A new object with filtered parameters.
   */ filterSpecificParams(params, keysToCheck) {
        const filteredParams = {};
        for (const [key, value] of Object.entries(params)){
            if (keysToCheck.includes(key)) {
                if (value !== null && value !== undefined && value !== "") {
                    filteredParams[key] = value;
                }
            } else {
                filteredParams[key] = value;
            }
        }
        return filteredParams;
    }
    /**
   * Splits text into Document objects using the page separator.
   *
   * @param text - The text to be split.
   * @returns An array of Document objects.
   */ splitTextBySeparator(text) {
        const separator = this.pageSeparator ?? "\n---\n";
        const textChunks = text.split(separator);
        return textChunks.map((docChunk)=>new Document({
                text: docChunk
            }));
    }
}

export { LlamaParseReader };
