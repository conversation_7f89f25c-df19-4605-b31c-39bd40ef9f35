"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/p-retry";
exports.ids = ["vendor-chunks/p-retry"];
exports.modules = {

/***/ "(rsc)/./node_modules/p-retry/index.js":
/*!***************************************!*\
  !*** ./node_modules/p-retry/index.js ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortError: () => (/* binding */ AbortError),\n/* harmony export */   \"default\": () => (/* binding */ pRetry)\n/* harmony export */ });\n/* harmony import */ var retry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! retry */ \"(rsc)/./node_modules/retry/index.js\");\n/* harmony import */ var is_network_error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! is-network-error */ \"(rsc)/./node_modules/is-network-error/index.js\");\n\n\n\nclass AbortError extends Error {\n\tconstructor(message) {\n\t\tsuper();\n\n\t\tif (message instanceof Error) {\n\t\t\tthis.originalError = message;\n\t\t\t({message} = message);\n\t\t} else {\n\t\t\tthis.originalError = new Error(message);\n\t\t\tthis.originalError.stack = this.stack;\n\t\t}\n\n\t\tthis.name = 'AbortError';\n\t\tthis.message = message;\n\t}\n}\n\nconst decorateErrorWithCounts = (error, attemptNumber, options) => {\n\t// Minus 1 from attemptNumber because the first attempt does not count as a retry\n\tconst retriesLeft = options.retries - (attemptNumber - 1);\n\n\terror.attemptNumber = attemptNumber;\n\terror.retriesLeft = retriesLeft;\n\treturn error;\n};\n\nasync function pRetry(input, options) {\n\treturn new Promise((resolve, reject) => {\n\t\toptions = {...options};\n\t\toptions.onFailedAttempt ??= () => {};\n\t\toptions.shouldRetry ??= () => true;\n\t\toptions.retries ??= 10;\n\n\t\tconst operation = retry__WEBPACK_IMPORTED_MODULE_0__.operation(options);\n\n\t\tconst abortHandler = () => {\n\t\t\toperation.stop();\n\t\t\treject(options.signal?.reason);\n\t\t};\n\n\t\tif (options.signal && !options.signal.aborted) {\n\t\t\toptions.signal.addEventListener('abort', abortHandler, {once: true});\n\t\t}\n\n\t\tconst cleanUp = () => {\n\t\t\toptions.signal?.removeEventListener('abort', abortHandler);\n\t\t\toperation.stop();\n\t\t};\n\n\t\toperation.attempt(async attemptNumber => {\n\t\t\ttry {\n\t\t\t\tconst result = await input(attemptNumber);\n\t\t\t\tcleanUp();\n\t\t\t\tresolve(result);\n\t\t\t} catch (error) {\n\t\t\t\ttry {\n\t\t\t\t\tif (!(error instanceof Error)) {\n\t\t\t\t\t\tthrow new TypeError(`Non-error was thrown: \"${error}\". You should only throw errors.`);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (error instanceof AbortError) {\n\t\t\t\t\t\tthrow error.originalError;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (error instanceof TypeError && !(0,is_network_error__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(error)) {\n\t\t\t\t\t\tthrow error;\n\t\t\t\t\t}\n\n\t\t\t\t\tdecorateErrorWithCounts(error, attemptNumber, options);\n\n\t\t\t\t\tif (!(await options.shouldRetry(error))) {\n\t\t\t\t\t\toperation.stop();\n\t\t\t\t\t\treject(error);\n\t\t\t\t\t}\n\n\t\t\t\t\tawait options.onFailedAttempt(error);\n\n\t\t\t\t\tif (!operation.retry(error)) {\n\t\t\t\t\t\tthrow operation.mainError();\n\t\t\t\t\t}\n\t\t\t\t} catch (finalError) {\n\t\t\t\t\tdecorateErrorWithCounts(finalError, attemptNumber, options);\n\t\t\t\t\tcleanUp();\n\t\t\t\t\treject(finalError);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t});\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/p-retry/index.js\n");

/***/ })

};
;