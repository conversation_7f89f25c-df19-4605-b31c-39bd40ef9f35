import { WorkflowStream } from "@llamaindex/workflow-core";

//#region src/stream/until.ts
const isWorkflowEvent = (value) => value != null && typeof value === "object" && "with" in value && "include" in value;
function until(stream, cond) {
	let reader = null;
	return WorkflowStream.fromReadableStream(new ReadableStream({
		start: () => {
			reader = stream.getReader();
		},
		pull: async (controller) => {
			const { done, value } = await reader.read();
			if (value) controller.enqueue(value);
			if (done) {
				reader.releaseLock();
				reader = null;
				controller.close();
			} else if (isWorkflowEvent(cond) && cond.include(value)) {
				reader.releaseLock();
				controller.close();
			} else if (typeof cond === "function" && await cond(value)) {
				reader.releaseLock();
				controller.close();
			}
		}
	}));
}

//#endregion
export { until };
//# sourceMappingURL=until.js.map