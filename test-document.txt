ChatDoc Application Documentation

Overview:
ChatDoc is a modern web application that allows users to upload text and markdown documents and then ask questions about their content using AI-powered natural language processing.

Key Features:
1. Document Upload: Users can upload .txt and .md files through a drag-and-drop interface
2. AI-Powered Chat: Ask questions about uploaded documents using natural language
3. Source Attribution: All responses include references to the source documents and relevant text chunks
4. Real-time Processing: Documents are automatically indexed and made searchable upon upload
5. Modern UI: Built with React, Next.js, and Tailwind CSS for a responsive experience

Technical Architecture:
- Frontend: Next.js 14 with React 18 and TypeScript
- Backend: Next.js API routes with LlamaIndex integration
- AI/ML: LlamaIndex for document processing and retrieval-augmented generation (RAG)
- Styling: Tailwind CSS with custom design system
- File Storage: Local file system with metadata tracking

How It Works:
1. Users upload documents through the web interface
2. Documents are stored locally and processed by LlamaIndex
3. Text is chunked and vectorized for efficient retrieval
4. When users ask questions, the system searches for relevant document chunks
5. AI generates responses based on the retrieved context with proper source attribution

This application demonstrates the power of combining modern web technologies with advanced AI capabilities to create intuitive document interaction experiences.
