{"version": 3, "file": "mcp.js", "names": ["workflow: Workflow", "start: WorkflowEvent<Start>", "stop: WorkflowEvent<Stop>"], "sources": ["../src/mcp.ts"], "sourcesContent": ["import { createAsyncContext } from \"@llamaindex/workflow-core/async-context\";\nimport { z, type ZodRawShape, type Zod<PERSON>ype<PERSON>ny } from \"zod\";\nimport type { Workflow, WorkflowEvent } from \"@llamaindex/workflow-core\";\nimport { runWorkflow } from \"./stream/run\";\nimport type { RequestHandlerExtra } from \"@modelcontextprotocol/sdk/shared/protocol.js\";\nimport type { CallToolResult } from \"@modelcontextprotocol/sdk/types.js\";\n\nconst requestHandlerExtraAsyncLocalStorage =\n  createAsyncContext<RequestHandlerExtra<any, any>>();\n\nexport const getReqHandlerExtra = () => {\n  const extra = requestHandlerExtraAsyncLocalStorage.getStore();\n  if (!extra) {\n    throw new Error(\"Request handler extra not set\");\n  }\n  return extra;\n};\n\nexport function mcpTool<\n  Args extends ZodRawShape,\n  Start extends z.objectOutputType<Args, ZodTypeAny>,\n  Stop extends CallToolResult,\n>(\n  workflow: Workflow,\n  start: WorkflowEvent<Start>,\n  stop: WorkflowEvent<Stop>,\n): (\n  args: Start,\n  extra: RequestHandlerExtra<any, any>,\n) => CallToolResult | Promise<CallToolResult> {\n  return async (args, extra) =>\n    requestHandlerExtraAsyncLocalStorage.run(extra, async () => {\n      const { data } = await runWorkflow(workflow, start.with(args), stop);\n      return data;\n    });\n}\n"], "mappings": ";;;;;AAOA,MAAM,uCACJ,oBAAmD;AAErD,MAAa,qBAAqB,MAAM;CACtC,MAAM,QAAQ,qCAAqC,UAAU;AAC7D,MAAK,MACH,OAAM,IAAI,MAAM;AAElB,QAAO;AACR;AAED,SAAgB,QAKdA,UACAC,OACAC,MAI4C;AAC5C,QAAO,OAAO,MAAM,UAClB,qCAAqC,IAAI,OAAO,YAAY;EAC1D,MAAM,EAAE,MAAM,GAAG,MAAM,YAAY,UAAU,MAAM,KAAK,KAAK,EAAE,KAAK;AACpE,SAAO;CACR,EAAC;AACL"}